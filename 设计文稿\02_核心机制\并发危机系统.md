# 并发危机系统设计

**文档类型**: 核心机制设计
**版本号**: v2.0
**创建日期**: 2024-12-18
**最后更新**: 2025-01-05
**维护者**: 核心机制设计团队
**状态**: 已发布

---

## 📖 文档概述

本文档详细描述月球RPG的核心叙事机制——并发危机系统。这是游戏最重要的创新机制，摒弃传统线性剧情，采用"中心辐射"模型，让玩家在多个同时发生的危机中自由选择应对顺序。

## 🎯 设计目标

1. **非线性叙事**：摒弃传统线性剧情，提供真正的选择自由
2. **连锁反应**：一个危机的选择影响其他危机的发展
3. **策略深度**：玩家需要权衡不同危机的优先级和后果
4. **沉浸体验**：通过并发机制营造真实的领导者体验

## 📋 相关文档

### 核心关联
- [参数系统](../05_数据与配置/05_01_参数系统.md) - 危机选择影响的参数变化
- [世界观总览](../01_世界观与剧情/00_世界观总览.md) - 各时代的危机背景
- [轮回系统](../03_系统设计/轮回与传承系统.md) - 危机处理的长期影响

### 支撑系统
- [事件系统](../01_世界观与剧情/事件系统设计.md) - 危机触发的随机事件
- [道之根基系统](./道之根基系统.md) - 危机选择确立的价值观
- [地标系统](./历史回响地标系统.md) - 危机处理留下的永久印记

### 技术规范
- [核心数据结构](../05_数据与配置/05_02_核心数据结构.md) - 危机系统的数据结构
- [系统依赖关系图](../系统依赖关系图.md) - 危机系统的接口规范

---

## 💡 核心设计理念

### 中心辐射模型
游戏采用**"中心辐射"**模型，玩家在指挥室作为中心枢纽，通过世界地图自由选择应对多个**同时发生、并行发展**的危机。

```
        危机A ←─┐
              │
    危机D ←─ 玩家 ─→ 危机B
              │
        危机C ←─┘
```

### 连锁反应机制
玩家在一个危机中的选择，会通过更新全局的**"世界状态"**，动态地影响另一个危机的内容或解决方式，形成真正的连锁反应。

---

## 🔧 系统架构

### 全局世界状态 (GlobalState)
所有剧情、选择、乃至世界状态的唯一数据源是一个全局JSON对象：

```json
{
  "crisis_status": {
    "traitor": "active",
    "wolf_king": "active",
    "supply_shortage": "resolved"
  },
  "world_modifiers": {
    "authority": 15,
    "diplomacy": -5,
    "military": 10
  },
  "story_flags": [
    "traitor_executed",
    "wolf_king_negotiated",
    "supply_route_secured"
  ],
  "timeline": {
    "current_day": 45,
    "crisis_deadlines": {
      "traitor": 60,
      "wolf_king": 90
    }
  }
}
```

### 危机状态管理
每个危机都有独立的状态机：

```
未激活 → 激活中 → 处理中 → 已解决/失败
   ↓        ↓        ↓         ↓
 等待触发  可选择   执行中    产生后果
```

### 反馈机制：《废土快报》
通过游戏内的信息系统，玩家可以实时了解：
- 各危机的最新进展
- 参数变化的具体影响
- 其他势力的反应
- 连锁事件的触发

---

## 🎮 玩法流程

### 1. 危机发现阶段
- 通过探索、情报、随机事件发现新危机
- 危机在世界地图上显示为可交互点
- 每个危机都有紧急程度和截止时间

### 2. 优先级决策阶段
- 玩家查看所有激活的危机
- 评估每个危机的重要性和紧急程度
- 考虑资源分配和人员调度

### 3. 危机处理阶段
- 选择具体的危机进入处理流程
- 在危机内部做出关键选择
- 选择结果影响参数和世界状态

### 4. 连锁反应阶段
- 参数变化触发其他危机的变化
- 新的事件和选项可能解锁或关闭
- 世界状态的更新影响后续发展

---

## ⚡ 核心机制详解

### 时间压力机制
- 每个危机都有隐性或显性的时间限制
- 拖延处理会导致危机恶化
- 某些危机会因为其他危机的处理而提前爆发

### 资源竞争机制
- 不同危机可能需要相同的资源（人员、物资、时间）
- 玩家必须在有限资源下做出取舍
- 资源分配影响危机处理的成功率

### 信息不对称机制
- 玩家初期对危机的了解有限
- 通过情报收集逐步揭示危机全貌
- 错误的信息可能导致错误的决策

---

## 📊 平衡性设计

### 难度曲线
- 初期危机相对独立，便于玩家理解机制
- 中期危机开始产生明显的连锁反应
- 后期危机高度关联，需要统筹规划

### 容错机制
- 没有绝对的"错误"选择，只有不同的后果
- 失败的危机处理也会产生有价值的经验和资源
- 轮回机制允许玩家重新尝试不同的策略

### 奖励机制
- 成功处理危机获得直接奖励
- 巧妙的连锁处理获得额外奖励
- 完美处理所有危机解锁特殊内容

---

**维护者**: 核心机制设计团队
**技术支持**: 系统架构师
**平衡调整**: 数值策划团队