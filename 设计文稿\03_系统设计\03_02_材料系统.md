# 材料系统设计

**文档类型**: 系统设计
**版本号**: v2.0
**创建日期**: 2024-12-19
**最后更新**: 2025-01-05
**维护者**: 系统设计团队
**状态**: 已发布

---

## 📖 文档概述

本文档详细描述月球RPG的材料系统设计，包括材料分类、获取方式、加工机制、品质体系等核心内容。材料系统是装备制作和强化的基础，与副职业系统、装备系统紧密关联。

## 🎯 设计目标

1. **资源深度**：丰富的材料种类支撑复杂的制作体系
2. **获取多样性**：通过多种途径获得不同类型的材料
3. **品质递进**：材料品质体系支撑装备品质提升
4. **世界观融合**：材料设定体现九个时代的特色变迁

## 📋 相关文档

### 核心关联
- [装备系统](./03_01_装备系统.md) - 材料是装备制作和强化的基础
- [副职业系统](./03_05_副职业系统.md) - 材料的加工和制作机制
- [装备词条系统](./03_01_01_装备词条设计.md) - 材料影响装备词条生成
- **[材料系统设计](./材料系统设计.md)** - 详细的材料系统设计文档

### 支撑系统
- [地图探索系统](../04_地图与探索/) - 材料的主要获取途径
- [战斗系统](./战斗系统.md) - 战斗掉落材料机制
- [家族天赋树](./03_07_家族天赋树系统.md) - 工艺传承影响材料处理

### 世界观基础
- [世界观总览](../01_世界观与剧情/00_世界观总览.md) - 各时代材料特色背景
- [九大时代](../01_世界观与剧情/01_九大时代概述.md) - 材料体系的时代演进

---

## ⚠️ 重要说明

> **轮回重置**: 材料在轮回时会重置，但加工配方会通过天赋树保留
> **数值待定**: 文档中的具体数值为设计初稿，待平衡调整
> **品质影响**: 材料品质直接影响制作出的装备品质和词条
> **详细设计**: 完整的材料系统设计请参考[材料系统设计.md](./材料系统设计.md)

---
- [材料分类体系](#️-材料分类体系)
  - [1️⃣ 通用基础材料](#1️⃣-通用基础材料)
  - [2️⃣ 副职业专用材料](#2️⃣-副职业专用材料)
- [制作规则体系](#️-制作规则体系)
  - [装备制作规则](#-装备制作规则)
  - [装备强化规则](#-装备强化规则)
- [材料获取机制](#-材料获取机制)
  - [获取方式分类](#-获取方式分类)
  - [材料掉落递减](#-材料掉落递减)
- [配方传承系统](#-配方传承系统)
  - [配方获取方式](#-配方获取方式)
  - [家族藏经阁传承](#️-家族藏经阁传承)
  - [天赋树配方传承](#-天赋树配方传承)
- [系统平衡性](#️-系统平衡性)
  - [经济平衡设计](#-经济平衡设计)
  - [进度平衡](#-进度平衡)
- [技术实现](#-技术实现)
  - [数据结构设计](#-数据结构设计)
  - [制作系统算法](#️-制作系统算法)
- [材料获取指南](#-材料获取指南)
  - [新手阶段 (1-3轮回)](#-新手阶段-1-3轮回)
  - [发展阶段 (4-6轮回)](#-发展阶段-4-6轮回)
  - [成熟阶段 (7-9轮回)](#-成熟阶段-7-9轮回)

---

## 📁 关联文档
- **[装备系统设计](./装备系统设计.md)** - 装备强化和打造材料需求
- **[装备词条系统设计](./装备词条系统设计.md)** - 词条改造和强化材料
- **[副职业系统设计](./副职业系统设计.md)** - 五大副职业的制作材料需求
- **[家主称号与遗物系统设计](./家主称号与遗物系统设计.md)** - 传承星火材料转换
- **[地图系统设计](./地图系统/地图系统设计.md)** - 材料掉落机制和递减规律
- **[世界观](./剧情系统/世界观.md)** - 九轮回时代的材料设定背景

## 📝 文档内容说明
本文档定义了简化后的材料系统，采用"简洁高效、深度策略"的设计理念。通过减少材料种类但保持品质分级，让玩家专注于材料品质提升而非复杂的材料收集。

---

# 月球(Yueqiu) - 材料系统设计文档

## 📍 文档概述
本文档详细描述月球RPG游戏的简化材料系统设计。系统以**材料等级+品质分级**为核心，通过**等级决定适用范围，品质决定制作效果**的机制，实现简洁而有深度的材料体验。

---

## 🌟 系统总览

### 🎯 设计理念
- **简洁性**: 减少材料种类，专注品质提升
- **通用性**: 大部分材料跨职业通用
- **策略性**: 高级制作可混用不同等级材料
- **传承性**: 配方作为家族传承，永久保留

### 📊 核心参数
- **材料等级**: 3个等级（初级/中级/高级）
- **材料品质**: 6个品质（白/绿/蓝/紫/橙/红）
- **副职业专用材料**: 每职业仅1-2种
- **配方传承**: 可存入家族仓库，轮回不删除

---

## 🗂️ 材料分类体系

### 1️⃣ 通用基础材料

#### 🔨 金属材料
**适用**: 所有装备制作和强化

| 材料名称 | 等级 | 获取轮回 | 主要用途 | 获取方式 |
|----------|------|----------|----------|----------|
| 铁料 | 初级 | 1-3轮回 | 初级装备制作/强化 | 怪物掉落、矿物采集 |
| 精钢 | 中级 | 4-6轮回 | 中级装备制作/强化 | 精英怪物、Boss掉落 |
| 仙金 | 高级 | 7-9轮回 | 高级装备制作/强化 | 高级Boss、特殊事件 |

**品质效果**:
- **白色**: 基础成功率，无额外效果
- **绿色**: 成功率+10%，制作品质提升概率+5%
- **蓝色**: 成功率+20%，制作品质提升概率+15%
- **紫色**: 成功率+35%，制作品质提升概率+30%
- **橙色**: 成功率+50%，制作品质提升概率+50%
- **红色**: 成功率+70%，制作品质提升概率+80%

#### 🧵 纤维材料
**适用**: 防具类装备制作和强化

| 材料名称 | 等级 | 获取轮回 | 主要用途 | 获取方式 |
|----------|------|----------|----------|----------|
| 兽皮 | 初级 | 1-3轮回 | 初级防具制作/强化 | 野兽类怪物掉落 |
| 丝绸 | 中级 | 4-6轮回 | 中级防具制作/强化 | 昆虫类精英、商店 |
| 仙羽 | 高级 | 7-9轮回 | 高级防具制作/强化 | 飞行类Boss掉落 |

#### 💎 宝石材料
**适用**: 饰品类装备制作和强化

| 材料名称 | 等级 | 获取轮回 | 主要用途 | 获取方式 |
|----------|------|----------|----------|----------|
| 水晶 | 初级 | 1-3轮回 | 初级饰品制作/强化 | 矿物采集、商店 |
| 宝石 | 中级 | 4-6轮回 | 中级饰品制作/强化 | 宝箱、精英掉落 |
| 仙石 | 高级 | 7-9轮回 | 高级饰品制作/强化 | 仙境Boss、试炼奖励 |

### 2️⃣ 副职业专用材料

#### 🔨 铁匠专用
| 材料名称 | 用途 | 获取方式 | 备注 |
|----------|------|----------|------|
| 锻造燃料 | 提升锻造成功率 | 特定区域采集、商店 | 消耗品，各等级通用 |

#### 🌿 医师专用
| 材料名称 | 用途 | 获取方式 | 备注 |
|----------|------|----------|------|
| 药草 | 制作各种丹药 | 野外采集、药园种植 | 品质影响丹药效果 |

#### 🏹 猎人专用
| 材料名称 | 用途 | 获取方式 | 备注 |
|----------|------|----------|------|
| 兽筋 | 制作陷阱和追踪道具 | 野兽类怪物掉落 | 可与兽皮配合使用 |

#### 💰 商人专用
| 材料名称 | 用途 | 获取方式 | 备注 |
|----------|------|----------|------|
| 商业印章 | 制作交易道具 | 商人工会、成就奖励 | 提升交易效率 |

#### ☠️ 毒师专用
| 材料名称 | 用途 | 获取方式 | 备注 |
|----------|------|----------|------|
| 毒囊 | 制作各种毒药 | 毒性生物掉落 | 品质影响毒药威力 |

---

## 🛠️ 制作规则体系

### 📋 装备制作规则

#### 🔰 低级装备制作（白色-蓝色）
**材料限制**: 只能使用同等级或更低等级材料
- **初级装备**: 只能用初级材料
- **中级装备**: 可用初级+中级材料
- **高级装备**: 可用初级+中级+高级材料

**材料数量需求**:
| 装备品质 | 主材料数量 | 辅助材料数量 | 专用材料数量 |
|----------|------------|--------------|--------------|
| 白色 | 1 | 0 | 0 |
| 绿色 | 2 | 1 | 0 |
| 蓝色 | 3 | 2 | 1 |

#### ⚡ 高级装备制作（紫色-红色）
**材料限制**: 可以混用不同等级材料，但有最低要求
- **紫色装备**: 至少50%材料为中级以上
- **橙色装备**: 至少70%材料为高级
- **红色装备**: 必须全部为高级材料

**材料数量需求**:
| 装备品质 | 主材料数量 | 辅助材料数量 | 专用材料数量 |
|----------|------------|--------------|--------------|
| 紫色 | 5 | 3 | 2 |
| 橙色 | 8 | 5 | 3 |
| 红色 | 12 | 8 | 5 |

### ⚡ 装备强化规则

#### 🔧 强化材料统一
**取消专门强化材料**: 直接使用制作材料进行强化
- **+1到+3**: 使用对应等级的白色-绿色材料
- **+4到+6**: 使用对应等级的蓝色-紫色材料  
- **+7到+9**: 使用对应等级的橙色-红色材料
- **+10**: 必须使用红色品质材料

**材料品质影响**:
| 材料品质 | 强化成功率加成 | 失败惩罚减免 |
|----------|----------------|--------------|
| 白色 | +0% | 0% |
| 绿色 | +5% | 10% |
| 蓝色 | +10% | 20% |
| 紫色 | +20% | 40% |
| 橙色 | +35% | 60% |
| 红色 | +50% | 80% |

---

## 📊 材料获取机制

### 🎯 获取方式分类

#### 1️⃣ Boss掉落（主要来源）
**掉落特点**: 高品质材料的主要来源
- **区域Boss**: 50%掉落率，绿色-蓝色品质为主
- **时代Boss**: 80%掉落率，紫色-橙色品质为主
- **终极Boss**: 100%掉落率，必出橙色-红色品质

#### 2️⃣ 装备分解
**分解规律**: 装备品质决定材料品质
- **白色装备**: 分解获得白色材料1-2个
- **绿色装备**: 分解获得绿色材料1-2个，10%概率获得蓝色
- **蓝色装备**: 分解获得蓝色材料1-3个，20%概率获得紫色
- **紫色装备**: 分解获得紫色材料2-4个，30%概率获得橙色
- **橙色装备**: 分解获得橙色材料3-5个，40%概率获得红色
- **红色装备**: 分解获得红色材料5-8个

#### 3️⃣ 材料合成
**合成规律**: 低品质材料合成高品质
- **3个白色 → 1个绿色** (90%成功率)
- **3个绿色 → 1个蓝色** (80%成功率)
- **3个蓝色 → 1个紫色** (70%成功率)
- **3个紫色 → 1个橙色** (60%成功率)
- **3个橙色 → 1个红色** (50%成功率)

### 🔄 材料掉落递减
**递减规律**: 只影响普通怪物掉落的白色-绿色材料
- **基础掉落率**: 30%（相比之前降低）
- **递减公式**: 实际掉落率 = 30% × (0.9)^等级差距
- **保底机制**: 最低掉落率1%
- **Boss掉落**: 不受递减影响，固定掉落率

---

## 🎯 配方传承系统

### 📜 配方获取方式

#### 🏪 系统商店购买
**商店分类**:
- **基础商店**: 出售白色-绿色品质装备配方
- **高级商店**: 出售蓝色-紫色品质装备配方
- **大师商店**: 出售橙色品质装备配方（需要声望）

**价格体系**:
| 配方品质 | 购买价格 | 解锁条件 |
|----------|----------|----------|
| 白色 | 100金币 | 无 |
| 绿色 | 500金币 | 副职业10级 |
| 蓝色 | 2000金币 | 副职业30级 |
| 紫色 | 8000金币 | 副职业60级 |
| 橙色 | 30000金币 | 副职业90级+声望 |

#### 👹 Boss掉落配方
**稀有配方**: 只能通过Boss掉落获得
- **特殊套装配方**: 特定Boss专属掉落
- **红色装备配方**: 终极Boss低概率掉落
- **传说配方**: 隐藏Boss或特殊事件奖励

#### ⭐ 特殊事件奖励 (补充途径)
**设计理念**: 作为对Boss掉落和商店购买的补充，部分具有特殊纪念意义的配方（不限于传说品质），会作为一次性事件的奖励。
- **示例**:
    - **配方名称**: 民兵套装制作图纸
    - **获取方式**: 在第三时代"督战队"支线关卡中，通过"说服"路线获得。
    - **描述**: 一张用兽皮绘制的简陋图纸，记录了如何为新兵快速打造耐用的基础护具，是团结与求生的智慧结晶。
    - **高品质基础材料**:
        - **材料名称**: 水晶 (紫色品质)
        - **获取方式**: 在第三时代"屈辱的使团"支线关卡中，达成"惨烈"评价获得。
        - **描述**: 将通用的基础材料赋予极高的品质和独特的剧情意义，作为对玩家特殊经历的奖励。这些材料是打造带有传奇词条装备的核心。
        - **更多示例**:
            - **兽皮(紫色)**: 第四时代"智慧的共享"支线，通过完美"点化"心魔学徒获得。描述为"记录了弟子顿悟瞬间的精神力量"。
            - **铁料(紫色)**: 第四时代"星空下的低语"支线，在高维认知下完成解析获得。描述为"沐浴过星光的金属，内部结构发生了奇妙变化"。

### 🏛️ 家族藏经阁传承

#### 📦 家族藏经阁存储机制

**配方性质说明**:
- **消耗品性质**: 配方为一次性消耗品，使用后配方消失
- **学习机制**: 使用配方后当前家主永久掌握该制作技能
- **轮回重置**: 轮回后新家主不会任何配方，需要重新学习

**家族藏经阁特性**:
- **家族资源**: 配方可存入家族藏经阁，作为家族重要资源
- **永久保存**: 存入藏经阁的配方永不删除
- **消耗使用**: 从藏经阁取出配方学习时，消耗一份配方
- **传承选择**: 获得配方后可选择立即学习或存入藏经阁
- **存储限制**: 家族藏经阁配方存储上限50个

#### 🎯 传承策略
**配方选择建议**:
- **优先存储**: 稀有Boss掉落的特殊配方
- **经济考虑**: 高价值的橙色装备配方
- **职业搭配**: 根据家族发展方向选择配方类型
- **数量规划**: 考虑多次轮回的配方消耗需求

#### 🌳 天赋树配方传承
**配方获得天赋**:
- **副职业传承**: 轮回时根据前任家主的副职业，有概率自动获得相关配方
- **传承概率**: 基础10%，每级天赋+5%，最高50%
- **条件限制**: 只能获得前任家主已掌握且同副职业的配方
- **稀有度影响**: 配方稀有度越高，传承概率越低

---

## ⚖️ 系统平衡性

### 📈 经济平衡设计

#### 💰 材料价值体系
**价值递增**: 高品质材料价值呈指数增长
- **白色材料**: 基础价值，大量产出
- **绿色材料**: 2倍价值，适中产出
- **蓝色材料**: 5倍价值，稀少产出
- **紫色材料**: 15倍价值，稀有产出
- **橙色材料**: 50倍价值，极稀有产出
- **红色材料**: 200倍价值，传说级产出

#### 🔄 资源循环
**健康循环**: 分解-合成-制作形成完整循环
- **分解回收**: 失败装备可分解回收部分材料价值
- **合成提升**: 低品质材料可合成为高品质
- **制作消耗**: 制作过程消耗材料，避免材料泛滥

### 🎯 进度平衡

#### 📊 获取曲线
**阶段性目标**: 不同阶段有明确的材料收集目标
- **1-3轮回**: 专注初级材料品质提升
- **4-6轮回**: 收集中级材料，学习高级配方
- **7-9轮回**: 追求高级材料，制作传说装备

#### ⚡ 难度递增
**合理挑战**: 高品质材料获取难度递增
- **绿色-蓝色**: 通过努力刷怪可稳定获得
- **紫色-橙色**: 需要挑战Boss和精英怪物
- **红色**: 需要完成最高难度挑战

---

## 🚀 技术实现

### 📊 数据结构设计

```javascript
// 材料数据结构
const Material = {
    id: 1,                          // 材料ID
    name: "铁料",                   // 材料名称
    level: "BASIC",                 // 材料等级 (BASIC/MEDIUM/ADVANCED)
    quality: "COMMON",              // 材料品质 (COMMON/UNCOMMON/RARE/EPIC/LEGENDARY/MYTHIC)
    type: "METAL",                  // 材料类型 (METAL/FIBER/GEM/SPECIAL)
    description: "基础金属材料",     // 材料描述
    maxStack: 999,                  // 最大堆叠数量
    sellPrice: 10,                  // 基础出售价格
    qualityMultiplier: {            // 品质价格倍数
        COMMON: 1,
        UNCOMMON: 2,
        RARE: 5,
        EPIC: 15,
        LEGENDARY: 50,
        MYTHIC: 200
    }
}

// 配方数据结构
const Recipe = {
    id: 1,                          // 配方ID
    name: "铁剑配方",               // 配方名称
    resultItemId: 101,              // 制作结果物品ID
    profession: "BLACKSMITH",       // 需要的副职业
    requiredLevel: 10,              // 需要的职业等级
    materials: [                    // 需要的材料
        {
            materialId: 1,          // 材料ID
            quantity: 3,            // 需要数量
            qualityRequirement: "COMMON" // 最低品质要求
        }
    ],
    successRate: 0.8,               // 基础成功率
    canInherit: true                // 是否可以传承
}
```

### ⚙️ 制作系统算法

```javascript
// 制作成功率计算
class CraftingManager {
    calculateSuccessRate(recipe, materials, professionLevel) {
        let baseRate = recipe.successRate;
        
        // 职业等级加成
        const levelBonus = professionLevel * 0.005; // 每级+0.5%
        
        // 材料品质加成
        let qualityBonus = 0;
        materials.forEach(material => {
            qualityBonus += this.getQualityBonus(material.quality);
        });
        qualityBonus = qualityBonus / materials.length; // 平均品质加成
        
        // 最终成功率
        const finalRate = Math.min(0.95, baseRate + levelBonus + qualityBonus);
        return finalRate;
    }
    
    getQualityBonus(quality) {
        const bonusMap = {
            'COMMON': 0,
            'UNCOMMON': 0.05,
            'RARE': 0.10,
            'EPIC': 0.20,
            'LEGENDARY': 0.35,
            'MYTHIC': 0.50
        };
        return bonusMap[quality] || 0;
    }
}

// 材料合成系统
class MaterialSynthesis {
    synthesize(materials) {
        // 检查材料数量和品质一致性
        if (materials.length !== 3) return null;
        if (!this.checkSameQuality(materials)) return null;
        
        const sourceQuality = materials[0].quality;
        const targetQuality = this.getNextQuality(sourceQuality);
        if (!targetQuality) return null;
        
        // 计算成功率
        const successRate = this.getSynthesisRate(sourceQuality);
        
        if (Math.random() < successRate) {
            return this.createMaterial(materials[0].type, targetQuality);
        }
        
        return null; // 合成失败
    }
    
    getSynthesisRate(quality) {
        const rateMap = {
            'COMMON': 0.90,
            'UNCOMMON': 0.80,
            'RARE': 0.70,
            'EPIC': 0.60,
            'LEGENDARY': 0.50
        };
        return rateMap[quality] || 0;
    }
}
```

---

## 📋 材料获取指南

### 🎯 新手阶段 (1-3轮回)
**主要目标**: 收集初级材料，提升材料品质
- **重点材料**: 铁料、兽皮、水晶（各品质）
- **获取策略**: 
  - 击杀普通怪物收集白色-绿色材料
  - 挑战区域Boss获取蓝色材料
  - 使用合成系统提升材料品质
- **使用建议**: 制作绿色-蓝色装备，强化至+3

### ⚡ 发展阶段 (4-6轮回)
**主要目标**: 收集中级材料，学习高级配方
- **重点材料**: 精钢、丝绸、宝石（紫色品质为目标）
- **获取策略**:
  - 挑战精英怪物和时代Boss
  - 分解蓝色装备获取材料
  - 购买高级配方扩展制作选择
- **使用建议**: 制作紫色装备，强化至+6

### 🌟 成熟阶段 (7-9轮回)
**主要目标**: 收集高级材料，制作传说装备
- **重点材料**: 仙金、仙羽、仙石（橙色-红色品质）
- **获取策略**:
  - 挑战终极Boss获取最高品质材料
  - 完成特殊试炼获得稀有配方
  - 合理使用家族传承配方
- **使用建议**: 制作橙色-红色装备，冲击+10强化

---

**文档版本**: v2.0  
**更新时间**: 2024年12月19日  
**设计目标**: 简化材料系统，提升游戏体验深度 