# 角色系统设计

**文档版本**: v1.0
**最后更新**: 2024-07-04
**关联系统**: [战斗系统](./战斗系统.md), [装备与材料系统](./装备与材料系统.md), [轮回与传承系统](./轮回与传承系统.md)

---

## 1. 系统概述

本系统定义了游戏中角色的所有核心属性、成长路径和能力参数。它是角色养成玩法的基石，直接决定了角色的战斗能力和成长体验，并为战斗、装备、技能等上层系统提供数据支持。

---

## 2. 核心属性 (Core Attributes)

角色拥有三个核心基础属性，是角色能力构成的最底层基石。

| 属性名称 | 英文 | 说明 |
| :--- | :--- | :--- |
| **力量 (Strength)** | STR | 代表角色的肌肉力量和物理爆发力。主要影响物理攻击能力。 |
| **敏捷 (Agility)** | AGI | 代表角色的协调性、速度和反应能力。主要影响攻击效率、命中、闪躲和暴击。 |
| **体质 (Constitution)**| CON | 代表角色的生命力和耐力。主要影响生存能力。 |

---

## 3. 战斗参数 (Combat Parameters)

战斗参数是角色在战斗中各项能力的直接体现，它们由核心属性、装备、技能等多种因素共同决定。

### 3.1 基础战斗参数
这些参数主要由核心属性按固定公式转换而来，是角色能力的基础框架。

| 参数名称 | 英文 | 换算公式 (来源: 核心属性) |
| :--- | :--- | :--- |
| **生命 (Health)** | HP | `10 * 体质` |
| **攻击 (Attack)** | ATK | `2 * 力量` |
| **防御 (Defense)** | DEF | `1.5 * 体质 + 0.5 * 力量` |
| **命中率 (Hit Rate)** | HIT | `0.1% * 敏捷` |
| **闪躲率 (Dodge Rate)**| EVA | `0.2% * 敏捷` |
| **暴击率 (Crit. Rate)**| CRIT | `0.1% * 敏捷` |
| **连击率 (Combo Rate)**| COMBO| `0.2% * 敏捷` |

### 3.2 高级战斗参数
这些参数**不受核心属性直接影响**，主要通过装备词条、技能效果、天赋等方式获得。

| 参数名称 | 英文 | 说明 |
| :--- | :--- | :--- |
| **破甲 (Armor Pen)** | PEN | 攻击时无视敌人部分防御力的效果。 |
| **吸血 (Life Steal)** | LIFESTEAL | 将造成的伤害按一定比例转化为自身生命值。 |
| **反击率 (Counter Rate)**| COUNTER | 受到攻击后进行反击的几率。 |
| **格挡率 (Block Rate)**| BLOCK | 受到攻击时格挡部分伤害的几率。 |

---

## 4. 角色成长

### 4.1 等级与经验值
角色通过获得经验值（EXP）来提升等级，满级为 **150级**。从 `L` 级升到 `L+1` 级所需的经验值由以下公式计算：

**`所需经验值 = floor((100 + (L * 50)) * (1.08 ^ L))`**

-   **`L`**: 角色当前等级
-   **`floor()`**: 向下取整

**示例**:
| 当前等级 (L) | 升到下一级所需经验值 |
| :--- | :--- |
| 1 | 162 |
| 10 | ~1,295 |
| 50 | ~121,835 |
| 100 | ~11,446,415 |
| 149 | ~486,950,917 |

### 4.2 属性点获取与分配
角色在成长过程中会获得可自由分配的属性点（AP），用于强化核心属性。

-   **初始状态 (1级)**:
    -   自动拥有 `5力量, 5敏捷, 5体质`。
    -   额外获得 **10点** 可自由分配的AP。
-   **升级获取**:
    -   **2-100级**: 每级获得 **5点** AP。
    -   **101-150级**: 每级获得 **3点** AP。
-   **里程碑奖励**:
    -   在等级达到 **10, 20, ..., 150** 的整数倍时，额外获得 **10点** AP。
-   **满级总计**:
    -   `10 (初始) + (99 * 5) + (50 * 3) + (15 * 10)` = **805点** 可自由分配的AP。

---

## 5. 设计思考

-   **成长分离**: 将角色的基础成长（通过等级和加点）与装备带来的特效成长（破甲、吸血等）进行分离，让玩家在加点时聚焦于角色的核心定位，同时通过搭配装备探索不同的战斗流派。
-   **曲线平滑**: 经验曲线和属性点获取曲线经过设计，确保前期成长感强，后期成长节奏放缓，将重心转移到玩法策略和装备构筑上，符合长期游玩体验。


