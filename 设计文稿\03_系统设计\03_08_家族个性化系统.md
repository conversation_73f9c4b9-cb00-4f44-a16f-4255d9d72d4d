# 族名族徽系统设计

## 📁 关联文档
- **[世界观](./剧情系统/世界观.md)** - 家族传承世界观背景，为族名族徽提供文化基础
- **[家主称号与遗物系统设计](./家主称号与遗物系统设计.md)** - 祖祠展示中族名族徽的显示方式

## 📝 文档内容说明
本文档定义了玩家的身份标识系统，包括全局唯一的族名设定和个性化的族徽选择。族名作为玩家的唯一标识，族徽作为头像功能，共同构成游戏中的身份展示系统。

## 📖 系统概述

族名族徽系统是游戏的用户身份标识系统，为每个玩家提供独特的家族身份。族名作为唯一用户名，族徽作为个性化头像，共同构成玩家在游戏中的身份标识。

### 🎯 设计目标
- **身份唯一性**: 通过全局唯一的族名确保玩家身份识别
- **个性化展示**: 通过族徽提供视觉个性化选择
- **主题契合**: 符合家族传承的游戏世界观
- **简单易用**: 提供直观的设置和管理界面

### 🌟 核心价值
- **身份认同**: 强化玩家的家族归属感
- **社交基础**: 为后期社交功能提供身份基础
- **个性表达**: 满足玩家个性化需求
- **沉浸感增强**: 提升游戏世界观的代入感

---

## 🏷️ 族名系统

### 📝 族名规则
- **字符限制**: 2-12个字符
- **字符类型**: 支持中文、英文字母、数字
- **唯一性**: 全局唯一，不允许重复
- **敏感词过滤**: 基础敏感词检查和过滤

### 🔧 功能特性
- **实时检查**: 输入时实时验证可用性
- **修改机制**: 
  - 首次设置免费
  - 后续修改消耗1000金币
  - 每个角色限制修改次数（最多5次）
- **历史记录**: 保留族名修改历史（管理用途）

### 🚫 命名限制
- **禁止内容**:
  - 系统保留词汇
  - 游戏NPC名称
  - 明显的敏感词汇
  - 纯数字或特殊字符组合
- **格式要求**:
  - 不能以数字开头
  - 不能包含连续的特殊字符
  - 必须包含至少一个汉字或字母

---

## 🎨 族徽系统

### 🖼️ 预设族徽
**设计分类**：
- **龙图腾系列** (8个): 青龙、赤龙、金龙、黑龙等
- **剑盾系列** (6个): 双剑交叉、盾牌纹章、剑与盾等
- **自然元素** (8个): 山峰、流水、烈火、疾风等
- **几何图案** (6个): 圆形纹章、六角星、太极图等
- **神兽系列** (2个): 凤凰、麒麟

**技术规格**：
- **格式**: PNG格式，支持透明背景
- **尺寸**: 128x128像素
- **色彩**: 16色调色板，确保视觉统一
- **存储**: 存放在assets/emblems/目录

### 📤 用户上传族徽
**支持格式**：
- **图片格式**: JPG、PNG
- **尺寸要求**: 建议128x128像素，自动缩放
- **文件大小**: 最大500KB
- **内容限制**: 基础内容审核（可选功能）

**处理流程**：
1. 用户选择图片文件
2. 系统自动压缩和裁剪
3. 保存到内部存储目录
4. 生成缩略图用于显示
5. 设置为当前族徽

### 🔄 族徽管理
- **切换功能**: 随时在预设和上传族徽间切换
- **删除功能**: 可删除上传的族徽，回退到预设
- **备份机制**: 保留最近3个上传的族徽
- **存储清理**: 自动清理超期的族徽文件

---

## 🗄️ 数据设计

### 📊 数据库设计
```sql
-- Player表扩展
ALTER TABLE Player ADD COLUMN clan_name TEXT UNIQUE NOT NULL;
ALTER TABLE Player ADD COLUMN clan_emblem_type TEXT DEFAULT 'preset';
ALTER TABLE Player ADD COLUMN clan_emblem_path TEXT;
ALTER TABLE Player ADD COLUMN clan_emblem_upload_time INTEGER;
ALTER TABLE Player ADD COLUMN clan_name_change_count INTEGER DEFAULT 0;

-- 族名历史记录表（可选）
CREATE TABLE clan_name_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    player_id INTEGER NOT NULL,
    old_clan_name TEXT,
    new_clan_name TEXT,
    change_time INTEGER NOT NULL,
    change_cost INTEGER DEFAULT 0,
    FOREIGN KEY (player_id) REFERENCES Player(id)
);

-- 敏感词表（可选）
CREATE TABLE forbidden_words (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word TEXT NOT NULL UNIQUE,
    type TEXT DEFAULT 'general' -- 'general', 'reserved', 'npc'
);
```

### 🏗️ 数据结构设计
```javascript
// 玩家身份数据结构
const playerIdentity = {
    clanName: "烈阳家族",
    emblemType: "PRESET",
    emblemPath: "dragon_01.png",
    uploadTime: null,
    nameChangeCount: 0
}

// 族徽类型常量
const EMBLEM_TYPE = {
    PRESET: "PRESET",      // 预设族徽
    UPLOADED: "UPLOADED"   // 用户上传
}

// 预设族徽数据结构
const presetEmblem = {
    id: "dragon_01",
    name: "金龙图腾",
    category: "DRAGON",
    resourcePath: "dragon_01.png"
}

// 族徽分类常量
const EMBLEM_CATEGORY = {
    DRAGON: "DRAGON",      // 龙图腾系列
    WEAPON: "WEAPON",      // 剑盾系列
    NATURE: "NATURE",      // 自然元素
    GEOMETRY: "GEOMETRY",  // 几何图案
    MYTHICAL: "MYTHICAL"   // 神兽系列
}

// 族名验证结果
const clanNameValidation = {
    isValid: true,
    errorMessage: null,
    isAvailable: true
}
```

---

## 🎮 用户界面设计

### 🖥️ 设置界面布局
```
┌─────────────────────────────────────┐
│  族名族徽设置                         │
├─────────────────────────────────────┤
│  👤 当前身份                          │
│  ┌─────┐ 族名：XXX家族                │
│  │族徽 │ 修改次数：2/5                 │
│  └─────┘                             │
├─────────────────────────────────────┤
│  ✏️ 修改族名                          │
│  ┌─────────────────────┐ [检查可用]    │
│  │ 输入新族名          │              │
│  └─────────────────────┘              │
│  💰 修改费用：1000金币                │
├─────────────────────────────────────┤
│  🎨 选择族徽                          │
│  📂 预设族徽  📤 上传族徽             │
│  ┌─┬─┬─┬─┬─┬─┐                      │
│  │🐲│⚔│🏔│🔥│⭐│📜│                  │
│  ├─┼─┼─┼─┼─┼─┤                      │
│  │🐉│🛡│🌊│💨│✨│🎯│                  │
│  └─┴─┴─┴─┴─┴─┘                      │
├─────────────────────────────────────┤
│  [确认修改]  [取消]                   │
└─────────────────────────────────────┘
```

### 📱 主界面显示
```
┌─────────────────────────────────────┐
│ ┌─────┐ XXX家族          💰1,250    │
│ │族徽 │ Lv.25                       │
│ └─────┘                   ⚡98/100  │
├─────────────────────────────────────┤
│ [主要游戏内容区域]                   │
│                                     │
│                                     │
└─────────────────────────────────────┘
```

### 🏛️ 祖祠显示界面
```
┌─────────────────────────────────────┐
│ 🏛️ 家族祖祠                         │
├─────────────────────────────────────┤
│ 第一任家主                           │
│ ┌─────┐ XXX家族                     │
│ │族徽 │ "无敌战神"                   │
│ └─────┘ 在位时间：第1-2时代           │
├─────────────────────────────────────┤
│ 第二任家主                           │
│ ┌─────┐ XXX家族                     │
│ │族徽 │ "血煞武君"                   │
│ └─────┘ 在位时间：第2-3时代           │
└─────────────────────────────────────┘
```

---

## ⚙️ 技术实现

### 🔧 核心功能实现

#### 族名验证系统
```javascript
// 族名验证类
class ClanNameValidator {
    constructor() {
        this.minLength = 2;
        this.maxLength = 12;
        this.forbiddenWords = this.loadForbiddenWords();
    }
    
    // 验证族名
    validateClanName(name) {
        // 长度检查
        if (name.length < this.minLength || name.length > this.maxLength) {
            return {
                isValid: false,
                errorMessage: "族名长度必须在2-12个字符之间",
                isAvailable: false
            };
        }
        
        // 字符类型检查
        if (!this.isValidCharacters(name)) {
            return {
                isValid: false,
                errorMessage: "族名只能包含中文、英文和数字",
                isAvailable: false
            };
        }
        
        // 敏感词检查
        if (this.containsForbiddenWords(name)) {
            return {
                isValid: false,
                errorMessage: "族名包含敏感词汇",
                isAvailable: false
            };
        }
        
        // 唯一性检查
        const isAvailable = this.checkNameAvailability(name);
        if (!isAvailable) {
            return {
                isValid: false,
                errorMessage: "该族名已被使用",
                isAvailable: false
            };
        }
        
        return {
            isValid: true,
            errorMessage: null,
            isAvailable: true
        };
    }
}
```

#### 族徽管理系统
```javascript
// 族徽管理类
class EmblemManager {
    constructor() {
        this.presetEmblems = this.loadPresetEmblems();
        this.uploadTempPath = "temp/clan_emblems/";
    }
    
    // 获取预设族徽列表
    getPresetEmblems() {
        return this.presetEmblems;
    }
    
    // 上传族徽 (微信小程序版本)
    uploadEmblem() {
        return new Promise((resolve, reject) => {
            wx.chooseImage({
                count: 1,
                sizeType: ['compressed'],
                sourceType: ['album', 'camera'],
                success: (res) => {
                    const tempFilePath = res.tempFilePaths[0];
                    this.processImage(tempFilePath)
                        .then(processedPath => resolve(processedPath))
                        .catch(error => reject(error));
                },
                fail: (error) => {
                    reject(error);
                }
            });
        });
    }
    
    // 处理图片
    processImage(tempFilePath) {
        return new Promise((resolve, reject) => {
            const canvas = wx.createCanvas();
            const ctx = canvas.getContext('2d');
            const img = canvas.createImage();
            
            img.onload = () => {
                canvas.width = 128;
                canvas.height = 128;
                ctx.drawImage(img, 0, 0, 128, 128);
                
                const filename = `emblem_${Date.now()}.png`;
                // 保存处理后的图片
                wx.setStorage({
                    key: `emblem_${filename}`,
                    data: canvas.toDataURL(),
                    success: () => resolve(filename),
                    fail: (error) => reject(error)
                });
            };
            
            img.src = tempFilePath;
        });
    }
}
```

### 💾 文件存储管理
```kotlin
class EmblemStorageManager {
    companion object {
        const val PRESET_EMBLEM_PATH = "emblems/preset/"
        const val UPLOAD_EMBLEM_DIR = "clan_emblems"
        const val MAX_UPLOAD_SIZE = 500 * 1024 // 500KB
        const val TARGET_SIZE = 128 // 128x128像素
    }
    
    fun getEmblemPath(type: EmblemType, path: String): String {
        return when (type) {
            EmblemType.PRESET -> "file:///android_asset/$PRESET_EMBLEM_PATH$path"
            EmblemType.UPLOADED -> File(context.filesDir, "$UPLOAD_EMBLEM_DIR/$path").absolutePath
        }
    }
    
    fun cleanupOldEmblems(keepCount: Int = 3) {
        // 清理旧的上传族徽文件，保留最近的几个
    }
}
```

---

## 🔄 系统集成

### 🏠 主界面集成
- **位置**: 主界面左上角
- **显示内容**: 族徽（32x32） + 族名
- **点击功能**: 快速跳转到角色面板

### 🏛️ 祖祠系统集成
- **历任家主**: 显示每任家主的族名和族徽
- **传承连续性**: 族名在家主传承中保持一致
- **视觉识别**: 通过族徽快速识别不同时期的家主

### 👥 社交系统预留
- **玩家分身Boss**: 显示对应玩家的族名族徽
- **排行榜**: 族徽作为玩家标识
- **交易系统**: 买卖双方身份展示

---

## 🎯 用户体验设计

### 🚀 首次设置流程
1. **新玩家引导**: 游戏开始时引导设置族名族徽
2. **推荐族名**: 基于随机词汇组合提供建议
3. **族徽预览**: 实时预览选择的族徽效果
4. **确认机制**: 明确告知修改规则和费用

### ⚡ 快速操作
- **一键恢复**: 快速回退到上一个族徽
- **批量预设**: 提供族名+族徽的组合推荐
- **实时预览**: 设置过程中实时看到效果

### 🛡️ 错误处理
- **网络异常**: 本地缓存验证结果
- **文件损坏**: 自动回退到预设族徽
- **存储空间**: 智能清理和压缩

---

## 📈 数据统计

### 📊 使用数据收集
- **族名字符分布**: 分析用户偏好的命名风格
- **族徽选择热度**: 统计各类族徽的使用频率
- **修改频率**: 跟踪族名修改的使用情况
- **上传比例**: 预设vs上传族徽的使用比例

### 🎯 优化方向
- **热门预设**: 根据使用数据增加受欢迎的族徽类型
- **命名建议**: 基于成功率优化族名推荐算法
- **界面优化**: 根据用户行为优化设置流程

---

**文档版本**: v1.0  
**创建时间**: 2024年12月19日  
**系统状态**: 设计完成，待开发实现 