/**
 * 游戏数据管理工具
 * 负责数据的存储、读取、验证等操作
 */

// 默认游戏数据
const DEFAULT_GAME_DATA = {
  playerData: {
    clanName: '龙族',
    level: 1,
    experience: 0,
    nextLevelExp: 100,
    gold: 1000,
    energy: 100,
    maxEnergy: 100,
    attack: 25,
    defense: 15,
    maxHp: 100,
    critRate: 5,
    attackSpeed: 1.2
  },
  gameStats: {
    playTime: 0,
    enemiesKilled: 0,
    goldEarned: 0,
    equipmentFound: 0,
    skillsUsed: 0
  },
  settings: {
    autoAttack: false,
    soundEnabled: true,
    vibrateEnabled: true
  }
}

/**
 * 获取游戏数据
 * @returns {Object} 游戏数据对象
 */
function getGameData() {
  try {
    const savedData = wx.getStorageSync('gameData')
    if (savedData) {
      // 合并默认数据和保存的数据，确保数据完整性
      return mergeData(DEFAULT_GAME_DATA, savedData)
    }
    return DEFAULT_GAME_DATA
  } catch (error) {
    console.error('获取游戏数据失败:', error)
    return DEFAULT_GAME_DATA
  }
}

/**
 * 保存游戏数据
 * @param {Object} gameData - 要保存的游戏数据
 * @returns {boolean} 保存是否成功
 */
function saveGameData(gameData) {
  try {
    const dataToSave = {
      ...gameData,
      lastSaveTime: Date.now(),
      version: '1.0.0'
    }
    
    wx.setStorageSync('gameData', dataToSave)
    return true
  } catch (error) {
    console.error('保存游戏数据失败:', error)
    return false
  }
}

/**
 * 更新玩家数据
 * @param {Object} updates - 要更新的数据
 * @returns {Object} 更新后的完整游戏数据
 */
function updatePlayerData(updates) {
  try {
    const currentData = getGameData()
    const updatedData = {
      ...currentData,
      playerData: {
        ...currentData.playerData,
        ...updates
      }
    }
    
    saveGameData(updatedData)
    return updatedData
  } catch (error) {
    console.error('更新玩家数据失败:', error)
    return getGameData()
  }
}

/**
 * 深度合并数据对象
 * @param {Object} defaultData - 默认数据
 * @param {Object} savedData - 保存的数据
 * @returns {Object} 合并后的数据
 */
function mergeData(defaultData, savedData) {
  const result = { ...defaultData }
  
  for (const key in savedData) {
    if (savedData.hasOwnProperty(key)) {
      if (typeof savedData[key] === 'object' && savedData[key] !== null && !Array.isArray(savedData[key])) {
        result[key] = mergeData(defaultData[key] || {}, savedData[key])
      } else {
        result[key] = savedData[key]
      }
    }
  }
  
  return result
}

/**
 * 重置游戏数据
 * @returns {Object} 重置后的游戏数据
 */
function resetGameData() {
  try {
    const newData = JSON.parse(JSON.stringify(DEFAULT_GAME_DATA))
    saveGameData(newData)
    return newData
  } catch (error) {
    console.error('重置游戏数据失败:', error)
    return DEFAULT_GAME_DATA
  }
}

/**
 * 导出游戏数据（用于备份）
 * @returns {string} JSON格式的游戏数据
 */
function exportGameData() {
  try {
    const gameData = getGameData()
    return JSON.stringify(gameData, null, 2)
  } catch (error) {
    console.error('导出游戏数据失败:', error)
    return ''
  }
}

/**
 * 导入游戏数据（用于恢复备份）
 * @param {string} dataString - JSON格式的游戏数据字符串
 * @returns {boolean} 导入是否成功
 */
function importGameData(dataString) {
  try {
    const importedData = JSON.parse(dataString)
    
    // 验证数据有效性
    if (validateGameData(importedData)) {
      saveGameData(importedData)
      return true
    } else {
      console.error('导入数据格式无效')
      return false
    }
  } catch (error) {
    console.error('导入游戏数据失败:', error)
    return false
  }
}

/**
 * 验证游戏数据有效性
 * @param {Object} data - 要验证的数据
 * @returns {boolean} 数据是否有效
 */
function validateGameData(data) {
  try {
    // 检查必需的字段
    if (!data.playerData) return false
    if (typeof data.playerData.level !== 'number') return false
    if (typeof data.playerData.gold !== 'number') return false
    
    return true
  } catch (error) {
    console.error('验证游戏数据失败:', error)
    return false
  }
}

// 危机节点条件检查函数
function checkCrisisConditions(era, globalState) {
  const availableCrisis = [];
  
  if (era === 1) {
    // 第一时代所有基础危机都无条件触发
    availableCrisis.push('traitor', 'wolf_king', 'supply', 'enemy');
  }
  
  if (era === 2) {
    // 第二时代基础危机
    availableCrisis.push('succession', 'development_path');
    
    // 条件触发：归来的血脉（需要第一时代选择了"秘密流放"）
    if (globalState.story_flags.includes('traitor_exiled')) {
      availableCrisis.push('returnees');
    }
  }
  
  if (era === 3) {
    // 第三时代基础危机
    availableCrisis.push('infiltration', 'tome_theft', 'alliance_pressure');
    
    // 条件触发：信任危机爆发（vigilance过高或选择了"立即逮捕"）
    const highVigilance = globalState.world_modifiers.vigilance > 15;
    const hadPublicExecution = globalState.story_flags.includes('public_execution');
    
    if (highVigilance || hadPublicExecution) {
      availableCrisis.push('internal_paranoia');
    }
  }
  
  if (era === 4) {
    // 第四时代所有危机都无条件触发（探索时代的特性）
    availableCrisis.push('qi_awaken', 'beast_mutation', 'path_conflict', 'fusion_attempt');
  }
  
  if (era === 5) {
    // 第五时代所有危机都无条件触发（交锋时代的特性）
    availableCrisis.push('western_first_contact', 'civilization_clash', 'internal_division', 'power_exploration');
  }
  
  if (era === 6) {
    // 第六时代所有危机都无条件触发（分裂与蜕变时代的特性）
    availableCrisis.push('bloodline_origin', 'western_escalation', 'immortal_fusion_explore', 'survival_test');
  }
  
  if (era === 7) {
    // 第七时代所有危机都无条件触发（求道时代的特性）
    availableCrisis.push('systematize_path', 'source_of_change', 'ascension_clues', 'final_guardians');
  }
  
  if (era === 8) {
    // 第八时代所有危机都无条件触发（守护时代的特性）
    availableCrisis.push('establish_foothold', 'laws_of_cosmos', 'new_civilizations', 'ancient_echoes');
  }
  
  if (era === 9) {
    // 第九时代所有危机都无条件触发（超越时代的特性）
    availableCrisis.push('ultimate_question', 'cosmic_responsibility', 'eternal_legacy', 'transcendence');
  }
  
  return availableCrisis;
}

// 条件选项检查函数
function checkConditionalOptions(era, nodeId, optionId, globalState) {
  // 第一时代条件选项检查
  if (era === 1) {
    if (nodeId === 'traitor' && optionId === 'D') {
      // 将计就计选项需要intelligence > 3
      return globalState.world_modifiers.intelligence > 3;
    }
    
    if (nodeId === 'supply' && optionId === 'C') {
      // 以人祭祀古树需要找到古树的隐藏事件
      return globalState.story_flags.includes('found_ancient_tree');
    }
  }
  
  // 第三时代条件选项检查
  if (era === 3) {
    if (nodeId === 'alliance_pressure' && optionId === 'D') {
      // 利用假情报设伏需要之前假信息反制成功
      return globalState.story_flags.includes('false_info_success');
    }
  }
  
  // 默认返回true（选项可用）
  return true;
}

// 隐藏事件触发检查
function checkHiddenEvents(era, globalState) {
  const hiddenEvents = [];
  
  if (era === 1) {
    // 探索时可能发现古树（基于insight值）
    const discoverAncientTreeChance = Math.min(0.3 + globalState.world_modifiers.insight * 0.05, 0.8);
    if (Math.random() < discoverAncientTreeChance) {
      hiddenEvents.push({
        id: 'ancient_tree_discovery',
        name: '发现古树',
        description: '在探索过程中，族人发现了一颗枯死的古树...',
        flag: 'found_ancient_tree'
      });
    }
  }
  
  return hiddenEvents;
}

// 获取当前可用的危机节点
function getAvailableCrisis(era, globalState) {
  const availableCrisis = checkCrisisConditions(era, globalState);
  
  // 过滤掉已解决的危机
  return availableCrisis.filter(crisis => {
    const crisisStatus = globalState.crisis_status[crisis];
    return crisisStatus === 'active' || crisisStatus === 'pending';
  });
}

module.exports = {
  getGameData,
  saveGameData,
  updatePlayerData,
  resetGameData,
  exportGameData,
  importGameData,
  DEFAULT_GAME_DATA,
  checkCrisisConditions,
  getAvailableCrisis,
  checkConditionalOptions,
  checkHiddenEvents
} 