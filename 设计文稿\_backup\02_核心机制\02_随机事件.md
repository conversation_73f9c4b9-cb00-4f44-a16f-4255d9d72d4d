# 随机事件系统

## 1. 系统概述

随机事件系统是驱动游戏**"并发危机"**叙事框架的核心引擎，也是为每一轮"轮回"增添无穷变数和重玩价值的关键。它通过在游戏进程中插入大量的、带有选择的微型故事，让玩家的每一次冒险都独一无二。

本系统旨在将玩家的决策，从简单的数值增减，提升为对**角色扮演、资源管理和长线规划**的综合考验。玩家在事件中的每一个选择，不仅会影响眼前的战局，更会通过**"标签"**系统，累积成其当世的性格与行为画像，最终在其逝去后，熔铸成独一无二的**家主称号**。

## 2. 事件的分类与触发

### 2.1. 事件分类
-   **主线模块 (Main Quest Events)**: 在主线剧情的关键节点必定触发的固定事件。这些事件的选项会对剧情走向产生重大影响。
-   **轮回历练事件 (Random Trial Events)**: 在探索地图时按概率随机触发的事件。是本系统的主体，旨在增加多样性。

### 2.2. 触发机制
-   **时机**: 主要在玩家进入一个新的地图区域时，按概率触发。
-   **概率**: 基础触发率为15%，会受特定游戏状态（如连续无事件、轮回节点）影响而动态调整。
-   **冷却**: 同一个地图区域在短时间内不会重复触发事件。

## 3. 标准事件模板

所有随机事件都应遵循以下标准结构进行设计，以保证其逻辑清晰，并能与游戏其他系统正确联动。

---
### **标准事件模板 V1.0**

-   **事件ID**: `Unique_Event_ID`
-   **事件名称**: 事件的标题
-   **触发条件**:
    -   **时代**: [第X时代]
    -   **触发概率**: [百分比]
    -   **前置条件**: (可选) [例如：需要拥有特定标签/物品，或世界状态满足某条件]
-   **情景描述**:
    > (这里是展示给玩家的、富有沉浸感的故事背景文本。)

-   **玩家选项**:

    -   **选项A**: [选项的简短描述]
        -   **结果**:
            -   **后续影响**: (对后续关卡或战斗的临时性影响描述)
            -   **资源变更**: (获得/失去的物品、金钱、或时代专属资源)
            -   **授予标签**: `[标签名]` (此标签将用于家主称号判定)
            -   **世界状态变更**: (可选) `[GlobalState.Key] [operator] [value]`

    -   **选项B**: [选项的简短描述]
        -   **结果**:
            -   **后续影响**: ...
            -   **资源变更**: ...
            -   **授予标签**: `[标签名]`
            -   **世界状态变更**: ...
---

## 4. 事件设计范例

以下是一个使用标准模板设计的、发生在第一时代的具体事件范例。

### **范例：斥候的遗言**

-   **事件ID**: `ERA1_SCOUT_LAST_WORD`
-   **事件名称**: 斥候的遗言
-   **触发条件**:
    -   **时代**: [第一时代]
    -   **触发概率**: [15%]
    -   **前置条件**: 无
-   **情景描述**:
    > 在一具饿狼的尸体旁，你发现了一位奄奄一息的家族斥候。他用尽最后力气告诉你，前方营地的首领是他的杀父仇人，但他同时也画出了一条可以绕开营地的安全密道。他恳求你为他复仇，但家族的存续也同样重要。

-   **玩家选项**:

    -   **选项A**: "为他复仇。我族的血，不能白流。"
        -   **结果**:
            -   **后续影响**: 接下来3个关卡的敌人强度增加20%，但最终的区域首领战中，我方全体获得"复仇怒火"状态（伤害+25%）。
            -   **资源变更**: 无。
            -   **授予标签**: `[复仇者]`
            -   **世界状态变更**: 无。

    -   **选项B**: "保存实力，家族的存续为重。"
        -   **结果**:
            -   **后续影响**: 直接跳过下一个非首领关卡，但有50%几率在密道中遭遇一只迷路的精英级怪物。
            -   **资源变更**: 无。
            -   **授予标签**: `[谨慎者]`
            -   **世界状态变更**: 无。

    -   **选项C**: "死人不需要这些东西。" (需要"冷酷"或"掠夺者"前置标签才能显示)
        -   **结果**:
            -   **后续影响**: 无。
            -   **资源变更**: 获得 50 金币，[家族补给] +5。
            -   **授予标签**: `[冷酷者]`
            -   **世界状态变更**: `GlobalState.world_modifiers.authority + 1`

---
**版本**: 1.0
**状态**: 初稿
**撰写人**: AI
**关联文档**:
- [核心循环与时代](./01_核心循环与时代.md)
- [家主称号与遗物系统](../01_系统设计/家主称号与遗物系统.md) 