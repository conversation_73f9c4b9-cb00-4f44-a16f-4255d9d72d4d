# 游戏核心设计文档 (GDD) - 月球 (Yueqiu) v2.0

**文档目的**: 本文档旨在统一并取代项目中所有分散的设计文档，作为游戏玩法、系统、数值和剧情框架的唯一最终解释。

---

## 1. 游戏总览 (Game Overview)

### 1.1. 核心理念与定位 (Concept & Positioning)
本游戏是一款以**家族万年兴衰史**为背景，以**剧情体验**为核心的**放置类角色扮演游戏（RPG）**。玩家的核心目标是通过一代代人的努力，在波澜壮阔的历史中延续家族血脉，最终揭示世界的真相。

### 1.2. 游戏类型 (Genre)
- **剧情驱动的放置RPG (Story-Driven Idle RPG)**: 游戏的重点在于体验宏大的叙事、培养角色、收集和强化装备。游戏中的"威望"、"士气"等策略数值主要服务于剧情分支，为玩家提供不同的叙事体验，而非核心经营玩法。
- **自动战斗 (Auto-Battler)**: 战斗一旦开始便全程自动进行，玩家无法操作角色移动或释放技能。胜负完全取决于玩家在战前的策略配装。

### 1.3. 核心特色 (Key Features)
- **并发危机驱动的策略选择**: 游戏剧情并非线性，而是由多个同时发生的危机事件组成。玩家的抉择会影响全局参数，动态改变世界，形成连锁反应。
- **深度的战前配装**: 战斗全自动，乐趣在于战前。玩家需要围绕"100%技能触发率"的核心机制，构筑（Build）出各种高效的技能循环。
- **宏大的史诗叙事**: 故事横跨九大时代，玩家将亲手引导一个家族从废墟中崛起，走向星辰大海，体验一部完整的文明发展史。
- **看得见的历史传承**: 玩家的重大抉择会物化为游戏世界中永久的地标，每一次轮回都是与自己创造的历史进行互动。

## 2. 核心玩法循环 (The Core Loop)
本游戏的核心循环由 **宏观战略** 与 **微观战斗** 两个层面交织而成，共同构成了"积累-挑战-突破"的螺旋式上升体验。

```mermaid
graph TD
    subgraph 宏观战略 (Macro Strategy)
        A[开始新轮回<br>选择副职业] --> B{处理时代危机<br>推动主线剧情};
        B -- 抉择影响 --> C((GlobalState<br>世界参数));
        C -- 动态触发 --> D[世界事件];
        B -- 确立"道之根基" --> E[解锁永久天赋<br>创造历史地标];
        E --> F[家族底蕴增强];
    end

    subgraph 微观战斗 (Micro Combat)
        G[进入轮回历练<br>挑战关卡/副本] --> H{自动战斗<br>探索/收集};
        H -- 遭遇瓶颈 --> I[结束历练/主动轮回];
    end

    subgraph 传承 (Legacy)
        I -- 结算表现 --> J[获得天赋点<br>传承装备/技能];
    end
    
    F --> G;
    J --> A;

    linkStyle 7 stroke-width:2px,stroke:green;
    linkStyle 8 stroke-width:2px,stroke:blue;
```

**循环解读**:
1.  **宏观战略 (Macro Strategy)**: 在每个时代，玩家作为家族领袖，处理并行的危机事件，推动剧情发展。玩家的**抉择**会影响**世界参数**，并为家族解锁**永久天赋**和**历史地标**，不断增强家族的整体底蕴。
2.  **微观战斗 (Micro Combat)**: 玩家进入具体的关卡或由历史地标形成的副本进行**轮回历练**。在历练中，通过**自动战斗**获取资源和临时成长。
3.  **遭遇瓶颈与传承 (Bottleneck & Legacy)**: 当在历练中达到实力极限时，玩家选择**结束本次轮回**。系统会根据本轮表现进行结算，将成果转化为**天赋点**和可继承的**装备/技能**，这些将用于下一轮的宏观战略和微观战斗。
4.  **螺旋上升**: 带着更强的家族底蕴，玩家开始新的轮回，处理新的时代危机，挑战更深的历练，形成一个不断变强、走得更远的完整闭环。

## 3. 核心系统详解 (Core Systems Explained)
### 3.1. 战斗系统 (Combat System)
#### 3.1.1. 战斗流程与技能触发机制
每一次攻击或技能判定都严格遵循以下流程，这是实现"100%触发率"玩法的技术核心：

1.  **判定开始**: 角色的每一次行动（基础间隔1.5秒，可被"攻击速度"词条缩短）都会触发一次技能释放判定。
2.  **计算总触发概率**: 系统会瞬间计算所有已装备技能的**最终触发概率**之和。
    -   `单个技能最终触发概率 = 技能基础概率 + 技能等级加成 + 装备词条加成 + 其他Buff加成`
    -   `总触发概率 = Σ (所有可用技能的最终触发概率)`
3.  **判定触发方式（核心阈值）**:
    -   **如果 `总触发概率` < 100%**: 本次行动将进行一次随机判定。有可能释放一个技能，也有可能只是一次普通的攻击。
    -   **如果 `总触发概率` ≥ 100%**: **本次行动必定会释放一个技能，而不会进行普通攻击。** 这是游戏策略构建的核心目标。
4.  **选择释放的技能**: 在必定触发技能的情况下，系统会根据一套权重体系来决定具体释放哪个技能。按技能栏的装配顺序，从左到右依次判定，每个技能的触发概率为其最终概率值。玩家可以通过装备【权重调节类词条】来调整各技能的最终概率，从而实现更精准的技能控制。
5.  **进入技能间隔**: 一个技能被触发后，该技能会立刻进入**"技能间隔期"**（也称"同技能冷却"），在此期间它的基础触发概率降为0%，以防止单一技能被连续滥用。此间隔不影响其他不同技能的触发。
6.  **超越100%的追求**: 当`总触发概率`显著超过100%后，其溢出部分并不会浪费。通过装备【超越100%类词条】，溢出的概率可以被转化为 **"多重触发"**（一次行动释放多个技能）、**"伤害提升"** 或其他强大效果，这是后期玩家追求的终极目标。

#### 3.1.2. 核心战斗目标：构筑100%触发率
游戏的核心策略在于 **战前配装**。胜负的关键不在于操作，而在于玩家的配装智慧。玩家需要通过精心搭配不同装备的词条，构建出一套能在特定条件下 **100%触发** 各种强大技能的战斗循环（Build）。

### 3.2. 角色系统 (Character System)
#### 3.2.1. 核心属性与战斗参数
角色拥有三个核心基础属性，是角色能力构成的最底层基石。

| 属性名称 | 英文 | 说明 |
| :--- | :--- | :--- |
| **力量 (Strength)** | STR | 代表角色的肌肉力量和物理爆发力。主要影响物理攻击能力。 |
| **敏捷 (Agility)** | AGI | 代表角色的协调性、速度和反应能力。主要影响攻击效率、命中、闪躲和暴击。 |
| **体质 (Constitution)**| CON | 代表角色的生命力和耐力。主要影响生存能力。 |

战斗参数是角色在战斗中各项能力的直接体现，它们由核心属性、装备、技能等多种因素共同决定。

**基础战斗参数**
这些参数主要由核心属性按固定公式转换而来，是角色能力的基础框架。

| 参数名称 | 英文 | 换算公式 (来源: 核心属性) |
| :--- | :--- | :--- |
| **生命 (Health)** | HP | `10 * 体质` |
| **攻击 (Attack)** | ATK | `2 * 力量` |
| **防御 (Defense)** | DEF | `1.5 * 体质 + 0.5 * 力量` |
| **命中率 (Hit Rate)** | HIT | `0.1% * 敏捷` |
| **闪躲率 (Dodge Rate)**| EVA | `0.2% * 敏捷` |
| **暴击率 (Crit. Rate)**| CRIT | `0.1% * 敏捷` |
| **连击率 (Combo Rate)**| COMBO| `0.2% * 敏捷` |

**高级战斗参数**
这些参数**不受核心属性直接影响**，主要通过装备词条、技能效果、天赋等方式获得。

| 参数名称 | 英文 | 说明 |
| :--- | :--- | :--- |
| **破甲 (Armor Pen)** | PEN | 攻击时无视敌人部分防御力的效果。 |
| **吸血 (Life Steal)** | LIFESTEAL | 将造成的伤害按一定比例转化为自身生命值。 |
| **反击率 (Counter Rate)**| COUNTER | 受到攻击后进行反击的几率。 |
| **格挡率 (Block Rate)**| BLOCK | 受到攻击时格挡部分伤害的几率。 |

#### 3.2.2. 等级、经验与属性点
角色通过获得经验值（EXP）来提升等级，满级为 **150级**。从 `L` 级升到 `L+1` 级所需的经验值由以下公式计算：

**`所需经验值 = floor((100 + (L * 50)) * (1.08 ^ L))`**

-   **`L`**: 角色当前等级
-   **`floor()`**: 向下取整

**属性点 (AP) 获取与分配**:
角色在成长过程中会获得可自由分配的属性点（AP），用于强化核心属性。
-   **初始状态 (1级)**: 自动拥有 `5力量, 5敏捷, 5体质`，并额外获得 **10点** 可自由分配的AP。
-   **升级获取**:
    -   **2-100级**: 每级获得 **5点** AP。
    -   **101-150级**: 每级获得 **3点** AP。
-   **里程碑奖励**: 在等级达到 **10, 20, ..., 150** 的整数倍时，额外获得 **10点** AP。
-   **满级总计**: `10 (初始) + (99 * 5) + (50 * 3) + (15 * 10)` = **805点** 可自由分配的AP。

### 3.3. 技能系统 (Skill System)
#### 3.3.1. 技能分类与效果
技能根据其力量本源和时代背景，分为三大体系。

| 体系 | 特点 | 基础触发率 | 主要获取轮回 |
| :--- | :--- | :--- | :--- |
| **武道技能** | 战斗的基础，效果直接纯粹。 | 较高 (2% ~ 6%) | 1-3时代 |
| **引气技能** | 融合天地灵气，效果开始变得奇特。| 中等 (1% ~ 4%) | 4-6时代 |
| **仙道技能** | 驾驭法则之力，效果强大且稀有。 | 极低 (0.5% ~ 2%)| 7-9时代 |

根据效果，技能可分为四种类型：
1.  **直接伤害型**: 造成一次性的爆发伤害，伤害值基于角色的**攻击力**并受**核心三维**加成。
2.  **持续效果型 (DOT/HOT/BUFF/DEBUFF)**: 施加一个持续数秒或数个回合的状态。
3.  **属性克制型**: 针对特定属性或状态的敌人造成毁灭性打击。
4.  **特殊机制型**: 拥有改变基础战斗规则的独特效果（如：吸血、连环触发等）。

#### 3.3.2. 技能获取、升级与熟练度
- **技能携带**: 玩家最多可以同时装备**20个**不同的技能。
- **获取方式**: 主要通过怪物掉落、离线收益、任务奖励获得**技能书**。
- **技能升级**: 消耗**相同的技能书**为技能提升等级，最高**10级**。升级会提升技能的基础触发概率和效果数值。
- **技能熟练度**: 每次成功触发技能会获得熟练度经验（1-100级），小幅增强该技能的最终效果。

### 3.4. 装备与材料系统 (Equipment & Materials)
#### 3.4.1. 装备分类、品质与等级
- **装备部位**:
    -   **武器**: 主手, 副手, 双手
    -   **防具**: 头, 胸, 腿, 手, 脚
    -   **饰品**: 项链, 戒指x2, 护符
- **装备品质**:
    -   共六阶：普通(白), 精良(绿), 稀有(蓝), 史诗(紫), 传说(橙), **神话(红)**。
    -   品质决定基础属性、词条数量和强度上限。
    -   **神话装备**无法掉落，**仅能通过制造获得**。
- **装备等级**: 每件装备都有一个**物品等级 (Item Level)**，决定了其基础属性和词条属性的数值范围。

#### 3.4.2. 词条系统 (Affixes)
词条是装备的灵魂，为装备提供多样化的特殊效果。
-   **结构**: 稀有及以上品质装备最多可拥有6个词条，分为 **前缀 (Prefix)** 和 **后缀 (Suffix)**。
    -   **前缀**: 通常提供攻击性、资源类或触发类效果。
    -   **后缀**: 通常提供防御性、属性类或功能类效果。
-   **词条库**: 不同装备类型可生成的词条池不同。部分稀有词条仅在特定时代或特定BOSS掉落。
-   **词条品质**: 词条本身也有品质，高品质词条的数值范围更高。

#### 3.4.3. 材料与制造
- **材料**: 制造的基础，分为矿石、皮革、布料、木材、宝石和特殊材料等。材料品质与装备品质一一对应。
- **获取方式**: 怪物掉落、采集、任务奖励、分解装备。
- **制造**: 玩家需先学习**蓝图**，然后在制造台消耗**材料**进行制造。
- **制造规则**:
    -   **主材料品质**: 决定了成品装备的**基础品质**。
    -   **辅助材料**: 可提升产出**高品质装备**的概率，或**锁定/指定**生成某类词条。
    -   **制造技能等级**: 等级越高，产出极品装备的概率越大。

### 3.5. 轮回与传承系统 (Reincarnation & Legacy)
这是游戏长线成长的核心，体现了"家族"这一概念的积累与传承。

#### 3.5.1. 轮回规则（重置与继承）
当玩家在某个时代的任务中卡关时，可以选择在当前进度点进行 **轮回**，以更强的姿态重新开始。

**重置项 (Reset)**:
- **角色等级**: 重置为1级。
- **角色属性点**: 所有自由分配的属性点清空，可重新分配。
- **装备**: 所有已装备和背包内的普通装备全部消失。
- **背包**: 除特殊道具外，所有消耗品、材料等均会清空。
- **副职业**: 副职业等级和已学配方重置，可重新选择。

**继承项 (Legacy)**:
- **祖祠堂**: 可从本轮装备中选择 **2件** 放入"祖祠堂"。祖祠堂内的装备是家族神器，可被后续任何一任家主随时取出使用，**不消耗使用次数**。
- **技能**:
    - **【已统一】** 已学会的技能 **会保留**（即技能本身已解锁，无需再次通过技能书学习），但技能 **等级** 和 **熟练度** 会重置为初始状态。
- **称号与遗物**: 已获得的所有 **家主称号** 和 **遗物** 将 **完全保留**。
- **天赋点**: 根据上一轮的表现（如最终等级、通关节点数、解锁图鉴等）获得5-20点天赋点，用于在【家族天赋树】中解锁永久加成。
- **副职业加成**: 根据前任家主的副职业等级，为新任家主提供初始熟练度加成，公式为：`上任等级 * 50% + 上上任等级 * 10%`。

#### 3.5.2. 家族天赋树
家族天赋树是本游戏中**唯一能够跨越"轮回"的永久性成长系统**。
- **天赋点**: 通过轮回结算、完成家族成就等方式获得。
- **天赋树结构**: 共分为五大分支：
    -   **武道传承**: 强化武道技能、基础三维、暴击等。
    -   **引气修炼**: 强化引气技能、元素伤害、持续效果等。
    -   **仙道超越**: 强化仙道技能、提供真实伤害等高级效果。
    -   **工艺传承**: 全面强化制造系统，提升高品质材料获取和极品词条概率。
    -   **家主威望**: 强化轮回初始优势，如初始金钱、遗物效果等。

#### 3.5.3. 家主称号与遗物
- **家主称号**: 每一轮"轮回"结束，系统会根据家主当世的综合表现（战斗风格、制造技艺、关键抉择等）授予一个**独一无二**的称号，作为荣誉记录在祖祠中。特定称号是解锁"家主威望"天赋的**前置条件**。
- **家主遗物**: 家主逝去时，其生前物品会成为"遗物"遗落在最终战败点。后代家主找到后，可选择获得三种祝福之一：
    1.  **先祖的祝福**: 获得一个持续当前轮回的强大BUFF。
    2.  **智慧的传承**: 立即获得1-3点永久的**家族天赋点**。
    3.  **遗产的馈赠**: 获得一份稀有的高品质制造材料。

#### 3.5.4. 副职业系统
为每一轮"轮回"提供了战斗之外的专精发展路线。
- **职业选择**: 每任家主在轮回开始时，可从五个副职业中**选择一个**进行专精。
- **连续传承**: 连续多代选择同一个副职业会获得**初始熟练度加成**。
- **五大副职业**:
    - **铁匠**: 装备打造与强化。
    - **医师**: 治疗与增益药剂。
    - **猎人**: 提升材料掉落与经验获取。
    - **商人**: 财富与交易。
    - **毒师**: 减益与持续伤害。

### 3.6. 叙事与探索系统 (Narrative & Exploration)
#### 3.6.1. 并发危机与参数系统
游戏剧情并非线性，而是由多个**同时发生、并行发展**的危机事件组成。
- **自由决策**: 玩家在指挥中心（主界面）可自由决定优先处理哪个危机。
- **全局参数 (`GlobalState`)**: 玩家的任何抉择都会更新一个全局的世界状态变量。这个变量的改变，会动态地影响其他危机的后续内容、难度和解决方案，形成连锁反应。
- **设计防火墙**: 参数系统的影响范围被严格限定。它主要通过影响**"战前状态"**（如敌人强度、数量、可用策略选项）和**"宏观战略"**（如资源、人口、剧情走向）来发挥作用，**绝不干涉"战中"的核心战斗机制**（如技能触发率、攻击间隔等）。

#### 3.6.2. 随机事件
随机事件是驱动"并发危机"叙事的核心引擎，为每一轮"轮回"增添变数。
- **触发**: 主要在玩家进入新地图区域时按概率触发。
- **影响**: 玩家在事件中的选择，不仅会影响眼前的战局，更会通过**"标签"**系统，累积成其当世的性格与行为画像，最终在其逝去后，熔铸成独一无二的**家主称号**。

#### 3.6.3. 轮回历练 (历史回响)
这是玩家与自己家族历史进行直接"互动"的核心舞台。
- **入口**: 玩家在主线剧情中做出的重大抉择所产生的**"历史回响地标"**，会成为世界地图上永久性的可选副本入口。
- **宿命剧本**: 历练中的挑战，是一个由玩家过往所有【主线抉择】共同谱写的动态"宿命剧本"。
    - **挑战来源**: 主要挑战来自于被玩家历代负面选择所累积的**"宿敌烙印"**所强化的首领。
- **核心回报**: 在每一轮新轮回中，**首次**成功通关一个时代的历练，可为当前轮回激活一个强大的被动增益**"先祖庇佑"**，是攻克当前主线的重要助力。

## 4. 世界观与时代剧情 (World & Story)
### 4.1. 世界观背景
本游戏的世界观，围绕一个曾经辉煌的武道家族——**烈阳家族**——在长达九代人的时光中所经历的衰落、抗争、复兴与升华展开。其核心故事是一部关于**"传承"**的史诗。

世界的风格将随着时代的演进，从一个挣扎求生的**低魔高武世界**，逐步过渡到充满奇珍异兽、天地灵气的**东方修仙世界**。

在故事的开端，烈阳家族正处于历史的最低谷：核心功法残缺，领地退守至最后的祖地，内忧外患。玩家的初代家主，便是在这般绝境之中，肩负起让家族血脉与荣耀之火得以延续的沉重使命。

### 4.2. 九大时代总览
整个游戏的故事和进程被划分为九个大的时代，代表着家族从衰败到鼎盛，最终寻求超越的完整史诗。

| 大阶段 | 轮回 | 纪元名称 | 核心主题 | 主要冲突 |
|:---|:---|:---|:---|:---|
| **序章：余烬中的刀锋** | 第一轮回 | 生存求存 | 武道求生 | 强邻环伺 + 野兽凶戾 |
| (衰落时代) | 第二轮回 | 血脉分歧 | **内部矛盾** | 族内三派争斗 |
| | 第三轮回 | 强邻入侵 | 外敌威胁 | 周边势力侵犯 |
| **承启：砺锋于微光** | 第四轮回 | 重建基业 | 武仙启蒙 | 妖兽横行 + 群雄并起 |
| (复苏时代) | 第五轮回 | 异域挑战 | **西方势力** | 圣殿+奥术+机械文明 |
| | 第六轮回 | 存亡危机 | **灭族风险** | 血脉消散 + 清洗者 |
| **鼎革：破茧见仙踪** | 第七轮回 | 巅峰突破 | 仙道崛起 | 妖王争锋 + 宗门竞争 |
| (鼎盛时代) | 第八轮回 | 域外入侵 | **域外威胁** | 混沌魔军 + 星空古神 |
| | 第九轮回 | 薪火升华 | 超越轮回 | 道心考验 |

## 5. 数值与平衡 (Values & Balancing)
### 5.1. 初步平衡性分析与建议
**注意**: 此处所有数值均为设计的初始基准，最终平衡性需要通过大量实际测试进行调整。

1.  **经验曲线 (EXP Curve)**
    -   **当前公式**: `floor((100 + (L * 50)) * (1.08 ^ L))`
    -   **分析**: 公式中的 `1.08 ^ L` 指数增长因子在游戏后期（约70级以后）会导致升级所需经验值急剧膨胀，可能会造成玩家成长感停滞。
    -   **建议**: 在游戏进入中后期（例如100级后），可以考虑将经验公式调整为分段函数，后段采用一个更平滑的曲线（如降低指数，或改为多项式增长），以保证后期等级提升的节奏。

2.  **核心属性收益 (Attribute Scaling)**
    -   **现状**:
        -   **力量**: 影响 `攻击` 和 `防御`。
        -   **体质**: 影响 `生命` 和 `防御`。
        -   **敏捷**: 影响 `命中率`、`闪躲率`、`暴击率` 和 `连击率`。
    -   **分析**: **敏捷 (Agility)** 的收益存在明显的**"过载"**问题。它同时影响了四项关键的概率类战斗参数，使得其在加点时的价值远高于力量和体质，可能会导致玩家加点策略单一化（无脑堆敏捷）。
    -   **建议**: **重新分配敏捷的收益**，将其部分效果转移至力量和体质，以提升三大核心属性的策略价值和平衡性。
        -   **保留**: 敏捷继续影响 `命中率` 和 `闪躲率`，这符合其"灵巧"的定位。
        -   **转移**:
            -   将 **`暴击率`** 的主要加成来源改为 **`力量`**。可以理解为，更高的力量更容易打出击中要害的重击。**公式调整为: `CRIT = 0.1% * 力量`**。
            -   将 **`连击率`** 的主要加成来源改为 **`体质`**。可以理解为，更好的体力支撑才能支持连续不断的攻击。**公式调整为: `COMBO = 0.1% * 体质`**。
        -   **修正后换算公式**:
            | 参数名称 | 英文 | 换算公式 (来源: 核心属性) |
            | :--- | :--- | :--- |
            | **生命 (Health)** | HP | `10 * 体质` |
            | **攻击 (Attack)** | ATK | `2 * 力量` |
            | **防御 (Defense)** | DEF | `1.5 * 体质 + 0.5 * 力量` |
            | **命中率 (Hit Rate)** | HIT | `0.1% * 敏捷` |
            | **闪躲率 (Dodge Rate)**| EVA | `0.2% * 敏捷` |
            | **暴击率 (Crit. Rate)**| CRIT | `0.1% * 力量` |
            | **连击率 (Combo Rate)**| COMBO| `0.1% * 体质` |

3.  **高级战斗参数 (Advanced Parameters)**
    -   **分析**: 将 `破甲`, `吸血` 等高级参数与核心属性脱钩，完全由装备、技能、天赋提供，这是一个优秀的设计。它强化了BD构筑的价值，让玩家的追求从单纯的"堆三维"转向更富策略性的"凑特效"。
    -   **建议**: 保持此设计。后续平衡的重点在于控制这些高级参数的数值在装备词条上的投放。

--- 