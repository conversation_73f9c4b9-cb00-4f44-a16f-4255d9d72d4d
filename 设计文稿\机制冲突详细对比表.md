# 月球RPG机制冲突详细对比表

**文档类型**: 冲突对比分析
**版本号**: v1.0
**创建日期**: 2025-01-05
**分析者**: 系统架构师
**状态**: 待决策

---

## 📋 冲突详细对比

### 🔥 冲突1：装备强化成功率加成

#### 涉及文档对比
| 文档 | 铁匠强化成功率加成 | 位置 |
|------|-------------------|------|
| `03_05_副职业系统.md` | **每级+0.3%，满级+30%** | 第93行 |
| `装备系统设计.md` | 未明确提及铁匠加成 | - |
| `03_01_装备系统.md` | 未明确提及铁匠加成 | - |

#### 具体差异
- **选项A**: 每级+0.5%强化成功率（更强的职业加成）
- **选项B**: 每级+0.3%强化成功率（当前副职业系统中的数值）

#### 影响分析
- **选项A优势**: 铁匠职业更有价值，强化体验更好
- **选项B优势**: 数值更保守，平衡性更好

---

### 🔥 冲突2：称号获得条件

#### 涉及文档对比
| 称号名称 | 文档A条件 | 文档B条件 | 差异说明 |
|----------|-----------|-----------|----------|
| **巧手工匠** | 装备制造次数 > 100 | 装备制造次数 > 50 | 触发难度差异 |
| **杀戮之王** | 击杀普通敌人 > 10000 | 击杀普通敌人 > 8000 | 数值略有差异 |
| **无敌传说** | 零战败记录 | 零战败记录 | 条件一致 |

#### 具体差异详解

##### 巧手工匠称号
- **选项A**: 装备制造次数 > 100件（更严格）
- **选项B**: 装备制造次数 > 50件（更宽松）

**影响分析**:
- 选项A：称号更有成就感，但获得难度较高
- 选项B：更容易获得，但成就感略低

##### 杀戮之王称号
- **选项A**: 击杀普通敌人 > 10000只
- **选项B**: 击杀普通敌人 > 8000只

**影响分析**:
- 差异相对较小，主要影响获得时机

---

### 🔥 冲突3：称号命名统一

#### 涉及文档对比
| 概念 | 文档A命名 | 文档B命名 | 风格差异 |
|------|-----------|-----------|----------|
| 零战败成就 | **无敌传说** | **不败传说** | 气势 vs 武侠风格 |

#### 具体差异
- **选项A**: "无敌传说"
  - 更有气势和霸气
  - 符合现代游戏命名风格
  - 更直接表达"从未败过"的含义

- **选项B**: "不败传说"
  - 更贴合武侠/古风风格
  - 与游戏的东方文化背景更契合
  - 语感更优雅

---

### 🔥 冲突4：天赋点获取机制

#### 涉及文档对比
| 获取方式 | 轮回与传承系统.md | 家族天赋树系统.md | 差异说明 |
|----------|-------------------|-------------------|----------|
| **基础天赋点** | 未明确具体数值 | 5点（保底奖励） | 数值明确性 |
| **表现奖励** | 未明确具体数值 | 0-15点 | 数值范围 |
| **里程碑奖励** | 未明确具体时机 | 第3、5、10次轮回 | 触发时机 |

#### 具体差异详解

##### 基础天赋点获取
- **轮回与传承系统**: 描述较为概括，未给出具体数值
- **家族天赋树系统**: 明确规定每次轮回基础5点天赋点

##### 里程碑奖励机制
**家族天赋树系统中的具体规则**:
```
- 第3次轮回：+3天赋点
- 第5次轮回：+5天赋点
- 第10次轮回：+8天赋点
- 此后每5次轮回：+5天赋点
```

##### 副职业奖励
**家族天赋树系统中的具体规则**:
```
- 副职业达到25级：+1天赋点
- 副职业达到50级：+2天赋点
- 副职业达到75级：+3天赋点
- 副职业达到100级：+5天赋点
```

#### 建议统一方案
保留家族天赋树系统中的详细数值规则，因为：
1. 数值更具体，便于实现
2. 平衡性经过考虑
3. 与整个成长体系匹配

---

### 🔶 冲突5：技能系统机制细节

#### 涉及文档对比
| 机制 | 技能系统设计.md | 03_03_技能系统.md | 差异说明 |
|------|-----------------|-------------------|----------|
| **攻击间隔** | 1.5秒 | 1.5秒 | 一致 |
| **100%触发率** | 最高+100% | 最高+100% | 基本一致 |
| **权重算法** | 详细实现 | 概念描述 | 详细程度差异 |

#### 具体差异
两个文档在核心数值上基本一致，主要差异在于：
- **描述详细程度**: 技能系统设计.md更详细
- **实现细节**: 算法描述的具体程度不同

#### 建议处理
保留技能系统设计.md中的详细描述，删除重复内容。

---

### 🔶 冲突6：材料系统重复

#### 涉及文档对比
| 机制 | 材料系统设计.md | 03_02_材料系统.md | 副职业系统.md |
|------|-----------------|-------------------|---------------|
| **品质分级** | 6级品质系统 | 6级品质系统 | 简化描述 |
| **合成成功率** | 详细数值表 | 概念描述 | 铁匠加成 |
| **配方传承** | 详细机制 | 概念描述 | 天赋树关联 |

#### 建议处理
1. 保留材料系统设计.md作为主要参考
2. 其他文档只保留与本系统相关的部分
3. 删除重复的详细描述

---

## 🎯 决策建议优先级

### 立即需要决策的冲突 🔥
1. **装备强化成功率**: 选择0.3%还是0.5%
2. **巧手工匠称号**: 选择50件还是100件装备
3. **称号命名**: 选择"无敌传说"还是"不败传说"

### 可以统一处理的冲突 🔶
1. **天赋点获取**: 采用家族天赋树系统的详细规则
2. **技能系统**: 保留详细版本，删除重复
3. **材料系统**: 保留主文档，删除重复描述

---

## 📊 推荐决策方案

### 装备强化成功率
**推荐**: 选择**0.3%**
- 理由：更平衡，避免铁匠职业过强

### 称号获得条件
**推荐**: 选择**100件装备**
- 理由：保持称号的稀有性和成就感

### 称号命名
**推荐**: 选择**"不败传说"**
- 理由：更符合游戏的东方文化背景

---

**报告完成时间**: 2025年1月5日 16:00
**等待决策**: 请对🔥标记的冲突进行最终选择