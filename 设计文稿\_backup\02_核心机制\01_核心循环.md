# 核心循环与时代

## 1. 游戏核心循环 (The Core Loop)

本游戏以"**轮回 (Reincarnation)**"为核心驱动，玩家将在一代又一代的家族传承中，不断积累力量，揭示世界的真相。每一次轮回都是一次完整的、从兴起到衰亡的体验。

其基本流程如下：

```mermaid
graph TD
    A[开始新一轮轮回] --> B{选择一个副职业};
    B --> C[进入当前时代];
    C --> D{探索地图, 应对并发危机};
    D --> E{抉择改变世界状态};
    E --> F{首次确立"道之根基"};
    F --> G[获得当世临时增益<br>解锁永久天赋分支/副本];
    D --> H[不断成长与挑战];
    H --> I{轮回结束<br>(战败或达成目标)};
    I --> J[结算表现, 获得天赋点];
    J --> A;

    subgraph "单次轮回体验"
        B; C; D; E; F; G; H; I;
    end

    subgraph "家族永久积累"
        J;
    end

    linkStyle 9 stroke:#ff0000,stroke-width:2px,stroke-dasharray: 5 5;
```

## 2. 叙事框架：并发危机 (Concurrent Crisis)

游戏剧情并非线性，而是由多个**同时发生、并行发展**的危机事件组成。玩家作为家族领袖，在指挥中心（主界面）自由决定优先处理哪个危机。

-   **中心辐射**: 玩家是决策的中心，所有危机事件围绕玩家展开。
-   **全局状态 (`GlobalState`)**: 玩家在任何一个危机中的选择，都会更新一个全局的世界状态变量。这个变量的改变，会动态地影响其他危机的后续内容、难度和解决方案，形成真正的连锁反应。
-   **动态反馈**: 每当玩家完成一个危机节点，返回指挥中心时，系统都会生成一份动态报告（如《废土快报》），清晰地告知玩家，他的决策给世界带来了哪些看得见的改变，强化了玩家行为与世界变化的关联感。

## 3. 核心传承系统：道之根基 (The Way)

"道"是玩家在一世轮回中所奉行的核心价值观的体现，由其在处理危机时的抉择倾向（如铁血、仁德、智谋等）所决定。当"道"被系统判定确立时，会立刻为玩家带来一次性的巨大影响，并为家族留下永久的遗产。

### 3.1. 当世之刻：道之烙印 (Mark of the Present)
-   **效果**: 一个强大的、**仅在当前轮回中生效**的临时增益（BUFF）。
-   **目的**: 奖励玩家坚持自己的道路，并帮助其克服眼前的挑战。
-   *示例*: 确立"铁血之道"后，获得"坚韧意志"烙印，效果为"对首领伤害增加10%，但受到的治疗效果降低15%"。

### 3.2. 后世之痕：永久的遗产 (Trace of the Future)
"道"的确立，更会为家族留下**永久的、可继承的**宝贵遗产，主要体现在两个方面：

1.  **解锁新的天赋分支**:
    -   在[家族天赋树系统](./../01_系统设计/家族天赋树系统.md)中，会永久解锁一个与该"道"相对应的新天赋分支。这是家族力量实现质变和多元化发展的最核心途径。
    -   *示例*: 确立"铁血之道"，将永久解锁天赋树中的"铁血传承"分支，其中包含大量强化战斗和生存的天赋。

2.  **创造新的历史地标**:
    -   在抉择发生的地图上，会永久出现一个全新的、与"道"主题相关的地标。
    -   这个地标不仅是纪念，更是一个**永久性的可选挑战副本**的入口。后代家主可以进入其中进行"轮回历练"，挑战被先祖的抉择所烙下的"宿敌"，以获取独特的奖励。

## 4. 九大时代：家族的史诗 (The Nine Eras)

整个游戏的故事和进程被划分为九个大的时代，代表着家族从衰败到鼎盛，最终寻求超越的完整史诗。每个时代都有其独特的核心主题、挑战和可解锁的内容。

| 阶段 | 时代 | 核心主题 | 核心冲突 |
| :--- | :--- | :--- | :--- |
| **序章** | 第一～三时代 | **在余烬中求生** | 内部纷争、外部入侵 |
| **承启** | 第四～六时代 | **于复苏中砺锋** | 妖兽横行、文明碰撞 |
| **鼎盛** | 第七～九时代 | **在巅峰上超越** | 仙魔大战、域外威胁 |

## 5. 核心参数：世界的变量 (Core Parameters: The World's Variables)

驱动整个"并发危机"叙事框架的，是一个全局的`GlobalState`对象。该对象由一系列核心参数构成，这些参数是玩家决策的量化体现，是连接玩家选择与游戏世界反馈的神经系统。

这些参数主要分为三大类：

### 5.1. 核心家族参数 (Core Family Parameters)
这是代表家族当前综合状态的基础数值，大部分对玩家可见，是日常决策的主要依据。

| 参数名 (Code) | 中文名 | 核心作用 |
|---|---|---|
| `authority` | **威望** | 影响内部执行力和对外威慑力。高威望能解锁【铁血】选项，压制内部反对声音。 |
| `morale` | **士气** | 影响战斗效率和负面事件抵抗力。高士气能提升部队战斗力，降低叛逃风险。 |
| `diplomacy` | **外交** | 影响与其他势力的关系和贸易效果。高外交能解锁【仁德】选项，促成和平解决方案。 |
| `intelligence` | **智谋** | 影响策略规划和情报获取能力。高智谋能解锁【智谋】选项，发现敌人的弱点。 |
| `insight` | **洞察** | 影响对事物本质的理解和预见能力。高洞察能解锁【洞察】选项，预见潜在的危机或机遇。|
| `cohesion` | **家族凝聚力** | 影响内部稳定和对负面事件的抵抗力。高凝聚力能降低族人出走或内斗的风险。|

### 5.2. 时代危机参数 (Era Crisis Parameters)
这些参数与特定时代的核心矛盾紧密相关，数值的变动直接反映了玩家在应对该时代危机时的进展和方式。

| 所属时代 | 参数名 (Code) | 中文名 |
|---|---|---|
| **第一时代** | `wolf_threat_level` | 狼王威胁度 |
| | `supply_shortage_rate`| 补给短缺率 |
| **第二时代** | `development_path_tension` | 发展路线张力 |
| **第三时代** | `neighbor_hostility` | 强邻侵犯度 |
| **第四时代** | `beast_threat` | 妖兽威胁度 |
| **第五时代** | `western_threat` | 西方威胁度 |
| **第六时代** | `purifier_influence` | 清洗者影响力 |
| **第七时代** | `western_invasion_progress`| 西征军推进度 |
| **第八时代** | `void_corruption` | 虚空腐蚀度 |
| **第九时代** | `cosmic_understanding`| 宇宙真理理解度|

### 5.3. 隐藏传承参数 (Hidden Legacy Parameters)
这些参数通常对玩家不可见，它们是玩家在历代轮回中重大抉择的积累，会从更深的层次影响游戏的长期走向。

| 参数名 (Code) | 中文名 | 核心作用 |
|---|---|---|
| `bloodline_purity` | **血脉纯粹度** | 受到第六时代"血脉原罪"危机抉择的深刻影响。决定后代出现特殊天赋或遗传缺陷的概率。 |
| `dao_understanding`| **大道领悟** | 在游戏后期，通过一系列特殊事件和抉择积累。是解锁第九时代最终结局的关键。|
| `world_truth_discovery`| **世界真相探索度** | 通过探索隐藏区域、解读古代文献等方式积累。揭示游戏世界观背后的终极秘密。 |
| `legacy_of_choice`| **道之烙印** | 记录玩家在每个时代首次确立的"道"（铁血/仁德等），影响家族的文化倾向和后代的初始性格。|

### 5.4. 参数影响机制
这些参数通过以下方式对游戏世界产生影响：

1.  **触发世界事件**:
    -   当一个或多个参数达到特定阈值时，会触发对应的"世界事件"。
    -   **示例**: 当`士气(morale)` < 20 且 `补给短缺率(supply_shortage_rate)` > 1.5 时，可能触发"饥饿的叛乱"世界事件。

2.  **解锁条件选项**:
    -   在主线危机中，某些特殊选项需要特定参数达到要求才能解锁。
    -   **示例**: 第一时代"内部的背叛"危机中，【洞察】选项需要 `洞察(insight)` > 3 才能选择。

3.  **影响被动收益/损耗**:
    -   参数会持续影响资源的产出、消耗和战斗效率。
    -   **示例**: 高`士气(morale)`会小幅提升所有部队的攻击力。

4.  **改变NPC行为**:
    -   NPC对玩家的态度和行为会根据相关参数（如`威望`、`外交`）发生改变。
    -   **示例**: `威望(authority)`足够高时，一些摇摆不定的NPC会选择屈服或加入。

---
**版本**: 1.0
**状态**: 初稿
**撰写人**: AI
**关联文档**:
- [家族天赋树系统](./../01_系统设计/家族天赋树系统.md)
- [角色系统](./../01_系统设计/角色系统.md) 