# 月球RPG装备词条系统设计

> 本文件以README为准，后续内容如有冲突以README为最终解释。
> 
> 【玩法模式】本游戏采用并发危机驱动模式，玩家在指挥室中心自由选择危机应对，所有危机并行发展，选择影响后续轮回。
> 
> 【轮回机制】每次轮回，等级、装备、技能、背包等全部重置，仅家族天赋树成长永久保留。
> 
> 【数值待定】【经济模型后补】

---

## 目录
- [关联文档](#关联文档)
- [文档内容说明](#文档内容说明)
- [词条系统总览](#️-词条系统总览)
  - [设计理念](#-设计理念)
  - [核心参数](#-核心参数)
- [词条分类体系](#-词条分类体系)
  - [按功能分类](#-按功能分类)
    - [1. 概率操控类词条](#1-概率操控类词条)
    - [2. 超越100%类词条](#2-超越100类词条)
    - [3. 技能选择操控类词条](#3-技能选择操控类词条)
    - [4. 间隔管理类词条](#4-间隔管理类词条)
    - [5. 攻击节奏类词条](#5-攻击节奏类词条)
    - [6. 技能强化类词条](#6-技能强化类词条)
  - [按轮回时代分类](#-按轮回时代分类)
    - [第1-3轮回：武道传承词条](#第1-3轮回武道传承词条)
    - [第4-6轮回：五行引气词条](#第4-6轮回五行引气词条)
    - [第7-9轮回：仙道超越词条](#第7-9轮回仙道超越词条)
- [词条组合系统](#-词条组合系统)
  - [协同效应机制](#-协同效应机制)
    - [1. 同类词条协同](#1-同类词条协同)
    - [2. 跨类词条协同](#2-跨类词条协同)
    - [3. 流派专精协同](#3-流派专精协同)
- [词条获取与强化](#-词条获取与强化)
  - [词条获取途径](#-词条获取途径)
  - [词条品质与数值](#-词条品质与数值)
  - [词条改造与升级](#-词条改造与升级)
- [平衡性与扩展性](#-平衡性与扩展性)
  - [数值平衡策略](#-数值平衡策略)
  - [未来扩展方向](#-未来扩展方向)
- [技术实现方案](#-技术实现方案)
  - [数据结构](#-数据结构)
  - [核心算法](#-核心算法)

---

## 📁 关联文档
- **[装备系统设计](./装备系统设计.md)** - 装备词条的基础框架和品质影响
- **[技能系统设计](./技能系统设计.md)** - 词条如何影响技能触发概率和攻击间隔
- **[材料系统设计](./材料系统设计.md)** - 词条改造和强化所需的材料体系
- **[世界观](./剧情系统/世界观.md)** - 武道/引气/仙道三阶段对应的词条时代特色
- **[家族天赋树系统设计](./家族天赋树系统设计.md)** - 天赋树对词条效果的协同加成

## 📝 文档内容说明
本文档是装备系统的深度扩展，专门设计了围绕100%技能触发率阈值的深度词条机制。包含六大词条分类体系、时代特色词条、多种流派构建方案，为游戏提供丰富的策略深度和构建多样性。

---

## 📍 文档概述
本文档详细描述月球RPG游戏的装备词条系统设计，基于站桩战斗+概率触发技能的核心机制，通过深度词条组合创造多样化的构建流派和策略选择。

---

## ⚔️ 词条系统总览

### 🎯 设计理念
装备词条系统采用"概率操控 + 阈值突破"的设计理念，围绕100%技能触发率这一关键阈值构建策略深度。通过词条组合，玩家可以从"概率流"发展到"必定触发流"，再进化为"多重触发流"或"精准控制流"。

### 📊 核心参数
- **词条上限**: 最多10条词条（红色装备）
- **关键阈值**: 100%技能总触发概率
- **核心机制**: 超过100%时每次攻击必定触发技能
- **词条品质**: 5个品质等级，影响数值范围和特殊效果
- **组合效应**: 同类词条可产生协同增强

---

## 🎲 词条分类体系

### 🌟 按功能分类

#### 1. 概率操控类词条
**目标**: 影响技能触发概率分布和选择机制

##### 基础概率词条
- **技能启蒙**: 所有技能触发率 +8%-20%
  - *设计用途*: 帮助新玩家接近100%阈值
  - *品质影响*: 传说品质可达+25%
- **武道精通**: 武道技能触发率 +10%-25%
  - *专精加成*: 只装备武道技能时效果翻倍
- **引气造诣**: 引气技能触发率 +8%-22%
  - *五行共鸣*: 装备五行词条时额外+5%
- **仙道感悟**: 仙道技能触发率 +5%-18%
  - *稀有效果*: 神话品质时技能必定暴击

##### 概率分配词条
- **专精武道**: 武道技能触发率 +30%，其他技能触发率 -10%
  - *流派导向*: 纯武道流核心词条
- **专精引气**: 引气技能触发率 +25%，其他技能触发率 -8%
- **专精仙道**: 仙道技能触发率 +20%，其他技能触发率 -6%
- **平衡修炼**: 所有技能触发率趋于均等，总触发率 +10%

##### 阈值冲刺词条
- **临界突破**: 当总触发率达到80%-95%时，额外 +10%-20%触发率
- **最后一击**: 当总触发率在90%-99%时，每次攻击后触发率 +1%
- **完美掌控**: 达到100%触发率后，下次轮回开局触发率 +20%

#### 2. 超越100%类词条
**目标**: 在100%基础上提供额外价值和多重触发

##### 多重触发词条
- **技能连携**: 总触发率每超过100%的10%，额外触发技能概率 +10%
  - *示例*: 120%触发率时，20%概率触发2个技能
- **连环爆发**: 总触发率为120%时，每次攻击触发2个技能
- **三重奏鸣**: 总触发率为150%时，每次攻击触发3个技能
- **无限循环**: 总触发率达到200%时，技能不受间隔限制

##### 溢出转换词条
- **溢出强化**: 总触发率每超过100%的5%，所有技能伤害 +10%
- **溢出治疗**: 总触发率每超过100%的10%，每次攻击恢复最大生命值2%
- **溢出护盾**: 总触发率超过150%时，每次技能触发获得护盾
- **完美掌控**: 总触发率超过120%时，可以指定下次触发的技能类型

#### 3. 技能选择操控类词条
**目标**: 影响必定触发时的技能选择权重

##### 权重调节词条
- **武道偏好**: 必定触发时，武道技能被选中概率 +30%-80%
- **引气偏好**: 必定触发时，引气技能被选中概率 +25%-70%
- **仙道偏好**: 必定触发时，仙道技能被选中概率 +20%-60%
- **智能选择**: 根据敌人类型自动调整技能选择权重

##### 优先级控制词条
- **伤害优先**: 必定触发时，优先选择伤害最高的技能
- **效果优先**: 必定触发时，优先选择特殊效果技能
- **冷却优先**: 必定触发时，优先选择刚脱离间隔期的技能
- **随机奇迹**: 必定触发时，完全随机选择，所选技能威力 +50%

##### 条件触发词条
- **血量感应**: 根据当前血量比例，调整技能选择权重
  - *机制*: 血量越低，优先选择恢复/防御技能
- **敌人感应**: 根据敌人类型，自动选择最有效的技能
- **战况感应**: 根据战斗时长，逐渐偏向强力技能

#### 4. 间隔管理类词条
**目标**: 在必定触发基础上，优化技能间隔机制

##### 间隔优化词条
- **快速循环**: 技能间隔加成 +25%-100%
  - *机制说明*: 提升被间隔技能重复触发的概率
- **无缝连接**: 每种技能类型的第一次触发不受间隔限制
- **循环加速**: 每触发3个不同技能后，清除所有技能间隔限制1次
- **完美节拍**: 技能按最优间隔自动触发，总体效率 +50%

##### 间隔清除词条
- **破限武道**: 武道技能触发后，50%-80%概率不进入间隔期
- **永续引气**: 引气技能触发后，40%-70%概率不进入间隔期
- **无限仙道**: 仙道技能触发后，30%-60%概率不进入间隔期
- **天道循环**: 任意技能触发后，20%-40%概率清除其他技能间隔

##### 间隔转换词条
- **技能轮换**: 强制技能按顺序触发，每个技能只能连续触发1次
- **类型轮换**: 强制武道→引气→仙道轮换，每类技能触发率 +30%
- **智能轮换**: 根据战况自动调整技能触发顺序

#### 5. 攻击节奏类词条
**目标**: 影响攻击速度和攻击模式

##### 速度提升词条
- **疾风手法**: 攻击速度 +10%-40%
- **武道节奏**: 每触发一次武道技能，攻击速度 +5%（可叠加）
- **引气律动**: 每触发一次引气技能，下2次攻击速度 +50%
- **仙道韵律**: 每触发一次仙道技能，攻击间隔固定0.5秒，持续5秒

##### 节奏控制词条
- **稳定输出**: 攻击间隔固定1秒，不受攻击速度影响，技能触发率 +20%
- **爆发节奏**: 每5次攻击，第6次攻击必定触发最高伤害技能
- **蓄力打击**: 攻击速度 -50%，但每次攻击触发2个技能
- **完美时机**: 攻击间隔固定在技能间隔刚好结束时

##### 攻击模式词条
- **连击专精**: 连续普攻时，每次攻击技能触发率递增 +10%（最多叠加5次）
- **首击必中**: 战斗开始第一次攻击必定触发最强技能
- **终结打击**: 敌人血量低于30%时，技能触发率翻倍
- **背水一战**: 自身血量低于50%时，每次攻击必定触发2个技能

#### 6. 技能强化类词条
**目标**: 影响技能威力和特殊效果

##### 伤害强化词条
- **武技威力**: 武道技能伤害 +15%-60%
- **真气凝聚**: 引气技能伤害 +20%-75%
- **仙法浩瀚**: 仙道技能伤害 +30%-120%
- **技法大成**: 所有技能伤害 +12%-45%

##### 效果强化词条
- **状态延长**: 技能附带状态效果持续时间 +50%-200%
- **效果叠加**: 同类状态效果可以叠加，最多5层
- **范围扩大**: 群体技能影响敌人数量 +1-3个
- **穿透攻击**: 单体技能有30%-60%概率同时攻击2个敌人

##### 特殊机制词条
- **技能觉醒**: 技能有5%-20%概率触发觉醒效果（伤害翻倍+特殊效果）
- **武道真意**: 武道技能触发时，10%-30%概率获得"武道真意"，下次攻击必暴击
- **五行共鸣**: 引气技能触发时，相克属性技能伤害 +100%
- **仙道法则**: 仙道技能触发时，20%概率无视所有防御和抗性

### 🌊 按轮回时代分类

#### 第1-3轮回：武道传承词条
**时代特色**: 生存武道，朴实有效

##### 生存武道词条
- **绝境反击**: 血量低于30%时，武道技能伤害 +100%
- **以伤换伤**: 受到致命伤害时，对攻击者造成等量真实伤害
- **血战到底**: 每损失10%血量，攻击速度 +10%
- **不屈之志**: 血量首次降到20%以下时，所有技能触发率 +100%（持续10秒）

##### 武道意境词条
- **刀意凌然**: 每连续击杀一个敌人，攻击力 +8%（最多叠加10层）
- **剑心通明**: 暴击率每+1%，技能触发率 +0.5%
- **拳意如山**: 受到伤害越多，反击伤害越高（最高 +200%）
- **武魂觉醒**: 连续使用3个不同武道技能后，下次攻击必定暴击

#### 第4-6轮回：五行引气词条
**时代特色**: 天地复苏，五行力量觉醒

##### 五行单体词条
- **烈火真意** [火]: 火属性伤害 +25%，触发时点燃敌人
- **寒冰精魄** [水]: 水属性伤害 +25%，触发时冰冻敌人
- **青木生机** [木]: 木属性伤害 +25%，击杀时恢复生命
- **厚土之力** [土]: 土属性伤害 +25%，增加20%防御力
- **锐金之锋** [金]: 金属性伤害 +25%，无视30%护甲

##### 五行共鸣词条
- **相生共鸣**: 装备2个相生五行词条时，两属性伤害都+50%
- **相克压制**: 对相克属性敌人造成 +100%伤害
- **五行轮转**: 每次攻击自动切换五行属性（火→土→金→水→木→火）
- **五行调和**: 同时拥有5种五行属性时，所有技能伤害 +100%

#### 第7-9轮回：仙道超越词条
**时代特色**: 仙道崛起，法则掌控

##### 法则掌控词条
- **时间法则**: 技能有15%概率不进入冷却，并回复其他技能1秒冷却
- **空间法则**: 攻击有20%概率同时攻击屏幕上所有敌人
- **因果法则**: 击杀敌人时，30%概率复活敌人为己方作战2回合
- **轮回法则**: 死亡时50%概率时光倒流至3秒前状态

##### 超越极限词条
- **破虚见真**: 无视敌人所有防御和抗性
- **万法归一**: 可以同时触发所有类型的技能
- **天人合一**: 所有伤害转化为真实伤害
- **飞升前兆**: 所有属性每回合自动提升1%，无上限

---

## 🎯 词条组合系统

### 🔄 协同效应机制

#### 1. 同类词条协同
**基础协同**: 装备2个以上同类词条时
- **概率类词条**: 效果相加后额外+20%
- **强化类词条**: 效果相乘而非相加
- **特殊类词条**: 解锁隐藏效果

#### 2. 跨类词条协同
**武道+引气协同**:
- 武道技能词条 + 五行词条 = 武技附带对应五行效果
- 连击词条 + 内力词条 = 连击时恢复内力

**多元素协同**:
- 装备3种不同五行词条时，解锁"五行轮转"效果
- 装备5种五行词条时，获得"五行大帝"称号加成

#### 3. 流派专精协同
**纯武道流**（装备4个以上武道词条）:
- 解锁"武道宗师"效果
- 武道技能触发率 +50%，伤害 +100%
- 普通攻击25%概率触发随机武道技能

**纯引气流**（装备4个以上引气词条）:
- 解锁"引气大师"效果
- 引气技能可以叠加效果
- 同时触发多个引气技能时，效果相乘

**混合修炼流**（平衡装备各类词条）:
- 解锁"文武双修"效果
- 武道技能触发引气技能，引气技能强化武道技能
- 两类技能交替触发时，威力递增

### 🎲 词条进化系统

#### 进化条件
词条可以通过特定条件进化为更强版本：

**基础词条进化**:
- **技能启蒙** + 达成100%触发率 → **技能大师**（+30%触发率）
- **武道精通** + 使用武道技能1000次 → **武道宗师**（武道技能必定暴击）
- **烈火真意** + 击杀火属性敌人100只 → **业火红莲**（火伤害+50%，点燃扩散）

**高级词条觉醒**:
- **时间法则** + 使用技能1000次 → **时间主宰**（所有技能无冷却）
- **五行调和** + 完成五行成就 → **五行帝君**（五行技能可同时触发）

---

## 🏗️ 词条品质系统

### 📊 品质分级

| 品质 | 颜色 | 数值范围 | 特殊效果 | 进化可能 |
|------|------|----------|----------|----------|
| **残缺** | 灰色 | 30%-50% | 无 | 可修复至完整 |
| **完整** | 白色 | 60%-80% | 无 | 可强化至精良 |
| **精良** | 绿色 | 80%-95% | 小概率触发特效 | 可进化至传说 |
| **传说** | 紫色 | 95%-120% | 必定触发特效 | 可觉醒至神话 |
| **神话** | 金色 | 120%-150% | 独特机制 | 无法继续提升 |

### 🔧 词条改造系统

#### 改造类型
- **词条重置**: 重新随机词条类型和数值（保留品质）
- **词条强化**: 提升现有词条的数值上限（品质提升）
- **词条合成**: 将两个低级词条合成一个高级词条
- **词条觉醒**: 为传说品质词条添加神话级特殊效果

#### 改造材料
- **重置石**: 重新随机词条属性
- **强化精华**: 提升词条品质等级
- **合成炉**: 合成高级词条的特殊设施
- **觉醒圣水**: 觉醒神话级词条的稀有材料

---

## 🌟 构建流派示例

### 1. 冲刺100%流
**目标**: 快速达到100%触发率
**核心词条**:
- 技能启蒙 +20%
- 武道精通 +25%
- 引气造诣 +22%
- 临界突破 +20%
- 修炼效率 +150%
**效果**: 轻松达到100%触发率，进入"必定触发"模式

### 2. 超越极限流
**目标**: 追求200%+触发率，多重技能触发
**核心词条**:
- 连环爆发
- 三重奏鸣
- 无限循环
- 溢出强化
- 技能连携
**效果**: 每次攻击触发多个技能，输出爆炸

### 3. 精准控制流
**目标**: 控制技能触发顺序和选择
**核心词条**:
- 完美掌控
- 技能轮换
- 伤害优先
- 智能选择
- 战况感应
**效果**: 100%触发基础上，精确控制战斗节奏

### 4. 单一极限流
**目标**: 专精一个强力技能
**核心词条**:
- 专精武道/引气/仙道
- 破限武道/永续引气/无限仙道
- 威力倍增
- 技能觉醒
- 随机奇迹
**效果**: 单一技能超高频触发，单点爆发

### 5. 五行轮转流
**目标**: 掌控五行力量，属性克制
**核心词条**:
- 五种五行单体词条
- 五行轮转
- 相克压制
- 五行调和
- 元素掌控
**效果**: 自动切换属性，对任何敌人都有克制优势

### 6. 仙道法则流
**目标**: 仙道技能为核心，法则掌控
**核心词条**:
- 专精仙道
- 时间法则
- 空间法则
- 破虚见真
- 万法归一
**效果**: 低频但威力巨大的仙道技能，质变级输出

---

## ⚖️ 平衡性设计

### 🎯 数值平衡原则

#### 1. 100%阈值平衡
- **达成难度**: 需要4-6个词条配合才能达到100%
- **溢出价值**: 超过100%的部分有明确价值转换
- **选择权衡**: 100%触发率 vs 高伤害词条的取舍

#### 2. 流派平衡
- **无绝对最优**: 每种流派都有独特优势和劣势
- **阶段适应**: 不同轮回阶段有对应的最优流派
- **敌人针对**: 特定敌人对某些流派有天然克制

#### 3. 词条获取平衡
- **基础词条**: 容易获得，效果中等
- **高级词条**: 稀有掉落，效果强力
- **神话词条**: 极其稀有，颠覆性效果

### 🔄 动态平衡机制

#### 自适应系统
- **敌人强度**: 根据玩家词条强度动态调整
- **掉落补偿**: 缺少某类词条时提高对应掉落率
- **流派引导**: 通过事件和奖励引导玩家尝试新流派

---

## 🚀 技术实现方案

### 📊 数据结构设计

```javascript
// 词条数据结构
const Affix = {
    id: "affix_001",
    name: "技能启蒙",
    type: "PROBABILITY_CONTROL",
    subType: "BASIC_PROBABILITY",
    quality: "PERFECT",
    value: 20,
    valueType: "PERCENTAGE",
    description: "所有技能触发率 +20%",
    evolutionPath: ["affix_master", "affix_grandmaster"],
    synergyTags: ["SKILL_TRIGGER", "ALL_SKILLS"]
}

// 词条效果计算
class AffixCalculator {
    // 计算总技能触发率
    calculateTotalTriggerRate(affixes, skills) {
        let totalRate = 0;
        
        // 基础触发率计算
        for (let skill of skills) {
            let skillRate = skill.baseTriggerRate;
            
            // 应用词条加成
            for (let affix of affixes) {
                skillRate += this.getAffixEffect(affix, skill);
            }
            
            totalRate += skillRate;
        }
        
        // 应用协同效果
        totalRate = this.applySynergyEffects(totalRate, affixes);
        
        return totalRate;
    }
    
    // 技能选择权重计算
    calculateSkillWeights(affixes, skills, battleContext) {
        let weights = {};
        
        for (let skill of skills) {
            weights[skill.id] = this.getBaseWeight(skill);
            
            // 应用词条影响
            for (let affix of affixes) {
                if (affix.type === "SKILL_SELECTION") {
                    weights[skill.id] *= this.getSelectionMultiplier(affix, skill);
                }
            }
            
            // 应用战况感应
            weights[skill.id] *= this.getBattleContextMultiplier(battleContext, skill);
        }
        
        return weights;
    }
}
```

### 🎮 游戏循环集成

```javascript
// 攻击循环中的词条系统
class CombatSystem {
    performAttack(player, enemy) {
        const affixes = player.getAllAffixes();
        const totalTriggerRate = this.affixCalculator.calculateTotalTriggerRate(
            affixes, player.skills
        );
        
        if (totalTriggerRate >= 100) {
            // 必定触发技能
            const triggeredSkills = this.selectSkillsToTrigger(affixes, totalTriggerRate);
            this.executeSkills(triggeredSkills, player, enemy);
        } else {
            // 概率触发
            if (Math.random() < totalTriggerRate / 100) {
                const skill = this.selectRandomSkill(player.skills, affixes);
                this.executeSkill(skill, player, enemy);
            } else {
                this.executeNormalAttack(player, enemy);
            }
        }
        
        // 更新技能间隔状态
        this.updateSkillCooldowns(player.skills, affixes);
    }
}
```

---

## 📈 扩展功能设计

### 🔮 后期扩展
- **词条共鸣系统**: 不同装备间的词条产生远程共鸣效果
- **传承词条**: 轮回时可以选择保留一个词条到下一代
- **神器词条**: 红色装备专属的颠覆性词条
- **临时词条**: 通过消耗品获得的短期强力词条

### 🏪 商业化考虑
- **词条重置服务**: 付费重新随机词条属性
- **品质提升服务**: 付费提升词条品质等级
- **进化加速服务**: 减少词条进化所需条件

---

**文档版本**: v1.0  
**创建日期**: 2024年12月19日  
**最后更新**: 2024年12月19日  
**相关文档**: 装备系统设计.md, 技能系统设计.md, README.md  
**维护者**: CHX 

### 🛡️ 基础战斗属性词条

作为技能系统的重要补充，这些词条专注于强化角色的基础战斗能力。

#### 📊 核心战斗属性强化

##### 生存基础词条
- **血肉强化**: 生命值 +15%-50%
- **力量觉醒**: 攻击力 +12%-40%
- **护甲大师**: 防御力 +10%-35%

##### 精准制胜词条
- **鹰眼**: 命中率 +5%-20%
- **破甲专精**: 破甲率 +3%-15%
- **致命本能**: 暴击率 +3%-18%
- **重击强化**: 暴击伤害 +20%-100%

##### 战斗反应词条
- **连击天赋**: 连击率 +2%-12%
- **反击直觉**: 反击率 +3%-15%
- **生命汲取**: 吸血率 +2%-10%

##### 生存技巧词条
- **身法如风**: 闪躲率 +3%-18%
- **盾牌精通**: 格挡率 +4%-20%

#### ⚡ 战斗事件联动词条

这些词条与技能系统形成深度互动，创造独特的战斗体验。

##### 事件链条词条
- **闪避反击**: 成功闪躲后，反击率临时+100%，持续3秒
- **格挡蓄力**: 格挡成功时，下次技能伤害+50%
- **暴击狂热**: 暴击时，连击率临时+30%，持续5秒
- **连击迅捷**: 连击成功时，攻击间隔-0.3秒，持续3秒

##### 属性转换词条
- **攻守相济**: 每15点防御力转化为1%暴击率
- **血性觉醒**: 每损失15%生命值，攻击力+10%
- **战意高涨**: 每连续攻击3次，暴击率+5%（可叠加至50%）

---

## 📈 词条进阶系统

### 🔄 词条品质与强化

#### 品质等级
- **普通(白)**: 基础效果，数值为标准值的70%-85%
- **稀有(蓝)**: 提升效果，数值为标准值的85%-100%
- **史诗(紫)**: 强化效果，数值为标准值的100%-120%
- **传说(橙)**: 卓越效果，数值为标准值的120%-150%
- **神话(红)**: 极致效果，数值为标准值的150%-200%，可能包含特殊机制

#### 词条升级系统
- **强化石**: 提升词条数值，不改变词条类型
- **重铸石**: 完全重新随机词条类型和数值
- **定向石**: 保留指定词条，重铸其他词条
- **传承石**: 将词条转移到其他装备（轮回继承的核心道具）

---

## 附录：具体词条列表

本附录提供了游戏中具体可用的词条及其数值范围，按品质（绿/蓝/紫/橙）划分，供开发时直接参考。

### **A. 基础属性词条**

这类词条直接提升角色的核心战斗属性。

*   **力量 (Strength)**
    *   **效果**: 增加力量值，影响物理伤害。
    *   **数值**: 绿(+3-5), 蓝(+6-10), 紫(+11-18), 橙(+19-30)
*   **敏捷 (Agility)**
    *   **效果**: 增加敏捷值，影响攻击速度和暴击率。
    *   **数值**: 绿(+3-5), 蓝(+6-10), 紫(+11-18), 橙(+19-30)
*   **体质 (Vitality)**
    *   **效果**: 增加体质值，影响最大生命值和防御。
    *   **数值**: 绿(+3-5), 蓝(+6-10), 紫(+11-18), 橙(+19-30)
*   **生命值 (Health)**
    *   **效果**: 直接增加最大生命值。
    *   **数值**: 绿(+30-50), 蓝(+51-100), 紫(+101-180), 橙(+181-300)
*   **攻击力 (Attack Power)**
    *   **效果**: 增加基础攻击力。
    *   **数值**: 绿(+5-8), 蓝(+9-15), 紫(+16-25), 橙(+26-40)
*   **物理抗性 (Physical Resistance)**
    *   **效果**: 降低受到的物理伤害百分比。
    *   **数值**: 绿(2-4%), 蓝(5-7%), 紫(8-11%), 橙(12-15%)
*   **法术抗性 (Magical Resistance)**
    *   **效果**: 降低受到的法术伤害百分比。
    *   **数值**: 绿(2-4%), 蓝(5-7%), 紫(8-11%), 橙(12-15%)
*   **暴击率 (Critical Hit Rate)**
    *   **效果**: 提升攻击的暴击几率。
    *   **数值**: 绿(1-2%), 蓝(2.1-3.5%), 紫(3.6-5%), 橙(5.1-7%)
*   **暴击伤害 (Critical Hit Damage)**
    *   **效果**: 提升暴击时造成的额外伤害。
    *   **数值**: 绿(5-8%), 蓝(9-15%), 紫(16-25%), 橙(26-40%)
*   **攻击速度 (Attack Speed)**
    *   **效果**: 降低攻击间隔。
    *   **数值**: 绿(2-4%), 蓝(4.1-6%), 紫(6.1-8.5%), 橙(8.6-12%)
*   **吸血 (Life Steal)**
    *   **效果**: 造成伤害时，将一定比例伤害转化为自身生命。
    *   **数值**: 绿(1-2%), 蓝(2.1-3.5%), 紫(3.6-5%), 橙(5.1-8%)

---
### **B. 功能性词条 (按六大分类)**

#### **1. 概率操控类词条**

*   **技能启蒙 (Skill Enlightenment)**
    *   **效果**: 所有技能的最终触发率增加 X%。
    *   **数值(X)**: 绿(2-3%), 蓝(3.1-4.5%), 紫(4.6-6%), 橙(6.1-8%)
*   **武道之心 (Heart of Martial Arts)**
    *   **效果**: 武道系技能的最终触发率增加 X%。
    *   **数值(X)**: 绿(4-6%), 蓝(6.1-9%), 紫(9.1-12.5%), 橙(12.6-16%)
*   **引气之源 (Source of Qi)**
    *   **效果**: 引气系技能的最终触发率增加 X%。
    *   **数值(X)**: 绿(4-6%), 蓝(6.1-9%), 紫(9.1-12.5%), 橙(12.6-16%)
*   **仙道之光 (Light of Immortality)**
    *   **效果**: 仙道系技能的最终触发率增加 X%。
    *   **数值(X)**: 绿(4-6%), 蓝(6.1-9%), 紫(9.1-12.5%), 橙(12.6-16%)
*   **[橙] 临界突破 (Critical Threshold)**
    *   **效果**: 当你的技能总触发率达到90%但不足100%时，直接获得额外10%的触发率。

#### **2. 超越100%类词条**

*   **[紫] 连携 (Chain)**
    *   **效果**: 你的技能总触发率每溢出100%的部分达到20%，你就有10%的几率触发一次额外的随机技能。
*   **[橙] 连环爆发 (Chain Burst)**
    *   **效果**: 当你的技能总触发率达到120%时，你的攻击有20%的概率触发两个技能。
*   **[橙] 溢出强化 (Overflowing Power)**
    *   **效果**: 你的技能总触发率每溢出100%的部分达到10%，你所有技能的最终伤害就提升5%。

#### **3. 技能选择操控类词条**

*   **武道偏好 (Martial Preference)**
    *   **效果**: 在必定触发技能时，武道系技能被选中的基础权重提升X。
    *   **数值(X)**: 蓝(+20), 紫(+35), 橙(+50)
*   **引气偏好 (Qi Preference)**
    *   **效果**: 在必定触发技能时，引气系技能被选中的基础权重提升X。
    *   **数值(X)**: 蓝(+20), 紫(+35), 橙(+50)
*   **仙道偏好 (Immortal Preference)**
    *   **效果**: 在必定触发技能时，仙道系技能被选中的基础权重提升X。
    *   **数值(X)**: 蓝(+20), 紫(+35), 橙(+50)
*   **[橙] 智能施法 (Smart Cast)**
    *   **效果**: 在必定触发技能时，优先选择克制当前敌人属性的技能。若无克制关系，则优先选择伤害最高的技能。

#### **4. 间隔管理类词条**

*   **快速循环 (Rapid Cycle)**
    *   **效果**: 所有技能的"技能间隔"缩短X%。
    *   **数值(X)**: 蓝(5-8%), 紫(8.1-12%), 橙(12.1-18%)
*   **[紫] 破限 (Limit Break)**
    *   **效果**: 你装备的技能中，等级最高的一个技能触发后有30%概率不进入技能间隔。
*   **[橙] 天道循环 (Heavenly Cycle)**
    *   **效果**: 任意技能触发后，有15%的概率清除所有其他技能的技能间隔。

#### **5. 攻击节奏类词条**

*   **疾风 (Gale)**
    *   **效果**: 攻击速度提升X%。 (与基础属性的"攻击速度"词条可叠加)
    *   **数值(X)**: 蓝(4-6%), 紫(6.1-9%), 橙(9.1-13%)
*   **[紫] 蓄力 (Charge)**
    *   **效果**: 你的攻击间隔延长20%，但你的所有技能伤害提升15%。
*   **[橙] 爆发节奏 (Burst Rhythm)**
    *   **效果**: 每进行4次普通攻击，你的下一次攻击必定触发技能，且该技能伤害提升30%。(此效果独立于100%触发判定)

#### **6. 技能强化类词条**

*   **武技威力 (Martial Might)**
    *   **效果**: 武道系技能的最终伤害增加X%。
    *   **数值(X)**: 绿(5-8%), 蓝(8.1-12%), 紫(12.1-17%), 橙(17.1-25%)
*   **真气凝聚 (Qi Condensation)**
    *   **效果**: 引气系技能的最终伤害增加X%。
    *   **数值(X)**: 绿(5-8%), 蓝(8.1-12%), 紫(12.1-17%), 橙(17.1-25%)
*   **仙法浩瀚 (Immortal Vastness)**
    *   **效果**: 仙道系技能的最终伤害增加X%。
    *   **数值(X)**: 绿(5-8%), 蓝(8.1-12%), 紫(12.1-17%), 橙(17.1-25%)
*   **[紫] 状态延长 (Status Extension)**
    *   **效果**: 你施加的所有增益和减益状态，其基础持续时间延长30%。
*   **[橙] 技能觉醒 (Skill Awakening)**
    *   **效果**: 你所有技能在触发时，有5%的几率发生"觉醒"，本次触发的最终伤害和效果翻倍。
