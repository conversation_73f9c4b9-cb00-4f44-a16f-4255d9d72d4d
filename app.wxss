/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
} 

/* 全局样式变量 */
:root {
  --primary-color: #e74c3c;
  --secondary-color: #3498db;
  --success-color: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --dark-color: #2c3e50;
  --light-color: #ecf0f1;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --border-color: #bdc3c7;
  --bg-color: #f8f9fa;
  --card-bg: #ffffff;
  
  /* 间距变量 */
  --spacing-xs: 8rpx;
  --spacing-sm: 16rpx;
  --spacing-md: 24rpx;
  --spacing-lg: 32rpx;
  --spacing-xl: 48rpx;
  
  /* 圆角变量 */
  --border-radius-sm: 8rpx;
  --border-radius-md: 12rpx;
  --border-radius-lg: 16rpx;
  
  /* 字体大小 */
  --font-xs: 24rpx;
  --font-sm: 28rpx;
  --font-md: 32rpx;
  --font-lg: 36rpx;
  --font-xl: 40rpx;
  --font-xxl: 48rpx;
}

/* 全局重置样式 */
page {
  background-color: var(--bg-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
}

/* 通用容器样式 */
.container {
  padding: var(--spacing-md);
}

.page-container {
  min-height: 100vh;
  background-color: var(--bg-color);
}

/* 卡片样式 */
.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: var(--spacing-md);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-md);
  border-bottom: 2rpx solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.card-title {
  font-size: var(--font-lg);
  font-weight: 600;
  margin: 0;
}

.card-content {
  padding: var(--spacing-md);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-md);
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 120rpx;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:active {
  background-color: #c0392b;
  transform: scale(0.98);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-sm);
  min-width: 80rpx;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 16rpx;
  background-color: var(--light-color);
  border-radius: 8rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color), var(--warning-color));
  transition: width 0.3s ease;
}

/* 图标样式 */
.icon {
  width: 48rpx;
  height: 48rpx;
}

.icon-large {
  width: 96rpx;
  height: 96rpx;
}

/* 文本样式 */
.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-center {
  text-align: center;
}

.text-bold {
  font-weight: 600;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  justify-content: space-around;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 间距样式 */
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.mx-auto { margin-left: auto; margin-right: auto; }

/* 装备品质颜色 */
.quality-common { color: #95a5a6; }
.quality-uncommon { color: #27ae60; }
.quality-rare { color: #3498db; }
.quality-epic { color: #9b59b6; }
.quality-legendary { color: #f39c12; }
.quality-artifact { color: #e74c3c; }
