// index.js
// const gameData = require('../../utils/gameData.js')
// const battleSystem = require('../../utils/battleSystem.js')

Page({
  data: {
    // 玩家数据
    playerData: {
      clanName: '龙族',
      level: 1,
      experience: 0,
      nextLevelExp: 100,
      gold: 1000,
      energy: 100,
      maxEnergy: 100
    },
    
    // 当前区域信息
    currentArea: {
      name: '新手村',
      description: '适合初学者的安全区域'
    },
    
    // 当前敌人
    currentEnemy: {
      name: '史莱姆',
      currentHp: 50,
      maxHp: 50,
      attack: 10,
      defense: 5,
      gold: 10,
      exp: 15
    },
    
    // 装备的技能
    equippedSkills: [
      { id: 1, name: '重击', level: 1, icon: '⚔️' },
      { id: 2, name: '治疗', level: 1, icon: '💚' }
    ],
    
    // 界面状态
    canAttack: true,
    autoAttack: false,
    canProgress: false,
    
    // 离线奖励
    showOfflineReward: false,
    offlineTime: '',
    offlineGold: 0,
    offlineExp: 0,
    
    // 计算属性
    experiencePercent: 0,
    enemyHealthPercent: 100
  },

  onLoad() {
    console.log('游戏主界面加载')
    this.initGameData()
    this.checkOfflineReward()
    this.startGameLoop()
  },

  onShow() {
    console.log('游戏主界面显示')
    // 从其他页面返回时刷新数据
    this.refreshPlayerData()
  },

  onHide() {
    console.log('游戏主界面隐藏')
    // 保存游戏数据
    this.saveGameData()
  },

  // 初始化游戏数据
  initGameData() {
    try {
      // 从本地存储加载游戏数据
      const savedData = wx.getStorageSync('gameData')
      if (savedData) {
        this.setData({
          playerData: { ...this.data.playerData, ...savedData.playerData }
        })
      }
      
      // 更新计算属性
      this.updateCalculatedValues()
      
    } catch (error) {
      console.error('初始化游戏数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      })
    }
  },

  // 更新计算属性
  updateCalculatedValues() {
    const { playerData, currentEnemy } = this.data
    
    this.setData({
      experiencePercent: Math.floor((playerData.experience / playerData.nextLevelExp) * 100),
      enemyHealthPercent: Math.floor((currentEnemy.currentHp / currentEnemy.maxHp) * 100)
    })
  },

  // 攻击按钮点击
  onAttack() {
    if (!this.data.canAttack) return
    
    try {
      // 执行攻击逻辑
      const attackResult = this.performAttack()
      
      // 更新界面
      this.setData({
        currentEnemy: attackResult.enemy,
        playerData: attackResult.player
      })
      
      this.updateCalculatedValues()
      
      // 检查战斗结果
      if (attackResult.enemyDefeated) {
        this.onEnemyDefeated()
      }
      
      // 添加攻击间隔
      this.setData({ canAttack: false })
      setTimeout(() => {
        this.setData({ canAttack: true })
      }, 500)
      
    } catch (error) {
      console.error('攻击执行失败:', error)
      wx.showToast({
        title: '攻击失败',
        icon: 'none'
      })
    }
  },

  // 执行攻击计算
  performAttack() {
    const { playerData, currentEnemy } = this.data
    
    // 简单的伤害计算
    const playerAttack = Math.floor(Math.random() * 20) + 10 // 10-30伤害
    const enemyDamage = Math.max(1, playerAttack - currentEnemy.defense)
    
    // 更新敌人血量
    const newEnemyHp = Math.max(0, currentEnemy.currentHp - enemyDamage)
    const enemyDefeated = newEnemyHp <= 0
    
    let updatedPlayer = { ...playerData }
    let updatedEnemy = { ...currentEnemy, currentHp: newEnemyHp }
    
    // 如果敌人被击败
    if (enemyDefeated) {
      updatedPlayer.gold += currentEnemy.gold
      updatedPlayer.experience += currentEnemy.exp
      
      // 检查升级
      if (updatedPlayer.experience >= updatedPlayer.nextLevelExp) {
        updatedPlayer.level += 1
        updatedPlayer.experience -= updatedPlayer.nextLevelExp
        updatedPlayer.nextLevelExp = Math.floor((100 + (updatedPlayer.level * 50)) * Math.pow(1.08, updatedPlayer.level))
        
        wx.showToast({
          title: `升级到 Lv.${updatedPlayer.level}!`,
          icon: 'success'
        })
      }
    }
    
    return {
      player: updatedPlayer,
      enemy: updatedEnemy,
      enemyDefeated,
      damage: enemyDamage
    }
  },

  // 敌人被击败
  onEnemyDefeated() {
    wx.showToast({
      title: `击败了 ${this.data.currentEnemy.name}!`,
      icon: 'success'
    })
    
    // 延迟刷新敌人
    setTimeout(() => {
      this.spawnNewEnemy()
    }, 1000)
    
    // 检查是否可以进入下一区域
    this.checkProgressCondition()
  },

  // 生成新敌人
  spawnNewEnemy() {
    const enemies = [
      { name: '史莱姆', maxHp: 50, attack: 10, defense: 5, gold: 10, exp: 15 },
      { name: '哥布林', maxHp: 80, attack: 15, defense: 8, gold: 15, exp: 25 },
      { name: '骷髅兵', maxHp: 120, attack: 20, defense: 12, gold: 25, exp: 35 }
    ]
    
    const randomEnemy = enemies[Math.floor(Math.random() * enemies.length)]
    const newEnemy = {
      ...randomEnemy,
      currentHp: randomEnemy.maxHp
    }
    
    this.setData({
      currentEnemy: newEnemy
    })
    
    this.updateCalculatedValues()
  },

  // 切换自动攻击
  toggleAutoAttack() {
    const newAutoAttack = !this.data.autoAttack
    this.setData({ autoAttack: newAutoAttack })
    
    if (newAutoAttack) {
      this.startAutoAttack()
      wx.showToast({
        title: '开启自动攻击',
        icon: 'success'
      })
    } else {
      this.stopAutoAttack()
      wx.showToast({
        title: '关闭自动攻击',
        icon: 'none'
      })
    }
  },

  // 开始自动攻击
  startAutoAttack() {
    this.autoAttackTimer = setInterval(() => {
      if (this.data.autoAttack && this.data.canAttack) {
        this.onAttack()
      }
    }, 1000) // 每秒攻击一次
  },

  // 停止自动攻击
  stopAutoAttack() {
    if (this.autoAttackTimer) {
      clearInterval(this.autoAttackTimer)
      this.autoAttackTimer = null
    }
  },

  // 检查离线奖励
  checkOfflineReward() {
    try {
      const lastOnlineTime = wx.getStorageSync('lastOnlineTime')
      const currentTime = Date.now()
      
      if (lastOnlineTime && currentTime - lastOnlineTime > 60000) { // 离线超过1分钟
        const offlineMinutes = Math.floor((currentTime - lastOnlineTime) / 60000)
        const offlineGold = offlineMinutes * 5 // 每分钟5金币
        const offlineExp = offlineMinutes * 2 // 每分钟2经验
        
        this.setData({
          showOfflineReward: true,
          offlineTime: `${offlineMinutes}分钟`,
          offlineGold,
          offlineExp
        })
      }
      
      // 更新在线时间
      wx.setStorageSync('lastOnlineTime', currentTime)
      
    } catch (error) {
      console.error('检查离线奖励失败:', error)
    }
  },

  // 领取离线奖励
  claimOfflineReward() {
    const { offlineGold, offlineExp, playerData } = this.data
    
    const updatedPlayerData = {
      ...playerData,
      gold: playerData.gold + offlineGold,
      experience: playerData.experience + offlineExp
    }
    
    this.setData({
      playerData: updatedPlayerData,
      showOfflineReward: false
    })
    
    this.updateCalculatedValues()
    this.saveGameData()
    
    wx.showToast({
      title: '奖励已领取!',
      icon: 'success'
    })
  },

  // 前往下一区域
  goToNextArea() {
    if (!this.data.canProgress) {
      wx.showToast({
        title: '需要更高等级',
        icon: 'none'
      })
      return
    }
    
    wx.showModal({
      title: '进入下一区域',
      content: '确定要进入更高级的区域吗？',
      success: (res) => {
        if (res.confirm) {
          // TODO: 实现区域切换逻辑
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          })
        }
      }
    })
  },

  // 检查进度条件
  checkProgressCondition() {
    const canProgress = this.data.playerData.level >= 5
    this.setData({ canProgress })
  },

  // 打开设置
  openSettings() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 刷新玩家数据
  refreshPlayerData() {
    try {
      const savedData = wx.getStorageSync('gameData')
      if (savedData && savedData.playerData) {
        this.setData({
          playerData: { ...this.data.playerData, ...savedData.playerData }
        })
        this.updateCalculatedValues()
      }
    } catch (error) {
      console.error('刷新玩家数据失败:', error)
    }
  },

  // 保存游戏数据
  saveGameData() {
    try {
      const gameData = {
        playerData: this.data.playerData,
        lastSaveTime: Date.now()
      }
      
      wx.setStorageSync('gameData', gameData)
      
    } catch (error) {
      console.error('保存游戏数据失败:', error)
    }
  },

  // 开始游戏循环
  startGameLoop() {
    // 每5秒自动保存一次
    this.saveTimer = setInterval(() => {
      this.saveGameData()
    }, 5000)
  },

  onUnload() {
    // 清理定时器
    this.stopAutoAttack()
    if (this.saveTimer) {
      clearInterval(this.saveTimer)
    }
    
    // 保存数据
    this.saveGameData()
  }
})
