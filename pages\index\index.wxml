<!--index.wxml-->
<navigation-bar title="Weixin" back="{{false}}" color="black" background="#FFF"></navigation-bar>
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <view class="page-container">
      <!-- 顶部角色信息栏 -->
      <view class="character-header">
        <view class="clan-info">
          <view class="clan-emblem">🐲</view>
          <view class="clan-details">
            <text class="clan-name">{{playerData.clanName}}</text>
            <text class="character-level">Lv.{{playerData.level}}</text>
          </view>
        </view>
        <view class="resources">
          <view class="resource-item">
            <text class="resource-label">💰</text>
            <text class="resource-value">{{playerData.gold}}</text>
          </view>
          <view class="resource-item">
            <text class="resource-label">⚡</text>
            <text class="resource-value">{{playerData.energy}}/{{playerData.maxEnergy}}</text>
          </view>
        </view>
      </view>

      <!-- 经验进度条 -->
      <view class="experience-bar">
        <view class="progress-label">
          <text>经验: {{playerData.experience}}/{{playerData.nextLevelExp}}</text>
        </view>
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{experiencePercent}}%"></view>
        </view>
      </view>

      <!-- 战斗区域 -->
      <view class="battle-area card">
        <view class="area-info">
          <text class="area-name">{{currentArea.name}}</text>
          <text class="area-description">{{currentArea.description}}</text>
        </view>
        
        <!-- 敌人信息 -->
        <view class="enemy-section" wx:if="{{currentEnemy}}">
          <view class="enemy-avatar">👹</view>
          <view class="enemy-info">
            <text class="enemy-name">{{currentEnemy.name}}</text>
            <view class="enemy-health">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{enemyHealthPercent}}%"></view>
              </view>
              <text class="health-text">{{currentEnemy.currentHp}}/{{currentEnemy.maxHp}}</text>
            </view>
          </view>
        </view>

        <!-- 战斗按钮 -->
        <view class="battle-controls">
          <button class="btn btn-primary battle-btn" bindtap="onAttack" disabled="{{!canAttack}}">
            攻击
          </button>
          <button class="btn btn-secondary btn-small" bindtap="toggleAutoAttack">
            {{autoAttack ? '停止自动' : '自动攻击'}}
          </button>
        </view>
      </view>

      <!-- 技能栏 -->
      <view class="skills-bar">
        <text class="section-title">装备技能</text>
        <scroll-view class="skills-list" scroll-x="true">
          <view class="skill-item" wx:for="{{equippedSkills}}" wx:key="id">
            <view class="skill-icon">⚔️</view>
            <text class="skill-name">{{item.name}}</text>
            <text class="skill-level">Lv.{{item.level}}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 离线收益提示 -->
      <view class="offline-reward card" wx:if="{{showOfflineReward}}">
        <view class="card-header">
          <text class="card-title">离线收益</text>
        </view>
        <view class="card-content">
          <text>离线时间: {{offlineTime}}</text>
          <text>获得金币: {{offlineGold}}</text>
          <text>获得经验: {{offlineExp}}</text>
          <button class="btn btn-success mt-sm" bindtap="claimOfflineReward">领取奖励</button>
        </view>
      </view>

      <!-- 快捷操作区 -->
      <view class="quick-actions">
        <button class="btn btn-warning btn-small" bindtap="goToNextArea" disabled="{{!canProgress}}">
          下一区域
        </button>
        <button class="btn btn-secondary btn-small" bindtap="openSettings">
          设置
        </button>
      </view>
    </view>
  </view>
</scroll-view>
