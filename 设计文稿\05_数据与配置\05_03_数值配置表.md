# 数值配置表

**文档类型**: 数据设计
**版本号**: v2.0
**创建日期**: 2025-01-05
**最后更新**: 2025-01-05
**维护者**: 数值策划团队
**状态**: 已发布

---

## 📖 文档概述

本文档定义月球RPG的所有核心数值配置，包括角色属性、敌人数据、经济参数等。这是游戏平衡性的核心文档，所有数值实现都应以此为准。

## 🎯 设计目标

1. **统一数值标准**：作为所有数值的唯一权威来源
2. **平衡性保证**：确保游戏各系统数值平衡
3. **易于调整**：提供清晰的数值调整依据
4. **开发指导**：为程序实现提供精确的数值规范

## 📋 相关文档

### 核心关联
- [核心数据结构](./05_02_核心数据结构.md) - 数值的数据结构定义
- [角色属性系统](../03_系统设计/03_04_角色属性系统.md) - 属性计算公式
- [战斗系统](../03_系统设计/战斗系统.md) - 战斗数值应用

### 系统数值
- [装备系统](../03_系统设计/03_01_装备系统.md) - 装备属性数值
- [技能系统](../03_系统设计/03_03_技能系统.md) - 技能效果数值
- [材料系统](../03_系统设计/03_02_材料系统.md) - 材料和制作数值

---

## ⚠️ 重要说明

> **数值标准**: 本文档为所有数值的权威定义，代码实现必须与此保持一致
> **平衡调整**: 所有数值调整都应先在此文档中修改，再同步到代码
> **测试验证**: 数值修改后需要进行平衡性测试验证

---

## 🎮 角色基础数值

### 角色初始属性
根据用户要求，角色初始属性统一为5点：

| 属性名称 | 初始值 | 说明 |
|----------|--------|------|
| **力量** | 5 | 影响攻击力和负重 |
| **敏捷** | 5 | 影响攻击速度和闪避 |
| **体质** | 5 | 影响生命值和抗性 |

### 属性转换公式
基于三属性系统计算战斗属性：

| 战斗属性 | 计算公式 | 示例(初始5/5/5) |
|----------|----------|-----------------|
| **攻击力** | 力量 × 3 + 敏捷 × 1 | 5×3 + 5×1 = 20 |
| **防御力** | 体质 × 2 + 力量 × 1 | 5×2 + 5×1 = 15 |
| **生命值** | 体质 × 15 + 力量 × 2 | 5×15 + 5×2 = 85 |
| **攻击速度** | 基础1.5秒 - (敏捷-5)×0.05秒 | 1.5 - 0×0.05 = 1.5秒 |

### 升级成长
每次升级获得3点属性点，玩家自由分配：

| 等级范围 | 每级属性点 | 累计属性点 |
|----------|------------|------------|
| 1-50级 | 3点 | 147点 |
| 51-100级 | 3点 | 297点 |
| 101-150级 | 3点 | 447点 |

### 经验值需求
使用设计文档中的标准公式：

| 等级 | 经验值需求 | 累计经验 |
|------|------------|----------|
| 1→2 | 108 | 108 |
| 2→3 | 166 | 274 |
| 3→4 | 229 | 503 |
| 10→11 | 675 | 4,635 |
| 50→51 | 11,739 | 约35万 |

**公式**: `(100 + L×50) × (1.08^L)`

---

## 👹 敌人数值配置

### 第一时代敌人 (黑石壁垒周边)
根据地图设计中的剧情设定，第一时代的敌人应该是：

| 敌人名称 | 等级 | 生命值 | 攻击力 | 防御力 | 经验值 | 金币 | 图标 |
|----------|------|--------|--------|--------|--------|------|------|
| **黑水帮探子** | 1 | 45 | 12 | 3 | 15 | 8 | 🗡️ |
| **变异山狼** | 2 | 65 | 16 | 5 | 25 | 12 | 🐺 |
| **狼群哨兵** | 3 | 90 | 20 | 8 | 35 | 18 | 🛡️ |
| **狼群头目** | 5 | 150 | 35 | 15 | 80 | 45 | 👑 |

### 其他区域敌人

| 敌人名称 | 等级 | 生命值 | 攻击力 | 防御力 | 经验值 | 金币 | 图标 |
|----------|------|--------|--------|--------|--------|------|------|
| **森林野猪** | 3 | 85 | 18 | 6 | 30 | 15 | 🐗 |
| **巨型蜘蛛** | 4 | 110 | 25 | 10 | 50 | 25 | 🕷️ |
| **拾荒者斥候** | 4 | 120 | 22 | 12 | 55 | 30 | 🔍 |
| **古树守护者** | 6 | 200 | 40 | 20 | 120 | 60 | 🌳 |

### 敌人属性计算规则
基于等级的敌人属性公式：

| 属性 | 计算公式 | 说明 |
|------|----------|------|
| **生命值** | 基础值 + 等级×8 | 确保有一定挑战性 |
| **攻击力** | 基础值 + 等级×3 | 与玩家攻击力相当 |
| **防御力** | 基础值 + 等级×2 | 略低于玩家防御力 |
| **经验值** | 等级×15 + 随机(0-10) | 保证升级节奏 |
| **金币** | 等级×8 + 随机(0-5) | 经济平衡 |

---

## 💰 经济系统数值

### 基础经济参数

| 参数名称 | 数值 | 说明 |
|----------|------|------|
| **初始金币** | 100 | 新角色起始金币 |
| **死亡惩罚** | 当前金币×10% | 死亡时损失金币 |
| **修理费用** | 装备价值×5% | 装备损坏修理费 |

### 装备价格体系

| 装备品质 | 基础价格 | 价格倍率 | 示例 |
|----------|----------|----------|------|
| **白色(普通)** | 50-200金 | 1.0× | 铁剑 100金 |
| **绿色(优秀)** | 200-800金 | 3.0× | 精铁剑 300金 |
| **蓝色(稀有)** | 800-3000金 | 10× | 寒铁剑 1000金 |
| **紫色(史诗)** | 3000-12000金 | 30× | 玄铁剑 3000金 |
| **橙色(传说)** | 12000-50000金 | 100× | 神铁剑 10000金 |

### 强化费用

| 强化等级 | 费用计算 | 成功率 | 示例(100金装备) |
|----------|----------|--------|-----------------|
| +1 | 装备价值×10% | 90% | 10金 |
| +2 | 装备价值×15% | 85% | 15金 |
| +3 | 装备价值×25% | 80% | 25金 |
| +5 | 装备价值×50% | 70% | 50金 |
| +10 | 装备价值×200% | 50% | 200金 |

---

## 🔧 技能系统数值

### 技能触发率基础值

| 技能类型 | 基础触发率 | 最大触发率 | 提升方式 |
|----------|------------|------------|----------|
| **普通攻击技能** | 15% | 60% | 装备词条+使用次数 |
| **防御技能** | 10% | 45% | 装备词条+受击次数 |
| **特殊技能** | 5% | 30% | 装备词条+特定条件 |

### 技能效果数值

| 效果类型 | 基础效果 | 最大效果 | 持续时间 |
|----------|----------|----------|----------|
| **伤害加成** | +20% | +100% | 即时 |
| **防御加成** | +15% | +75% | 3回合 |
| **生命恢复** | +10% | +50% | 即时 |
| **属性提升** | +2点 | +10点 | 5回合 |

---

## 📊 平衡性验证数据

### 战斗时长预期

| 敌人等级差 | 预期战斗回合 | 预期时间 |
|------------|-------------|----------|
| 同级 | 3-5回合 | 15-25秒 |
| +1级 | 4-7回合 | 20-35秒 |
| +2级 | 6-10回合 | 30-50秒 |
| +3级以上 | 建议避免 | 过于困难 |

### 升级节奏预期

| 等级段 | 预期游戏时间 | 主要活动 |
|--------|-------------|----------|
| 1-10级 | 30-60分钟 | 学习基础操作 |
| 10-30级 | 2-4小时 | 掌握核心机制 |
| 30-50级 | 6-10小时 | 完成第一轮回 |

---

**维护者**: 数值策划团队
**技术支持**: 系统架构师
**平衡调整**: 根据测试数据持续优化