<!--
**【设计目标】**
本设计文档旨在弥补当前剧情框架中的核心缺失环节——轮回历练系统。
该系统是连接玩家在不同时代的【核心抉择】与其【长线后果】的关键桥梁，
旨在将"历史回响"的设定具象化为可玩的、有意义的游戏内容，并以此提升游戏的重玩价值和叙事深度。
-->

# 轮回历练系统设计

### 1. 文档概述

本文档详细阐述了 **轮回历练系统** 的核心机制、内容类型、触发方式，及其与 **历史回响系统**、**地图交互** 和 **玩家成长** 的联动关系。

---

### 2. 核心目标

*   **机制闭环**: 让"历史回响"中的【宿敌烙印】和【先祖庇佑】不再是孤立的文本描述，而是能直接影响游戏体验的核心机制。
*   **内容沉淀**: 为每个时代提供可重复挑战的、与剧情选择紧密关联的后期内容(End-game)。
*   **叙事强化**: 通过"亲身体验"先祖的抉择所带来的影响，加深玩家对家族千年传承的代入感。
*   **玩法延伸**: 提供一个独立于主线之外的成长与挑战轴线，丰富玩家的游戏目标。

---

### 3. 系统入口与地图交互

这是本系统设计的关键，旨在解决"在地图设计中发现的问题"。

*   **入口形式**: 轮回历练的入口 **不是** 一个独立的UI按钮，而是固化在游戏世界地图上的 **【历史回响地标】**。
*   **触发逻辑**:
    1.  当玩家完成一个时代的主线剧情后，该时代对应的【历史回响】建筑会根据玩家的最终选择，永久地出现在世界地图的特定位置。（例如：第四时代结束后，地图上会出现"飞升之塔"或"万法石林"等）。
    2.  这些地标在地图上是可交互的实体。玩家在后续的任何时代，都可以派遣角色前往或"追忆"这些地标。
    3.  与地标交互，即可打开该时代的"轮回历练"界面，开始挑战。

*   **地图设计的意义**:
    *   **历史的见证**: 世界地图本身就成为了一本可视化的编年史，玩家的每一次重大选择，都在地图上留下了不可磨灭的印记。
    *   **直观的交互**: 玩家可以直观地看到自己家族历史的"遗产"，并随时可以回去"体验"这段历史。
    *   **探索的驱动**: 寻找并解锁这些隐藏在世界各地的【历史回响地标】，成为玩家探索地图的长期目标之一。

---

### 4. 历练类型与内容设计

轮回历练分为三大类型，分别对应不同的挑战和奖励，以补全当前缺失的支线任务体系。

#### 4.1 **传承考验 (Test of Legacy)**
*   **内容**: 非战斗型考验，侧重于对家族历史和玩家过往选择的"回顾"。
*   **形式**:
    *   **情景问答**: "你的先祖在面对武仙遗迹时，选择了何种道路？他的决定为后世带来了怎样的'宿敌烙印'？" 错误的回答可能会在接下来的试炼中引来额外的惩罚。
    *   **因果排序**: 打乱某个历史事件的关键节点，让玩家根据记忆重新排序。
    *   **幻境重现**: 让玩家以观察者的身份，重温先祖做出关键抉择时的场景。
*   **目的**: 强化玩家对剧情的记忆，理解自己选择的长远影响。

#### 4.2 **道德抉择 (Test of Morality)**
*   **内容**: 模拟两难情景，考验玩家是否会坚守或背离先祖的"道"。
*   **形式**:
    *   在一个独立的副本环境中，生成一个微缩版的历史危机。例如，在"血脉原罪"的历练中，玩家会再次遇到一个"被污染"的族人，并需要在"处决"与"拯救"之间做出选择。
    *   玩家的选择会与当年先祖的选择进行对比，并产生不同的即时效果。
*   **目的**: 检验玩家的价值观，并为当前轮回提供临时的、与"道"相关的增益或减益效果。

#### 4.3 **技能试炼 (Test of Skill)**
*   **内容**: 核心战斗或策略考验，是【宿敌烙印】发挥作用的主要舞台。
*   **形式**:
    *   **首领挑战**: 对抗一个被【宿敌烙印】强化过的、代表该时代核心矛盾的精英敌人或首领。例如，在"飞升之塔"的历练中，玩家将面对一个拥有"复制技能"能力的"被流放的天才"。
    *   **防守/生存关卡**: 在特定场景下，抵御数波被【宿敌烙行】强化的敌人。
    *   **解谜式战斗**: 需要利用特定机制才能击败的敌人，例如必须优先击破"规则破坏者"的护盾才能对其造成伤害。
*   **目的**: 将"宿敌烙印"带来的负面影响转化为富有挑战性的游戏内容。

---

### 5. 与历史回响的联动机制

*   **宿敌烙印 (Enemy Imprint)**:
    *   玩家在主线剧情中做出的选择，会累积不同的【宿敌烙印】。
    *   在进行"技能试炼"时，当前拥有的所有【宿敌烙印】都会被激活，并以词缀(Modifier)的形式附加给敌人，极大地改变敌人的行为模式和强度。
    *   这使得每一次轮回的战斗体验都因玩家的选择历史而变得独一无二。

*   **先祖庇佑 (Ancestral Blessing)**:
    *   **首次完成** 某个时代的"轮回历练"（通常是完成该时代所有的传承、道德和技能试炼），即可为本次轮回 **激活或升级** 对应的【先祖庇佑】。
    *   【先祖庇佑】是强大的被动增益，旨在平衡【宿敌烙印】带来的难度提升，并奖励深度体验游戏的玩家。
    *   庇佑效果在新的轮回开始时会重置，需要再次完成历练来激活，从而构成了游戏的核心循环。

---

### 6. 奖励机制

*   **核心奖励**:
    *   激活/升级【先祖庇佑】（每个时代在每次轮回中仅限一次）。
*   **次级奖励** (可重复获取):
    *   与该时代主题相关的稀有材料或装备。
    *   "传承点数"，可用于解锁通用的家族天赋树。
    *   解锁关于该时代更深层次背景故事的"历史碎片"。
*   **特殊奖励**:
    *   首次集齐所有九个时代的【先祖庇佑】，将解锁终极试炼的挑战资格。

---

### 7. 设计风险与平衡性考量

*   **难度叠加**: 必须谨慎设计【宿敌烙印】的叠加效应，避免在游戏后期因烙印过多导致难度失控。可考虑设置一个"烙印激活上限"，让玩家在挑战前选择激活哪些烙印。
*   **内容重复**: 需要确保每个时代的历练都有其独特的玩法和敌人机制，避免沦为单纯的数值填充。
*   **玩家引导**: 需在游戏中设置清晰的引导，让玩家理解"历史回响地标"的作用，以及"轮回历练"与其自身选择的关联。 