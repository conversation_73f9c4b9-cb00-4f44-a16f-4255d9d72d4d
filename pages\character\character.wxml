<view class="page-container">
  <!-- 角色基本信息 -->
  <view class="character-profile card">
    <view class="card-header">
      <text class="card-title">角色信息</text>
    </view>
    <view class="card-content">
      <view class="profile-main">
        <view class="character-avatar">
          <text class="clan-emblem-large">{{characterData.clanEmblem}}</text>
        </view>
        <view class="character-info">
          <text class="character-name">{{characterData.name}}</text>
          <text class="character-title">{{characterData.title}}</text>
          <text class="clan-name">{{characterData.clanName}} 族</text>
          <view class="level-info">
            <text class="level-text">等级 {{characterData.level}}</text>
            <view class="exp-bar">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{expPercent}}%"></view>
              </view>
              <text class="exp-text">{{characterData.experience}}/{{characterData.nextLevelExp}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 角色属性 -->
  <view class="character-stats card">
    <view class="card-header">
      <text class="card-title">角色属性</text>
    </view>
    <view class="card-content">
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-label">攻击力</text>
          <text class="stat-value text-primary">{{characterData.attack}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">防御力</text>
          <text class="stat-value text-secondary">{{characterData.defense}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">生命值</text>
          <text class="stat-value text-success">{{characterData.maxHp}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">暴击率</text>
          <text class="stat-value text-warning">{{characterData.critRate}}%</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">攻击速度</text>
          <text class="stat-value">{{characterData.attackSpeed}}</text>
        </view>
        <view class="stat-item">
          <text class="stat-label">每秒伤害</text>
          <text class="stat-value text-danger">{{characterData.dps}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 家族传承 -->
  <view class="clan-heritage card">
    <view class="card-header">
      <text class="card-title">家族传承</text>
      <button class="btn btn-small" bindtap="editClanInfo">编辑</button>
    </view>
    <view class="card-content">
      <view class="heritage-item">
        <text class="heritage-label">族名</text>
        <text class="heritage-value">{{characterData.clanName}}</text>
      </view>
      <view class="heritage-item">
        <text class="heritage-label">族徽</text>
        <text class="heritage-emblem">{{characterData.clanEmblem}}</text>
      </view>
      <view class="heritage-item">
        <text class="heritage-label">家族等级</text>
        <text class="heritage-value">Lv.{{characterData.clanLevel}}</text>
      </view>
      <view class="heritage-item">
        <text class="heritage-label">传承点数</text>
        <text class="heritage-value text-warning">{{characterData.heritagePoints}}</text>
      </view>
    </view>
  </view>

  <!-- 已装备称号 -->
  <view class="equipped-titles card">
    <view class="card-header">
      <text class="card-title">称号</text>
      <button class="btn btn-small" bindtap="manageTitles">管理</button>
    </view>
    <view class="card-content">
      <view class="title-list">
        <view class="title-item {{title.equipped ? 'equipped' : ''}}" 
              wx:for="{{titles}}" wx:key="id"
              bindtap="toggleTitle" data-id="{{title.id}}">
          <text class="title-name">{{title.name}}</text>
          <text class="title-effect">{{title.effect}}</text>
          <view class="title-stats" wx:if="{{title.stats}}">
            <text class="title-stat" wx:for="{{title.stats}}" wx:key="type">
              +{{item.value}} {{item.name}}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 资源信息 -->
  <view class="resources-info card">
    <view class="card-header">
      <text class="card-title">资源</text>
    </view>
    <view class="card-content">
      <view class="resource-grid">
        <view class="resource-item">
          <text class="resource-icon">💰</text>
          <view class="resource-details">
            <text class="resource-name">金币</text>
            <text class="resource-amount">{{characterData.gold}}</text>
          </view>
        </view>
        <view class="resource-item">
          <text class="resource-icon">⚡</text>
          <view class="resource-details">
            <text class="resource-name">能量</text>
            <text class="resource-amount">{{characterData.energy}}/{{characterData.maxEnergy}}</text>
          </view>
        </view>
        <view class="resource-item">
          <text class="resource-icon">💎</text>
          <view class="resource-details">
            <text class="resource-name">钻石</text>
            <text class="resource-amount">{{characterData.diamond}}</text>
          </view>
        </view>
        <view class="resource-item">
          <text class="resource-icon">🔮</text>
          <view class="resource-details">
            <text class="resource-name">技能点</text>
            <text class="resource-amount">{{characterData.skillPoints}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 游戏统计 -->
  <view class="game-stats card">
    <view class="card-header">
      <text class="card-title">游戏统计</text>
    </view>
    <view class="card-content">
      <view class="stats-list">
        <view class="stat-row">
          <text class="stat-label">游戏时间</text>
          <text class="stat-value">{{gameStats.playTime}}</text>
        </view>
        <view class="stat-row">
          <text class="stat-label">击败敌人</text>
          <text class="stat-value">{{gameStats.enemiesKilled}}</text>
        </view>
        <view class="stat-row">
          <text class="stat-label">获得金币</text>
          <text class="stat-value">{{gameStats.goldEarned}}</text>
        </view>
        <view class="stat-row">
          <text class="stat-label">装备获得</text>
          <text class="stat-value">{{gameStats.equipmentFound}}</text>
        </view>
        <view class="stat-row">
          <text class="stat-label">技能释放</text>
          <text class="stat-value">{{gameStats.skillsUsed}}</text>
        </view>
      </view>
    </view>
  </view>
</view> 