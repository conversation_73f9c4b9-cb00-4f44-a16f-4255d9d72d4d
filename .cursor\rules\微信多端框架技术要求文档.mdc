---
description: 
globs: 
alwaysApply: true
---
---
description: 微信多端框架技术要求和开发规范文档，包含环境要求、包体限制、性能规范、API使用规范等
globs: 
  - "*.json"
  - "*.js" 
  - "*.wxml"
  - "*.wxss"
  - "*.ts"
  - "project.config.json"
  - "project.miniapp.json"
  - "app.json"
  - "app.miniapp.json"
alwaysApply: true
---

# 微信多端框架技术要求文档

## 📋 文档概述

本文档详细阐述了微信多端框架的技术要求、开发规范、包体限制和兼容性要求，适用于基于小程序技术的多端应用开发。

---

## 🛠️ 开发环境要求

### 1. 基础环境要求

#### 1.1 开发工具版本
- **微信开发者工具**: ≥ 1.06.2306272 的开发版
- **Node.js**: ≥ 16.0.0 (推荐 18.x LTS)
- **npm/yarn**: 最新稳定版本

#### 1.2 操作系统兼容性
- **Windows**: Windows 10 及以上
- **macOS**: macOS 10.14 及以上  
- **Linux**: Ubuntu 18.04 LTS 及以上

#### 1.3 硬件要求
- **内存**: 最少 8GB，推荐 16GB
- **存储空间**: 至少 5GB 可用空间
- **网络**: 稳定的互联网连接

### 2. 项目配置要求

#### 2.1 项目架构设置
```json
{
  "projectArchitecture": "multiPlatform",
  "renderer": "skyline",
  "componentFramework": "glass-easel",
  "lazyCodeLoading": "requiredComponents"
}
```

#### 2.2 必需配置文件
- `project.config.json` - 项目基础配置
- `project.miniapp.json` - 多端应用配置
- `app.json` - 应用全局配置
- `app.miniapp.json` - 多端应用适配配置

---

## 📱 多端支持规范

### 1. 支持平台
- **小程序端**: 微信小程序
- **Android 端**: API 24+ (Android 7.0+)
- **iOS 端**: iOS 12.0+
- **鸿蒙 端**: HarmonyOS 3.0+

### 2. SDK 版本要求

#### 2.1 Android 配置
```json
{
  "mini-android": {
    "sdkVersion": "1.6.14",
    "toolkitVersion": "0.11.0",
    "useExtendedSdk": {
      "media": false,
      "bluetooth": false,
      "network": false,
      "scanner": false,
      "xweb": false
    }
  }
}
```

#### 2.2 iOS 配置  
```json
{
  "mini-ios": {
    "sdkVersion": "1.6.15",
    "toolkitVersion": "0.0.9",
    "useExtendedSdk": {
      "WeAppOpenFuns": true,
      "WeAppNetwork": false,
      "WeAppBluetooth": false,
      "WeAppMedia": false,
      "WeAppLBS": false,
      "WeAppOthers": false
    }
  }
}
```

---

## 📦 包体大小限制

### 1. 基础限制规范

| 类型 | 大小限制 | 备注 |
|------|----------|------|
| **总代码包** | ≤ 30MB | 包含主包和所有分包 |
| **主包** | ≤ 4MB | 启动必需资源 |
| **单个分包** | 无明确限制 | 建议合理控制 |
| **插件包** | ≤ 4MB | 第三方插件包体积 |

### 2. 分包策略

#### 2.1 推荐分包结构
```json
{
  "pages": ["pages/index/index"],
  "subpackages": [
    {
      "root": "packages/game",
      "pages": ["battle", "shop", "inventory"],
      "independent": false
    },
    {
      "root": "packages/social", 
      "pages": ["friends", "guild"],
      "independent": true
    }
  ],
  "preloadRule": {
    "pages/index/index": {
      "network": "all",
      "packages": ["game"]
    }
  }
}
```

#### 2.2 分包类型说明
- **普通分包**: 依赖主包，按需下载
- **独立分包**: 可独立运行，无需主包
- **分包预下载**: 提前下载提升体验

### 3. 资源优化要求

#### 3.1 图片资源
- **格式**: 优先使用 WebP，fallback PNG/JPEG
- **尺寸**: 图片宽×高 ≤ 实际显示宽×高 × (设备像素比)²
- **压缩**: 启用图片压缩，质量80-90%
- **CDN**: 大图片使用 CDN，代码包仅包含小图标

#### 3.2 音频资源
- **格式**: MP3 (iOS), AAC (Android)
- **码率**: 背景音乐 ≤ 128kbps，音效 ≤ 64kbps
- **时长**: 代码包内音频 ≤ 30秒

#### 3.3 支持文件类型
```text
// 图片类型
png, jpg, jpeg, gif, svg, bmp, webp

// 音频类型  
mp3, wav, m4a, aac, ogg

// 代码类型
js, json, wxml, wxss, ts

// 其他类型
ttf, woff, woff2, txt, xml, csv
```

---

## 🔧 技术架构要求

### 1. 渲染引擎
- **默认引擎**: Skyline 渲染引擎
- **组件框架**: glass-easel
- **向下兼容**: 支持 WebView 渲染 fallback

### 2. 基础库要求
- **最低版本**: 3.0.0
- **推荐版本**: 最新稳定版
- **兼容策略**: 设置最低基础库版本阈值

### 3. JavaScript 支持
- **语法支持**: ES6+（通过转译支持）
- **模块化**: CommonJS / ES Modules
- **TypeScript**: 支持（推荐使用）

---

## 📝 代码编写规范

### 1. 项目结构规范

#### 1.1 目录结构要求
```
project/
├── components/          # 通用组件
│   ├── ui/             # UI组件
│   └── business/       # 业务组件
├── pages/              # 页面文件
│   ├── index/         # 首页
│   ├── game/          # 游戏相关页面
│   └── profile/       # 用户相关页面
├── utils/              # 工具函数
├── api/                # API接口
├── assets/             # 静态资源
│   ├── images/        # 图片资源
│   ├── audio/         # 音频资源
│   └── icons/         # 图标资源
├── config/             # 配置文件
├── data/               # 数据文件
└── miniapp/            # 多端应用特定文件
    ├── android/       # Android 平台
    └── ios/           # iOS 平台
```

#### 1.2 文件命名规范
- **页面文件**: 使用 kebab-case，如 `user-profile/`
- **组件文件**: 使用 PascalCase，如 `GameButton.js`
- **工具文件**: 使用 camelCase，如 `formatUtils.js`
- **配置文件**: 使用 camelCase，如 `gameConfig.js`

### 2. JavaScript 编码规范

#### 2.1 变量命名
```javascript
// 常量：全大写+下划线
const MAX_PLAYER_LEVEL = 100;
const GAME_CONFIG = {
  version: '1.0.0'
};

// 变量：小驼峰
let playerScore = 0;
let gameIsRunning = false;

// 私有变量：下划线前缀
let _internalState = {};

// 函数：动词开头的小驼峰
function calculateDamage(attack, defense) {
  return Math.max(1, attack - defense);
}

// 类：大驼峰
class GameManager {
  constructor() {
    this._players = [];
  }
}
```

#### 2.2 代码格式规范
```javascript
// 1. 使用2个空格缩进
if (condition) {
  doSomething();
} else {
  doSomethingElse();
}

// 2. 对象和数组格式
const gameSettings = {
  difficulty: 'normal',
  soundEnabled: true,
  graphics: 'high'
};

const playerActions = [
  'attack',
  'defend', 
  'heal'
];

// 3. 函数参数换行（超过3个参数）
function createCharacter(
  name,
  level,
  attributes,
  equipment,
  skills
) {
  // 实现代码
}
```

#### 2.3 注释规范
```javascript
/**
 * 计算战斗伤害
 * @param {number} attack - 攻击力
 * @param {number} defense - 防御力
 * @param {number} [criticalRate=0] - 暴击率 (0-1)
 * @returns {number} 最终伤害值
 */
function calculateDamage(attack, defense, criticalRate = 0) {
  // 基础伤害计算
  let baseDamage = Math.max(1, attack - defense);
  
  // 暴击判定
  if (Math.random() < criticalRate) {
    baseDamage *= 2; // 暴击伤害翻倍
  }
  
  return Math.floor(baseDamage);
}

// 单行注释：解释复杂逻辑
const isValidMove = playerX >= 0 && playerX < MAP_WIDTH; // 检查移动是否在地图范围内
```

### 3. 小程序特定编码规范

#### 3.1 页面生命周期规范
```javascript
Page({
  data: {
    // 页面数据定义在顶部
    playerInfo: null,
    gameStatus: 'loading'
  },

  // 生命周期函数按顺序排列
  onLoad(options) {
    // 页面加载时执行
    this.initPageData(options);
  },

  onReady() {
    // 页面初次渲染完成
    this.startGame();
  },

  onShow() {
    // 页面显示时执行
    this.resumeGame();
  },

  onHide() {
    // 页面隐藏时执行
    this.pauseGame();
  },

  onUnload() {
    // 页面卸载时执行
    this.saveGameData();
  },

  // 自定义方法放在生命周期后面
  initPageData(options) {
    // 初始化页面数据
  },

  startGame() {
    // 开始游戏逻辑
  }
});
```

#### 3.2 组件编写规范
```javascript
Component({
  // 组件选项
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },

  // 组件属性
  properties: {
    title: {
      type: String,
      value: '默认标题'
    },
    disabled: {
      type: Boolean,
      value: false
    }
  },

  // 组件数据
  data: {
    internalState: false
  },

  // 组件生命周期
  lifetimes: {
    attached() {
      // 组件实例被放到页面节点树上时执行
    },
    
    detached() {
      // 组件实例被从页面节点树移除时执行
    }
  },

  // 组件方法
  methods: {
    onTap() {
      // 处理点击事件
      this.triggerEvent('tap', {
        value: this.data.title
      });
    }
  }
});
```

### 4. WXML 模板规范

#### 4.1 模板结构规范
```xml
<!-- 使用语义化的标签和类名 -->
<view class="game-container">
  <!-- 游戏界面头部 -->
  <view class="game-header">
    <text class="player-name">{{playerInfo.name}}</text>
    <text class="player-level">Lv.{{playerInfo.level}}</text>
  </view>

  <!-- 游戏主体内容 -->
  <view class="game-content">
    <!-- 使用条件渲染 -->
    <view wx:if="{{gameStatus === 'playing'}}" class="game-playing">
      <!-- 游戏进行中的界面 -->
    </view>
    
    <view wx:elif="{{gameStatus === 'paused'}}" class="game-paused">
      <!-- 游戏暂停时的界面 -->
    </view>
    
    <view wx:else class="game-loading">
      <!-- 游戏加载中的界面 -->
    </view>
  </view>

  <!-- 游戏底部操作区 -->
  <view class="game-footer">
    <button
      wx:for="{{actionButtons}}"
      wx:key="id"
      class="action-btn {{item.disabled ? 'disabled' : ''}}"
      bindtap="onActionTap"
      data-action="{{item.action}}"
    >
      {{item.label}}
    </button>
  </view>
</view>
```

#### 4.2 数据绑定规范
```xml
<!-- 正确的数据绑定 -->
<view class="player-stats">
  <!-- 文本绑定 -->
  <text>血量: {{playerInfo.hp}}/{{playerInfo.maxHp}}</text>
  
  <!-- 属性绑定 -->
  <progress 
    percent="{{playerInfo.hp / playerInfo.maxHp * 100}}"
    stroke-width="8"
    activeColor="#ff0000"
  />
  
  <!-- 事件绑定 -->
  <button bindtap="onAttackTap" data-target="{{currentEnemy.id}}">
    攻击
  </button>
</view>

<!-- 列表渲染 -->
<view class="inventory-list">
  <view 
    wx:for="{{inventory}}" 
    wx:key="id"
    class="inventory-item {{item.equipped ? 'equipped' : ''}}"
  >
    <image src="{{item.icon}}" class="item-icon" />
    <text class="item-name">{{item.name}}</text>
    <text class="item-count">×{{item.count}}</text>
  </view>
</view>
```

### 5. WXSS 样式规范

#### 5.1 样式组织规范
```css
/* 1. 全局样式变量 */
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --text-color: #000000;
  --text-color-secondary: #666666;
  --border-radius: 8rpx;
  --spacing-small: 16rpx;
  --spacing-medium: 32rpx;
  --spacing-large: 48rpx;
}

/* 2. 页面容器样式 */
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: var(--spacing-medium);
}

/* 3. 组件样式 */
.game-button {
  padding: var(--spacing-small) var(--spacing-medium);
  border-radius: var(--border-radius);
  background-color: var(--primary-color);
  color: #ffffff;
  border: none;
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.game-button:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.game-button.disabled {
  background-color: #d9d9d9;
  color: #999999;
}

/* 4. 响应式设计 */
@media (max-width: 375px) {
  .game-container {
    padding: var(--spacing-small);
  }
}
```

#### 5.2 单位使用规范
```css
/* 推荐使用 rpx 进行屏幕适配 */
.container {
  width: 750rpx;          /* 全屏宽度 */
  padding: 30rpx;         /* 内边距 */
  margin: 20rpx auto;     /* 外边距 */
  font-size: 28rpx;       /* 字体大小 */
}

/* 特殊情况使用固定单位 */
.border {
  border-width: 1px;      /* 边框宽度使用 px */
}

.precise-layout {
  width: 100%;            /* 百分比用于特殊布局 */
  height: 50vh;           /* 视口单位用于特殊需求 */
}
```

### 6. 错误处理规范

#### 6.1 统一错误处理
```javascript
// 全局错误处理
App({
  onError(error) {
    console.error('应用错误:', error);
    
    // 错误上报
    this.reportError(error);
    
    // 用户友好提示
    wx.showToast({
      title: '出现了一些问题',
      icon: 'none',
      duration: 2000
    });
  },

  reportError(error) {
    // 上报错误到服务器
    wx.request({
      url: 'https://api.example.com/error-report',
      method: 'POST',
      data: {
        error: error.toString(),
        stack: error.stack,
        timestamp: Date.now(),
        version: this.globalData.version
      }
    });
  }
});

// 页面级错误处理
Page({
  data: {
    isLoading: false,
    errorMessage: ''
  },

  async loadGameData() {
    try {
      this.setData({ isLoading: true, errorMessage: '' });
      
      const gameData = await this.fetchGameData();
      this.setData({ gameData });
      
    } catch (error) {
      console.error('加载游戏数据失败:', error);
      
      this.setData({
        errorMessage: '加载失败，请稍后重试'
      });
      
      // 提供重试机制
      this.showRetryOption();
      
    } finally {
      this.setData({ isLoading: false });
    }
  }
});
```

### 7. 性能优化规范

#### 7.1 数据更新优化
```javascript
Page({
  // 批量更新数据
  updatePlayerStats(newStats) {
    this.setData({
      'playerInfo.hp': newStats.hp,
      'playerInfo.mp': newStats.mp,
      'playerInfo.exp': newStats.exp
    });
  },

  // 避免频繁setData
  onPlayerMove(e) {
    // 使用节流处理频繁事件
    if (this.moveTimer) return;
    
    this.moveTimer = setTimeout(() => {
      this.updatePlayerPosition(e.detail);
      this.moveTimer = null;
    }, 16); // 约60fps
  },

  // 大数据列表优化
  loadMoreItems() {
    const currentItems = this.data.itemList;
    const newItems = this.fetchMoreItems();
    
    // 分批加载，避免一次性加载大量数据
    this.setData({
      itemList: currentItems.concat(newItems.slice(0, 20))
    });
  }
});
```

### 8. 代码质量保证

#### 8.1 ESLint 配置
```javascript
// .eslintrc.js
module.exports = {
  env: {
    browser: true,
    es6: true,
    node: true
  },
  globals: {
    wx: true,
    App: true,
    Page: true,
    Component: true,
    getApp: true,
    getCurrentPages: true
  },
  extends: ['eslint:recommended'],
  rules: {
    'no-console': 'warn',
    'no-unused-vars': 'error',
    'prefer-const': 'error',
    'no-var': 'error'
  }
};
```

#### 8.2 代码检查规范
- **提交前检查**: 使用 pre-commit hooks 进行代码检查
- **代码审查**: 所有代码变更必须经过同行审查
- **自动化测试**: 编写单元测试覆盖核心逻辑

---

## ⚡ 性能要求规范

### 1. 启动性能指标

| 平台 | 下载启动时间 | 本地启动时间 |
|------|-------------|-------------|
| **Android** | ≤ 3.7s | ≤ 2.6s |
| **iOS** | ≤ 1.8s | ≤ 0.9s |

### 2. 运行时性能指标

| 指标 | 要求 | 备注 |
|------|------|------|
| **首屏渲染** | ≤ 5s | 从启动到首屏内容显示 |
| **页面渲染** | ≤ 500ms | 单次渲染操作耗时 |
| **脚本执行** | ≤ 1s | 同步脚本执行时间 |
| **setData频率** | ≤ 20次/秒 | 数据更新频率限制 |
| **setData大小** | ≤ 256KB | 单次数据传输大小 |

### 3. 界面性能要求

#### 3.1 WXML 节点限制
- **节点总数**: < 1000个/页面
- **节点深度**: < 30层
- **子节点数**: ≤ 60个/节点

#### 3.2 网络性能
- **请求响应**: ≤ 1s
- **并发请求**: ≤ 10个（超过300ms的请求）
- **图片并发**: ≤ 6个/域名（超过100ms的请求）

---

## 🔌 API 使用规范

### 1. 多端 API 兼容性

#### 1.1 通用 API
```javascript
// 网络请求
wx.request()
wx.uploadFile()
wx.downloadFile()

// 数据存储
wx.setStorage()
wx.getStorage()
wx.removeStorage()

// 界面交互
wx.showToast()
wx.showModal()
wx.showActionSheet()
```

#### 1.2 平台特定 API
```javascript
// 多端分享功能
if (wx.miniapp) {
  // 多端应用环境
  wx.miniapp.shareImageMessage()
  wx.miniapp.shareTextMessage()
  wx.miniapp.shareWebPageMessage()
} else {
  // 小程序环境
  wx.shareAppMessage()
}
```

### 2. API 调用规范

#### 2.1 错误处理
```javascript
wx.request({
  url: 'https://api.example.com/data',
  success(res) {
    if (res.statusCode === 200) {
      // 处理成功响应
    } else {
      // 处理业务错误
    }
  },
  fail(err) {
    // 处理网络错误
    console.error('请求失败:', err)
  }
})
```

#### 2.2 版本兼容性检查
```javascript
// 检查 API 支持
if (wx.canIUse('getLocation')) {
  wx.getLocation({
    type: 'gcj02',
    success: function(res) {
      // 处理位置信息
    }
  })
} else {
  // 提供降级方案
}

// 检查基础库版本
const version = wx.getAppBaseInfo().SDKVersion
if (compareVersion(version, '2.10.0') >= 0) {
  // 使用新 API
} else {
  // 使用旧 API 或降级方案
}
```

### 3. 调用频率限制

| API 类型 | 频率限制 | 说明 |
|----------|----------|------|
| **网络请求** | 20次/秒 | 单个小程序实例 |
| **文件上传** | 10次/分钟 | 单个小程序实例 |
| **位置接口** | 5次/秒 | 防止频繁调用 |
| **支付接口** | 1次/秒 | 安全限制 |

---

## 🛡️ 安全与隐私要求

### 1. 数据安全
- **HTTPS**: 所有网络请求必须使用 HTTPS
- **域名白名单**: 配置合法域名列表
- **数据加密**: 敏感数据本地加密存储

### 2. 隐私保护
- **权限申请**: 仅申请必要权限
- **用户同意**: 明确获得用户授权
- **数据最小化**: 只收集必要的用户数据

### 3. 内容安全
- **内容审核**: 用户生成内容需要审核
- **敏感词过滤**: 实现关键词过滤机制
- **举报机制**: 提供用户举报功能

---

## 🧪 测试与调试要求

### 1. 兼容性测试

#### 1.1 设备测试覆盖
- **Android**: 主流机型（华为、小米、OPPO、VIVO）
- **iOS**: iPhone 8+ 及 iPad（第6代+）
- **屏幕适配**: 不同分辨率和屏幕比例

#### 1.2 版本测试
- **基础库版本**: 最低支持版本到最新版本
- **微信版本**: 近3个版本的微信客户端

### 2. 性能监控

#### 2.1 关键指标监控
```javascript
// 启动时间监控
const startTime = Date.now()
App({
  onLaunch() {
    const launchTime = Date.now() - startTime
    console.log('启动耗时:', launchTime)
  }
})

// 页面性能监控
Page({
  onReady() {
    const performance = wx.getPerformance()
    const timing = performance.timing
    console.log('页面渲染耗时:', timing.domComplete - timing.navigationStart)
  }
})
```

### 3. 错误监控
```javascript
// 全局错误监控
App({
  onError(error) {
    console.error('全局错误:', error)
    // 上报错误日志
    wx.request({
      url: 'https://api.example.com/error',
      method: 'POST',
      data: {
        error: error,
        timestamp: Date.now(),
        version: wx.getAppBaseInfo().version
      }
    })
  }
})
```

---

## 📋 发布与审核要求

### 1. 发布前检查清单

#### 1.1 功能完整性
- [ ] 核心功能正常运行
- [ ] 所有页面可正常访问
- [ ] 异常情况处理完善
- [ ] 用户反馈机制完整

#### 1.2 性能指标
- [ ] 启动时间符合要求
- [ ] 包体大小控制合理
- [ ] 内存使用优化
- [ ] 电池消耗测试通过

#### 1.3 兼容性验证
- [ ] 多设备适配正常
- [ ] 不同网络环境测试
- [ ] 低端设备性能验证
- [ ] 基础库版本兼容

### 2. 审核规范遵循

#### 2.1 内容规范
- 遵守微信小程序平台规则
- 内容健康积极，无违法违规信息
- 功能描述准确，无虚假宣传

#### 2.2 技术规范
- 代码无恶意逻辑
- 无滥用系统资源
- 无破坏用户体验的行为

---

## 🔄 更新与维护要求

### 1. 版本管理
- **语义化版本**: 采用 Major.Minor.Patch 格式
- **向下兼容**: 确保升级不破坏现有功能
- **灰度发布**: 重要更新采用灰度发布策略

### 2. 持续优化
- **性能监控**: 持续监控关键性能指标
- **用户反馈**: 及时响应用户问题和建议
- **技术更新**: 跟进微信平台技术更新

---

## 📞 技术支持

### 官方文档
- [微信小程序官方文档](mdc:https:/developers.weixin.qq.com/miniprogram/dev/framework)
- [多端应用开发指南](mdc:https:/developers.weixin.qq.com/doc/oplatform/Mobile_App)

### 社区资源
- 微信开放社区
- GitHub 开源项目
- 技术博客和教程

---

## 📝 附录

### A. 常见问题解决方案
- 包体积超限问题
- 性能优化技巧
- 兼容性问题处理
- API 调用异常处理

### B. 最佳实践示例
- 项目结构设计
- 代码规范模板
- 性能优化案例
- 错误处理模式

---

**文档版本**: v1.0  
**最后更新**: 2024年12月19日  
**维护团队**: 月球(Yueqiu)开发团队 