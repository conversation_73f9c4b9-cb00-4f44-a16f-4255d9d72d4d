# 💎 装备系统设计

> [!NOTE]
> **数值待定**: 本文档中所有具体数值（如属性加成、掉率等）均为设计初稿，待核心战斗公式确定后将进行统一调整。
> **系统解锁**: 游戏中的装备相关系统（如强化、词条等）将随玩家游戏进程逐步解锁，不会在初期一次性对玩家开放。

---

## 📁 关联文档
- **[世界观](./剧情系统/世界观.md)** - 东方/西方装备的世界观背景，九个时代的装备风格演进
- **[技能系统设计](./技能系统设计.md)** - 装备属性如何影响技能触发和伤害计算
- **[装备词条系统设计](./装备词条系统设计.md)** - 装备词条的深度机制设计
- **[材料系统设计](./材料系统设计.md)** - 装备强化和打造所需的完整材料体系
- **[地图系统设计](./地图系统/地图系统设计.md)** - 装备掉落机制和轮回传承机制
- **[副职业系统设计](./副职业系统设计.md)** - 铁匠职业的装备制作和强化
- **[家族天赋树系统设计](./家族天赋树系统设计.md)** - 工艺传承天赋树对装备的加成

## 📝 文档内容说明
本文档定义了装备系统的完整机制，包括6个装备部位、品质体系、强化系统、套装效果、东西方装备差异等。重点说明了装备数值如何转换为战斗力，装备词条系统的基础框架，以及装备与技能系统的对接机制。

---

# 月球(Yueqiu) - 装备系统设计文档

## 📍 文档概述
本文档详细描述月球RPG游戏的装备系统设计，包括装备分类、词条系统、套装系统、强化打造、掉落机制以及技术实现方案。

---

## ⚔️ 装备系统总览

### 🎯 设计理念
装备系统采用"精简而深度"的设计理念，通过有限的装备部位和丰富的词条系统，创造出多样化的装备搭配可能性。结合东西方文明冲突的世界观，为玩家提供策略性的装备选择。

### 📊 核心参数
- **装备部位**: 6个核心部位
- **品质等级**: 6个品质阶段，每级提升20%基础属性
- **词条上限**: 最多10条词条（红色装备）
- **套装激活**: 2/4/6件套激活条件
- **强化等级**: 最高+10强化，总属性翻倍

---

## 🏗️ 装备分类系统

### ⚔️ 装备部位设计 (6个核心部位)

#### 1. 武器 (攻击核心)
**基础属性**: 攻击力为主导
- **剑类**: 平衡型武器，攻击力+物理穿透
- **刀类**: 爆发型武器，攻击力+暴击伤害
- **枪类**: 连击型武器，攻击力+攻击速度
- **法宝**: 体质型武器，攻击力+攻击穿透
- **拳套**: 技巧型武器，攻击力+连击率

**专属词条**:
- 武器伤害加成 (10%-50%)
- 技能伤害加成 (15%-60%)
- 暴击率加成 (2%-12%)
- 攻击速度加成 (5%-25%)
- 武器特殊效果 (击杀回血、连击触发等)

**特殊装备示例**:
- **名称**: 矿工领袖的战锤
- **品质**: 蓝色 (精良)
- **获取**: 第三时代"督战队"支线，在[决斗]中战胜矿工领袖。
- **描述**: 一柄沉重的、经过粗糙改装的采矿锤，锤头上还残留着岩石的碎屑和淡淡的血腥味。它象征着来自底层最纯粹的固执与力量。
- **词条示例**:
    1. 攻击力 +35
    2. 对"人形"单位伤害 +10%
    3. 5%几率在攻击时造成"眩晕"

#### 2. 护甲 (防御核心)
**基础属性**: 防御力和生命值
- **轻甲**: 高闪避，低防御，+敏捷加成
- **重甲**: 高防御，低敏捷，+体质加成
- **道袍**: 全抗性，+体质
- **皮甲**: 平衡防御，+暴击抗性

**专属词条**:
- 物理防御加成 (10%-40%)
- 法术抗性加成 (10%-40%)
- 生命值加成 (15%-60%)
- 闪避率加成 (2%-15%)
- 受击特效 (反弹伤害、护盾生成等)

#### 3. 头盔 (精神核心)
**基础属性**: 平衡各属性，提供特殊抗性
- **战盔**: +力量，物理抗性
- **道冠**: +体质，攻击力
- **轻盔**: +敏捷，暴击率
- **护额**: +体质，生命恢复

**专属词条**:
- 全属性加成 (2%-10%)
- 技能触发率加成 (1%-15%)
- 状态抗性 (眩晕、沉默、减速等)
- 精神类特效 (经验加成、技能间隔等)

#### 4. 护手 (技巧核心)
**基础属性**: 攻击辅助和技能增强
- **战手**: +攻击力，握持加成
- **修炼手套**: +体质，攻击加成
- **巧手**: +敏捷，精准加成
- **护拳**: +防御，格挡加成

**专属词条**:
- 攻击精度加成 (5%-25%)
- 技能效果加成 (10%-45%)
- 格挡几率 (3%-18%)
- 手部特效 (连击、技能链等)

#### 5. 腰带 (资源核心)
**基础属性**: 资源管理和持续效果
- **力量腰带**: +力量，负重加成
- **敏捷腰带**: +敏捷，移动加成
- **修炼腰带**: +体质，攻击相关
- **生命腰带**: +体质，恢复加成

**专属词条**:
- 资源恢复加成 (生命恢复等)
- 持续效果增强 (DOT、HOT时间延长)
- 背包容量加成
- 腰带特效 (额外收益、资源获取等)

#### 6. 靴子 (机动核心)
**基础属性**: 机动性和逃脱能力
- **战靴**: +移动力，冲锋相关
- **轻靴**: +敏捷，闪避相关
- **修炼靴**: +体质，攻击机动
- **重靴**: +防御，稳定相关

**专属词条**:
- 移动速度加成 (10%-40%)
- 闪避几率加成 (3%-20%)
- 踩踏特效 (震慑、减速敌人等)
- 逃脱能力 (免疫某些控制)

---

## 🎯 品质等级系统

### 📊 品质分级与属性
| 品质 | 颜色 | 基础属性倍率 | 词条数量 | 获取方式 | 特殊说明 |
|------|------|--------------|----------|----------|----------|
| 普通 | 白色 | 100% | 0-2条 | 怪物掉落 | 基础装备，过渡使用 |
| 优秀 | 绿色 | 120% | 1-3条 | 怪物掉落、打造 | 初期主力装备 |
| 精良 | 蓝色 | 144% | 2-4条 | 怪物掉落、打造 | 中期核心装备 |
| 史诗 | 紫色 | 173% | 3-6条 | 怪物掉落、打造 | 高级装备，稀有 |
| 传说 | 橙色 | 207% | 4-8条 | Boss掉落、高级打造 | 极品装备，罕见 |
| 神器 | 红色 | 249% | 6-10条 | 玩家打造专属 | 最强装备，唯一途径 |

### 🔨 品质影响因素
- **基础属性**: 每个品质提升20%基础数值
- **词条数量**: 高品质装备拥有更多词条槽位
- **词条品质**: 高品质装备的词条数值范围更大
- **特殊效果**: 紫色以上装备可能拥有独特特效

---

## 🎲 词条系统设计

### 📊 词条分类体系

#### 1. 基础属性词条
**力量系**:
- 力量 +8-45点
- 攻击力 +15-85点
- 物理伤害 +12%-45%
- 暴击伤害 +15%-60%

**敏捷系**:
- 敏捷 +8-45点
- 攻击速度 +8%-35%
- 暴击率 +3%-15%
- 闪避率 +5%-25%

**体质系**:
- 体质 +8-45点
- 生命值 +120-680点
- 生命恢复 +15-85/秒
- 物理抗性 +10%-40%

**高级系** (引气/仙道阶段):
- 攻击力 +20-110点
- 暴击率 +3%-15%
- 全抗性 +10%-40%
- 五行伤害 +8%-35%

#### 2. 战斗效果词条
**攻击强化**:
- 所有伤害 +8%-30%
- 技能伤害 +15%-60%
- 元素伤害 +12%-45%
- 破甲效果 +10%-35%

**防御强化**:
- 所有抗性 +8%-25%
- 受到伤害减少 +6%-20%
- 格挡几率 +5%-18%
- 免控时间 +15%-50%

**特殊机制**:
- 吸血效果 +3%-12%
- 反弹伤害 +8%-25%
- 额外经验 +10%-35%
- 额外金币 +15%-50%

#### 3. 技能相关词条
**技能增强**:
- 技能触发率 +1%-15%
- 技能间隔提升 +10%-35%
- 特定技能强化 +20%-80%
- 技能范围扩大 +15%-50%

**武道技能** (1-3轮回主要):
- 武道技能伤害 +15%-50%
- 武道技能触发率 +3%-12%
- 重击技能强化 +20%-60%
- 连击技能强化 +20%-60%

**引气技能** (4-6轮回主要):
- 引气技能伤害 +20%-65%
- 引气技能触发率 +1%-15%
- 五行技能强化 +25%-75%
- 内力技能强化 +25%-75%

**仙道技能** (7-9轮回主要):
- 仙道技能伤害 +30%-100%
- 仙道技能触发率 +1%-15%
- 仙法技能强化 +40%-120%
- 超越技能强化 +50%-150%

#### 4. 套装词条 (特殊)
**套装专属词条** (仅套装装备拥有):
- 套装技能强化 +25%-80%
- 套装效果增强 +20%-60%
- 套装件数需求-1 (极稀有)
- 双套装效果 (可激活两个套装)

### 🎯 词条生成机制

#### 词条品质分级
- **普通词条**: 数值范围 60%-85%
- **优秀词条**: 数值范围 70%-95%
- **完美词条**: 数值范围 85%-100%
- **传说词条**: 数值范围 95%-100% + 特殊效果

#### 词条生成规则
```
词条数量 = 基础数量 + 随机数量
词条品质 = 装备品质影响 + 随机因素
词条类型 = 装备部位限制 + 随机选择
词条数值 = 品质范围内随机 + 强化加成
```

---

## 🎖️ 套装系统设计

### 🏛️ 套装分类

#### 1. 通用进程套装
这些套装与游戏的核心进程（武道、引气、仙道）绑定，代表了玩家在不同阶段的主流装备选择。

- **武道套装** (1-3时代主要)
  - **血战套装** (力量主导)
  - **疾风套装** (敏捷主导)
- **引气套装** (4-6时代主要)
  - **五行套装** (五行轮转)
  - **太极套装** (阴阳平衡)
- **仙道套装** (7-8时代主要)
  - **天道套装** (超越极限)
  - **虚空套装** (空间掌控)
- **跨时代套装**
  - **传承套装** (家族荣光)
  - **万象套装** (终极平衡)

#### 2. 地标专属套装
这些套装与特定时代的"历史回响地标"副本强关联，需要通过挑战特定副本获取的专属材料来打造，拥有独特的主题和强大的专属效果。

- **第一时代：奠基**
  - `处决者系列装备`
  - `调停者系列装备`
  - `间谍大师系列装备`
  - `先知系列装备`
- **第二时代：血脉分歧**
  - `督察官系列装备`
  - `调解者系列装备`
  - `幕后主使系列装备`
  - `破壁者系列装备`
- **第三时代：强邻入侵**
  - `英烈传承系列装备`
  - `和平使者系列装备`
  - `谋略大师系列装备`
  - `神射手系列装备`
- **第四时代：武仙启蒙**
  - `飞升者系列装备`
  - `博学者系列装备`
  - `精英系列装备`
  - `德鲁伊系列装备`
- **第五时代：初次交锋**
  - `排外者系列装备`
  - `和谐者系列装备`
  - `间谍大师系列装备`
  - `观察家系列装备`
- **第六时代：血脉原罪**
  - `冷血者系列装备`
  - `救赎者系列装备`
  - `双面者系列装备`
  - `真相者系列装备`
- **第七时代：霸业崛起**
  - `英雄王系列装备`
  - `外交家系列装备`
  - `海战统帅系列装备`
  - `天人系列装备`
- **第八时代：域外入侵**
  - `独行者系列装备`
  - `信任者系列装备`
  - `幻象大师系列装备`
  - `协调者系列装备`

### 🎯 套装获取方式
- **通用进程套装**: 主要通过挑战主线Boss、通用打造获得。
- **地标专属套装**: 必须通过挑战对应"历史回响地标"副本，获取"地标专属材料"进行打造。

### 🎯 套装效果示例

#### ⚔️ 民兵套装 (Militia's Set)
- **时代背景**: 第三时代，强邻入侵，全民皆兵的产物。
- **获取方式**: 在"强邻入侵"危机的"督战队"支线关卡中，通过"说服"路线获得其制作图纸。
- **套装部件**:
    - **民兵胸甲** (护甲)
    - **民兵头盔** (头盔)
- **套装效果**:
    - **2件套**: 生命值 +15%，受到"精英"敌人伤害 -5%。
- **设计理念**: 这不是一套为英雄设计的装备，而是为炮灰。其核心价值在于提升基础生存能力，体现了在残酷战争中保护普通族人的实用主义思想。

---

## 🔨 强化与打造系统

### ⚡ 装备强化系统

#### 强化等级与效果
```
强化等级: +0 → +10 (共11个等级)
属性提升: 每级+20%基础属性
最终效果: +10时总属性200%

强化影响:
- 基础属性: 攻击力、防御力、生命值等
- 词条属性: 词条数值上下限同时提升
- 套装效果: 套装数值随强化等级提升
```

#### 强化成功率与材料
| 强化等级 | 成功率 | 金币消耗 | 材料需求 | 失败惩罚 |
|----------|--------|----------|----------|----------|
| +0→+3 | 100% | 等级×100 | 普通材料×1 | 无 |
| +3→+6 | 80% | 等级×200 | 高级材料×1 | 返还50%金币 |
| +6→+9 | 60% | 等级×400 | 完美材料×1 | 装备-1级 |
| +9→+10 | 40% | 等级×800 | 传说材料×1 | 装备-2级 |

#### 强化保护机制
- **+0到+3**: 100%成功，无风险阶段
- **+4到+6**: 失败返还50%金币，装备不损坏
- **+7到+9**: 失败时装备强化等级-1
- **+10**: 失败时装备强化等级-2，需要传说材料

### 🏗️ 装备打造系统

#### 打造分类
**1. 基础打造** (白色-蓝色装备)
- **材料**: 金币 + 对应等级普通材料
- **成功率**: 95%
- **特点**: 词条随机，品质固定

**2. 高级打造** (紫色-橙色装备)
- **材料**: 金币 + 高级材料 + 装备图纸
- **成功率**: 70%
- **特点**: 可指定部分词条类型

**3. 神器打造** (红色装备)
- **材料**: 大量金币 + 完美材料 + Boss材料 + 传说图纸
- **成功率**: 40%
- **特点**: 可指定主要词条，最高词条数量

#### 打造材料系统
**普通材料** (40%掉落率，递减至1%):
- 铁矿石、兽皮、布料等基础材料
- 用于+0到+6强化和基础打造

**高级材料** (精英怪20%掉落，不递减):
- 精钢、魔兽皮、魔法布等
- 用于+7到+9强化和高级打造

**完美材料** (Boss 50%掉落，不递减):
- 秘银、龙鳞、仙丝等稀有材料
- 用于+10强化和神器打造

**地标专属材料** (100%掉落于对应地标副本):
- **定义**: 从特定"历史回响地标"副本中产出的、与该地标主题强相关的特殊材料。例如`纪律石板`、`英雄印记`等。
- **核心机制**:
  1.  **催化剂功能**: 在打造**任意**装备时，添加地标专属材料可以**抵扣一部分通用基础材料**（铁料/精钢/玄铁等）的消耗量，具体抵扣比例由公式决定。
  2.  **方向性功能**:
      - **定向打造 (100%强度)**: 使用与其主题匹配的专属材料打造地标专属套装时（例如，使用`英雄印记`打造`英雄王系列装备`），成品将拥有**100%**的正常设计强度。这是打造最强装备的唯一途径。
      - **万能打造 (80%强度)**: 使用不匹配的专属材料打造地标专属套装时（例如，使用`纪律石板`打造`英雄王系列装备`），依然可以成功打造，但成品只有**80%**的设计强度。这为玩家提供了灵活性，但最优解依然是挑战特定副本。

#### 地标专属装备打造规则
要打造任意一件地标专属装备，必须遵循以下配方模板：

- **核心三要素**: `对应图纸` + `足量通用材料` + `足量地标专属材料`

- **打造示例**:
  - **打造目标**: `英雄王之铠 (传说品质)`
  - **所需图纸**: `图纸：英雄王之铠` x 1
  - **所需通用材料**: `玄铁` x50, `云锦` x30, `灵石` x10 (具体数值待定)
  - **所需专属材料**: `英雄印记` x5 (具体数值待定)

> [!TIP]
> **关于图纸**: 每件专属装备的特定部位，都有其唯一的图纸，同样在对应的地标副本中掉落。
> **关于强度折扣**: 若在上例的打造中，使用**不匹配**的专属材料（如`纪律石板`）替代`英雄印记`，虽然仍可打造，但成品装备的强度将只有**80%**。

### 🌏 势力装备系统

#### 东方装备 (可装备)
**特点**:
- 正常装备使用
- 分解获得积分用于抽奖
- 符合东方武侠/修仙风格
- 命名采用中文古典风格

**示例**:
- 青锋剑、赤炎刀、寒月枪
- 玄铁重甲、轻风法袍、护心镜
- 凤翎冠、龙鳞护手、踏云靴

#### 西方装备 (不可装备)
**特点**:
- 无法穿戴，仅作为材料
- 分解获得特殊材料（不给积分）
- 特殊材料用于强化东方装备
- 命名采用西方奇幻风格

**示例**:
- 圣光剑、火焰法杖、雷霆之锤
- 骑士重甲、法师长袍、刺客皮甲
- 王者之冠、力量护手、疾风之靴

#### 势力材料转换
```
西方装备分解产出:
- 白色: 1-2个对应等级材料（白色品质）
- 绿色: 2-4个对应等级材料（绿色品质）
- 蓝色: 3-6个对应等级材料（蓝色品质）
- 紫色: 5-10个对应等级材料（紫色品质）
- 橙色: 8-15个对应等级材料（橙色品质）

分解材料用途:
- 直接用于装备强化（取消专门强化材料）
- 用于制作东方装备
- 材料品质影响强化成功率和制作品质
```

---

## 📊 掉落与获取机制

### 🎯 装备掉落规则

#### 基础掉落概率
```
掉落品质分布:
- 白色: 45% (基础装备)
- 绿色: 30% (常见装备)
- 蓝色: 18% (优质装备)
- 紫色: 6% (稀有装备)
- 橙色: 1% (传说装备)
- 红色: 0% (仅限打造)

掉落内容分配:
- 70%概率: 装备
- 30%概率: 技能书
```

#### Boss特殊掉落
**普通Boss**:
- 保证掉落当前区域等级装备1件
- 20%概率掉落高一品质装备
- 100%掉落特殊Boss材料

**精英Boss** (每10区域):
- 保证掉落高品质装备2件
- 50%概率掉落套装部件
- 掉落稀有强化材料

**传承Boss** (每5区域分身关卡):
- 掉落传承相关装备
- 100%掉落传承材料
- 有概率掉落制作配方

#### 势力装备分布
**东方装备区域** (主要获取):
- 区域1-30: 90%东方装备
- 区域31-60: 80%东方装备  
- 区域61-90: 70%东方装备

**西方装备区域** (材料来源):
- 区域1-30: 10%西方装备
- 区域31-60: 20%西方装备
- 区域61-90: 30%西方装备

### 🎲 抽奖系统设计

#### 积分抽奖
**普通抽奖** (10积分):
- 保底: 绿色装备
- 概率: 70%绿、25%蓝、5%紫

**高级抽奖** (50积分):
- 保底: 蓝色装备  
- 概率: 50%蓝、35%紫、15%橙

**传说抽奖** (200积分):
- 保底: 紫色装备
- 概率: 40%紫、45%橙、15%红

#### 保底机制
- **连抽保底**: 连续10次未出高品质，下次必出
- **积分返还**: 抽中重复套装时返还50%积分
- **选择权**: 传说抽奖可指定装备部位

---

## 🔗 装备数值与技能系统对接

### 📊 明确转换规则

#### 基础属性转换
```
装备属性 → 战斗数值转换表

【直接转换类】
- 攻击力：1点装备攻击力 = 1点技能基础伤害
- 防御力：1点装备防御力 = 减少1点受到伤害（护甲值）
- 生命值：1点装备生命值 = 1点生命上限
- 暴击率：1%装备暴击率 = 1%实际暴击概率
- 暴击伤害：1%装备暴击伤害 = 暴击时额外1%伤害倍率

【比例转换类】
- 攻击速度：1%装备攻击速度 = 攻击间隔减少1%
  公式：实际攻击间隔 = 1.5秒 / (1 + 攻击速度加成%)
- 技能触发率：直接加成，1%装备加成 = 1%技能触发概率提升
- 闪避率：1%装备闪避率 = 1%几率完全避免伤害
```

#### 技能系统对接机制
```
【攻击间隔基础规则】
- 基础攻击间隔：1.5秒
- 每1.5秒触发一次攻击判定
- 如果技能总概率≥100%：按权重随机选择一个技能释放
- 如果技能总概率<100%：按概率判定是否触发技能，否则执行普通攻击

【技能选择权重算法】
当技能总概率≥100%时：
1. 过滤掉处于冷却中的技能（刚触发过的技能需要间隔1个攻击周期）
2. 计算可用技能权重：权重 = 技能概率 / 总可用概率
3. 生成随机数选择技能

示例：
- 技能A：30%概率，可用
- 技能B：40%概率，冷却中
- 技能C：50%概率，可用
- 可用总概率：30% + 50% = 80%
- 技能A权重：30/80 = 37.5%
- 技能C权重：50/80 = 62.5%
```

#### 伤害计算公式
```
【技能伤害计算】
最终伤害 = (技能基础伤害 + 装备攻击力) × 技能倍率 × 暴击倍率 × 其他加成 - 敌人防御力

【普通攻击计算】
最终伤害 = 装备攻击力 × 暴击倍率 × 其他加成 - 敌人防御力

【防御减伤计算】
实际受到伤害 = max(1, 敌人伤害 - 我方防御力) × (1 - 抗性%)
```

---

## 🔧 技术实现设计

### 📊 数据结构设计

```javascript
// 装备基础数据结构
const equipment = {
    id: 1,                             // 装备ID
    name: "新手长剑",                  // 装备名称
    type: "WEAPON",                    // 装备类型
    slot: "WEAPON",                    // 装备部位
    quality: "COMMON",                 // 装备品质
    level: 1,                          // 装备等级
    faction: "EASTERN",                // 势力归属
    baseStats: {},                     // 基础属性
    affixes: [],                       // 词条列表
    setId: null,                       // 套装ID
    enhancementLevel: 0,               // 强化等级
    durability: 100,                   // 耐久度
    isEquipped: false                  // 是否已装备
}

// 装备基础属性
const baseStats = {
    attack: 0,                         // 攻击力
    defense: 0,                        // 防御力
    health: 0,                         // 生命值
    strength: 0,                       // 力量
    agility: 0,                        // 敏捷
    constitution: 0                    // 体质
}

// 词条数据结构
const affix = {
    id: 1,                             // 词条ID
    type: "STRENGTH",                  // 词条类型
    value: 10,                         // 词条数值
    valueType: "FIXED",                // 数值类型(百分比/固定值)
    tier: "NORMAL"                     // 词条品质
}

// 套装数据结构
const equipmentSet = {
    id: 1,                             // 套装ID
    name: "烈阳套装",                  // 套装名称
    description: "烈阳家族传承套装",    // 套装描述
    faction: "EASTERN",                // 势力归属
    twoPieceBonus: {},                 // 2件套效果
    fourPieceBonus: null,              // 4件套效果
    sixPieceBonus: null                // 6件套效果
}

// 常量定义
const EQUIPMENT_SLOT = {
    WEAPON: "WEAPON",      // 武器
    ARMOR: "ARMOR",        // 护甲
    HELMET: "HELMET",      // 头盔
    GLOVES: "GLOVES",      // 护手
    BELT: "BELT",          // 腰带
    BOOTS: "BOOTS"         // 靴子
}

const EQUIPMENT_QUALITY = {
    COMMON: "COMMON",      // 白色
    UNCOMMON: "UNCOMMON",  // 绿色
    RARE: "RARE",          // 蓝色
    EPIC: "EPIC",          // 紫色
    LEGENDARY: "LEGENDARY", // 橙色
    ARTIFACT: "ARTIFACT"   // 红色
}

const FACTION = {
    EASTERN: "EASTERN",    // 东方
    WESTERN: "WESTERN"     // 西方
}

const AFFIX_TYPE = {
    // 基础属性
    STRENGTH: "STRENGTH", 
    AGILITY: "AGILITY", 
    CONSTITUTION: "CONSTITUTION",
    ATTACK_POWER: "ATTACK_POWER", 
    DEFENSE: "DEFENSE", 
    HEALTH: "HEALTH",
    
    // 战斗属性
    CRIT_RATE: "CRIT_RATE", 
    CRIT_DAMAGE: "CRIT_DAMAGE", 
    ATTACK_SPEED: "ATTACK_SPEED", 
    DODGE_RATE: "DODGE_RATE",
    PHYSICAL_PENETRATION: "PHYSICAL_PENETRATION", 
    ALL_PENETRATION: "ALL_PENETRATION",
    PHYSICAL_RESISTANCE: "PHYSICAL_RESISTANCE", 
    ALL_RESISTANCE: "ALL_RESISTANCE",
    
    // 技能相关
    SKILL_DAMAGE: "SKILL_DAMAGE", 
    SKILL_TRIGGER_RATE: "SKILL_TRIGGER_RATE", 
    SKILL_COOLDOWN_REDUCTION: "SKILL_COOLDOWN_REDUCTION",
    MARTIAL_SKILL_DAMAGE: "MARTIAL_SKILL_DAMAGE", 
    QI_SKILL_DAMAGE: "QI_SKILL_DAMAGE", 
    IMMORTAL_SKILL_DAMAGE: "IMMORTAL_SKILL_DAMAGE",
    
    // 特殊效果
    LIFE_STEAL: "LIFE_STEAL", 
    DAMAGE_REFLECTION: "DAMAGE_REFLECTION", 
    EXPERIENCE_BONUS: "EXPERIENCE_BONUS", 
    GOLD_BONUS: "GOLD_BONUS",
    ELEMENTAL_DAMAGE: "ELEMENTAL_DAMAGE", 
    STATUS_RESISTANCE: "STATUS_RESISTANCE"
}
```

### ⚙️ 装备管理系统

```javascript
// 装备管理类
class EquipmentManager {
    constructor() {
        this.equippedItems = new Map(); // 已装备的物品
        this.inventory = [];            // 背包
    }
    
    // 装备穿戴
    equipItem(equipment) {
        // 检查装备条件
        if (!this.canEquip(equipment)) return false;
        
        // 检查势力限制
        if (equipment.faction === FACTION.WESTERN) {
            throw new Error("西方装备无法穿戴");
        }
        
        // 卸下当前装备
        const currentEquipped = this.equippedItems.get(equipment.slot);
        if (currentEquipped) { 
            currentEquipped.isEquipped = false;
            this.addToInventory(currentEquipped);
        }
        
        // 穿戴新装备
        const newEquipment = { ...equipment, isEquipped: true };
        this.equippedItems.set(equipment.slot, newEquipment);
        this.removeFromInventory(equipment);
        
        // 重新计算属性
        this.recalculateStats();
        return true;
    }
    
    // 计算套装效果
    fun getActiveSetBonuses(): List<SetBonus> {
        val setBonuses = mutableListOf<SetBonus>()
        val setCount = mutableMapOf<Int, Int>()
        
        // 统计套装件数
        equippedItems.values.forEach { equipment ->
            equipment.setId?.let { setId ->
                setCount[setId] = setCount.getOrDefault(setId, 0) + 1
            }
        }
        
        // 激活对应套装效果
        setCount.forEach { (setId, count) ->
            val set = getEquipmentSet(setId)
            when {
                count >= 6 && set.sixPieceBonus != null -> 
                    setBonuses.addAll(listOf(set.twoPieceBonus, set.fourPieceBonus!!, set.sixPieceBonus))
                count >= 4 && set.fourPieceBonus != null -> 
                    setBonuses.addAll(listOf(set.twoPieceBonus, set.fourPieceBonus))
                count >= 2 -> 
                    setBonuses.add(set.twoPieceBonus)
            }
        }
        
        return setBonuses
    }
    
    // 计算最终属性
    fun calculateFinalStats(): CombinedStats {
        var totalStats = BaseStats()
        
        // 装备基础属性
        equippedItems.values.forEach { equipment ->
            val enhancedStats = applyEnhancement(equipment.baseStats, equipment.enhancementLevel)
            totalStats = totalStats.combine(enhancedStats)
        }
        
        // 词条属性
        val affixBonuses = calculateAffixBonuses()
        totalStats = totalStats.applyAffixes(affixBonuses)
        
        // 套装效果
        val setBonuses = getActiveSetBonuses()
        totalStats = totalStats.applySetBonuses(setBonuses)
        
        return CombinedStats(
            baseStats = totalStats,
            affixBonuses = affixBonuses,
            setBonuses = setBonuses
        )
    }
    
    private fun applyEnhancement(baseStats: BaseStats, level: Int): BaseStats {
        val multiplier = 1f + (level * 0.2f) // 每级+20%
        return baseStats.copy(
            attack = (baseStats.attack * multiplier).toInt(),
            defense = (baseStats.defense * multiplier).toInt(),
            health = (baseStats.health * multiplier).toInt(),
            // ... 其他属性
        )
    }
}
```

### 🔨 强化系统实现

```kotlin
class EnhancementSystem {
    
    fun enhanceEquipment(
        equipment: Equipment,
        materials: List<Material>,
        gold: Int
    ): EnhancementResult {
        val nextLevel = equipment.enhancementLevel + 1
        val requirement = getEnhancementRequirement(nextLevel)
        
        // 检查材料和金币
        if (gold < requirement.goldCost || !hasSufficientMaterials(materials, requirement.materials)) {
            return EnhancementResult.INSUFFICIENT_RESOURCES
        }
        
        // 计算成功率
        val successRate = calculateSuccessRate(nextLevel, materials)
        val success = Random.nextFloat() < successRate
        
        return if (success) {
            val enhancedEquipment = equipment.copy(enhancementLevel = nextLevel)
            EnhancementResult.SUCCESS(enhancedEquipment)
        } else {
            val penaltyLevel = calculateFailurePenalty(nextLevel)
            val penalizedEquipment = equipment.copy(
                enhancementLevel = maxOf(0, equipment.enhancementLevel - penaltyLevel)
            )
            EnhancementResult.FAILURE(penalizedEquipment, requirement.goldCost / 2)
        }
    }
    
    private fun calculateSuccessRate(targetLevel: Int, materials: List<Material>): Float {
        val baseRate = when (targetLevel) {
            in 1..3 -> 1.0f      // 100%
            in 4..6 -> 0.8f      // 80%
            in 7..9 -> 0.6f      // 60%
            10 -> 0.4f           // 40%
            else -> 0.0f
        }
        
        // 材料品质影响成功率
        val materialBonus = materials.sumOf { it.enhancementBonus } / 100f
        
        return minOf(1.0f, baseRate + materialBonus)
    }
}
```

### 🎲 装备生成系统

```kotlin
class EquipmentGenerator {
    
    fun generateEquipment(
        slot: EquipmentSlot,
        quality: EquipmentQuality,
        level: Int,
        faction: Faction = Faction.EASTERN
    ): Equipment {
        // 生成基础属性
        val baseStats = generateBaseStats(slot, quality, level)
        
        // 生成词条
        val affixCount = getAffixCount(quality)
        val affixes = generateAffixes(slot, quality, affixCount)
        
        // 选择套装（如果适用）
        val setId = if (Random.nextFloat() < getSetDropRate(quality)) {
            selectRandomSet(quality, faction)
        } else null
        
        return Equipment(
            id = generateUniqueId(),
            name = generateName(slot, quality, faction),
            type = getEquipmentType(slot),
            slot = slot,
            quality = quality,
            level = level,
            faction = faction,
            baseStats = baseStats,
            affixes = affixes,
            setId = setId
        )
    }
    
    private fun generateAffixes(
        slot: EquipmentSlot,
        quality: EquipmentQuality,
        count: Int
    ): List<Affix> {
        val availableAffixes = getAvailableAffixes(slot)
        val selectedAffixes = mutableListOf<Affix>()
        
        repeat(count) {
            val affixType = availableAffixes.random()
            val tier = rollAffixTier(quality)
            val value = generateAffixValue(affixType, tier)
            
            selectedAffixes.add(
                Affix(
                    id = generateUniqueId(),
                    type = affixType,
                    value = value,
                    valueType = getValueType(affixType),
                    tier = tier
                )
            )
        }
        
        return selectedAffixes
    }
    
    private fun getAffixCount(quality: EquipmentQuality): Int {
        return when (quality) {
            EquipmentQuality.COMMON -> Random.nextInt(0, 3)      // 0-2
            EquipmentQuality.UNCOMMON -> Random.nextInt(1, 4)    // 1-3
            EquipmentQuality.RARE -> Random.nextInt(2, 5)        // 2-4
            EquipmentQuality.EPIC -> Random.nextInt(3, 7)        // 3-6
            EquipmentQuality.LEGENDARY -> Random.nextInt(4, 9)   // 4-8
            EquipmentQuality.ARTIFACT -> Random.nextInt(6, 11)   // 6-10
        }
    }
}
```

---

## 📈 平衡性考虑

### ⚖️ 数值平衡原则

#### 1. 装备获取难度梯度
```
装备获取投入 ∝ 装备价值提升

白色装备: 无门槛，快速替换
绿色装备: 低门槛，短期使用
蓝色装备: 中门槛，中期核心
紫色装备: 高门槛，长期目标
橙色装备: 极高门槛，追求目标
红色装备: 终极门槛，完美追求
```

#### 2. 强化风险收益平衡
- **低强化等级** (+1到+3): 无风险，鼓励尝试
- **中强化等级** (+4到+6): 低风险，稳定提升
- **高强化等级** (+7到+9): 中风险，显著提升
- **极限强化** (+10): 高风险，极限追求

#### 3. 套装激活条件
- **2件套**: 易于激活，提供基础加成
- **4件套**: 需要投入，提供核心能力
- **6件套**: 重大投入，获得质变效果

### 🎯 长期发展考虑

#### 装备更新换代
- **1-3轮回**: 主要使用武道系装备和套装
- **4-6轮回**: 引气系装备开始占主导
- **7-9轮回**: 仙道系装备成为终极目标
- **后续轮回**: 通过强化和完美词条提升

#### 经济循环健康
- **材料消耗**: 通过强化消耗低级材料，保持经济流动
- **装备更新**: 定期更换装备，避免一套装备用到底
- **选择多样**: 多种装备流派，避免唯一最优解

---

## 🚀 扩展功能设计

### 🔮 装备进阶系统（后期扩展）

#### 1. 装备觉醒
- **觉醒条件**: 满强化+完美词条+特殊材料
- **觉醒效果**: 装备获得独特外观和额外特效
- **觉醒等级**: 多级觉醒，逐步解锁新能力

#### 2. 装备融合
- **融合机制**: 两件相同部位装备融合成新装备
- **融合规则**: 保留最好的词条，基础属性取平均值
- **融合成本**: 消耗特殊融合石和大量金币

#### 3. 装备符文
- **符文系统**: 为装备镶嵌符文提供额外效果
- **符文类型**: 攻击符文、防御符文、技能符文等
- **符文等级**: 符文也有品质和等级系统

### 🏪 交易系统（后期扩展）

#### 1. 拍卖行
- **装备交易**: 玩家间装备买卖
- **价格发现**: 市场决定装备价值
- **税收机制**: 交易税收调节经济

#### 2. 材料交换
- **材料交易**: 低级材料换高级材料
- **比例递增**: 防止材料贬值
- **每日限额**: 控制交换规模

---

## 🎭 主线故事专属装备

### 📚 设计说明
本章节记录了通过主线故事模块获得的特殊装备。这些装备具有独特的背景故事，通常拥有特殊效果或纪念意义，是玩家收藏和使用的珍贵物品。

### ⚔️ 武器类专属装备

#### 【老锤的矿镐】
- **品质**: 蓝色 (精良)
- **类型**: 钝器武器
- **获取途径**: 第三时代"血色征召"支线，在[决斗]中战胜矿工领袖"老锤"
- **装备描述**: 一柄沉重的、经过粗糙改装的采矿锤，锤头上还残留着岩石的碎屑和淡淡的血腥味。它象征着来自底层最纯粹的固执与力量。
- **基础属性**: 攻击力 +35
- **专属词条**:
  - 对"人形"单位伤害 +10%
  - 5%几率在攻击时造成"眩晕"效果
  - 击杀敌人时恢复少量生命值
- **背景故事**: 这把矿镐原本属于矿场的领袖"老锤"，是他多年来挖掘岩石的工具。在与家主的决斗中败北后，这把矿镐成了勇气与坚持的象征。

#### 【无面者的血刃】
- **品质**: 红色 (神器) - 唯一装备
- **类型**: 匕首/短刃
- **获取途径**: 第六时代"大清洗"支线，击败清洗者头目"无面者"刺客
- **装备描述**: 一柄形状诡异的弯曲短刀，刀身上似乎永远沾着无法清洗的血迹。凝视刀刃时，仿佛能听到无数亡魂的哀嚎。
- **基础属性**: 攻击力 +85, 暴击率 +12%
- **专属词条**:
  - 对"叛徒"和"间谍"类敌人伤害 +35%
  - 攻击时10%几率造成"出血"效果
  - 击杀敌人后下一次攻击必定暴击
  - 装备时略微降低魅力值，但增加威慑力
- **背景故事**: 这把血刃的真正来历已无从考证，只知道它曾属于神秘的"无面者"组织。刀刃上的血迹据说来自于历代背叛者，是复仇与清算的象征。

#### 【埃德里克的统帅剑】
- **品质**: 橙色 (传说)
- **类型**: 长剑
- **获取途径**: 第七时代铁血路径"血火之约"，在决斗中击败西域联军统帅埃德里克
- **装备描述**: 一柄做工精良的西式长剑，剑柄上刻着复杂的家族纹章。虽然属于敌人，但其精湛的工艺和历史意义让人不得不敬重。
- **基础属性**: 攻击力 +75, 威望 +5
- **专属词条**:
  - 对"西方"单位额外伤害 +20%
  - 装备时所有友方单位士气 +10%
  - 在单挑战斗中获得额外优势
  - 击败强敌后永久提升少量威望
- **背景故事**: 这把剑曾是西域名将埃德里克的佩剑，见证了无数场战斗的胜利。在最终的决斗中败于东方家主手下，成为了两个文明冲突的历史见证。

### 🛡️ 防具类专属装备

#### 【烈焰舞者斗篷】
- **品质**: 蓝色 (精良)
- **类型**: 斗篷/外袍
- **获取途径**: 第三时代"峡谷伏兵"支线，核心战斗中无伤亡完成火烧连营
- **装备描述**: 一件经过特殊处理的斗篷，表面似乎永远在微弱地闪烁着火光。穿着者在火焰中行走时显得格外优雅。
- **基础属性**: 防御力 +25, 火焰抗性 +30%
- **专属词条**:
  - 免疫"燃烧"状态
  - 移动时20%几率在身后留下火焰痕迹
  - 火焰伤害反弹 15%
- **背景故事**: 这件斗篷是在峡谷大火中诞生的奇迹，火焰不但没有烧毁它，反而赋予了它特殊的力量。

#### 【风暴壁垒】
- **品质**: 红色 (神器) - 唯一装备
- **类型**: 盾牌
- **获取途径**: 第五时代铁血路径"第一波浪潮"，三战全胜后获得
- **装备描述**: 一面厚重的塔盾，表面雕刻着波涛和闪电的图案。握持时能感受到其中蕴含的风暴之力。
- **基础属性**: 防御力 +120, 生命值 +500
- **专属词条**:
  - 格挡时25%几率释放"雷击"反击
  - 在海战或风暴天气中防御力 +50%
  - 可主动释放"风暴护盾"技能（冷却时间5分钟）
  - 装备时队伍整体防御力 +10%
- **背景故事**: 这面盾牌在抵御西域联军海上进攻时展现了神奇的力量，据说是海神对勇敢守护者的恩赐。

#### 【秩序法袍】
- **品质**: 红色 (神器) - 唯一装备
- **类型**: 法袍/道袍
- **获取途径**: 第四时代智谋路径"门之试炼"，完美通过所有试炼
- **装备描述**: 一件剪裁精致的深蓝色法袍，袍身上绣着复杂的几何图案，散发着智慧与秩序的光辉。
- **基础属性**: 防御力 +60, 体质 +20, 智谋 +15
- **专属词条**:
  - 所有属性检定成功率 +15%
  - 免疫"魅惑"和"混乱"状态
  - 释放技能时10%几率重置冷却时间
  - 在管理和外交事件中获得额外选项
- **背景故事**: 这件法袍是为真正的智者而制，只有通过严格试炼证明自己智慧的人才能驾驭它的力量。

### 💍 饰品类专属装备

#### 【苍之血玉】
- **品质**: 红色 (神器) - 唯一装备
- **类型**: 护符/项链
- **获取途径**: 第四时代铁血路径"禁地的守护者"，战胜"求道者·苍"后选择处决
- **装备描述**: 一块深红色的玉石，内部似乎流动着鲜血般的液体。佩戴时能感受到其中蕴含的狂热意志。
- **基础属性**: 体质 +18, 攻击力 +30
- **专属词条**:
  - 生命值低于30%时攻击力 +50%
  - 击杀敌人时恢复15%最大生命值
  - 免疫"恐惧"状态
  - 死亡时25%几率复活（每天只能触发一次）
- **背景故事**: 这块血玉据说凝聚了"求道者·苍"一生的修炼精华，其中蕴含着对武道的极端追求和不屈意志。

#### 【共鸣之心】
- **品质**: 红色 (神器) - 唯一装备
- **类型**: 心法宝物
- **获取途径**: 第四时代洞察路径"先贤之梦"，对最终问题做出最深刻回答
- **装备描述**: 一颗透明的水晶球，内部似乎包含着整个宇宙的奥秘。注视时能感受到与万物的深层连接。
- **基础属性**: 洞察 +25, 全属性 +5
- **专属词条**:
  - 与NPC互动时好感度获得 +100%
  - 所有感知类检定自动成功
  - 可预知随机事件的结果（每天3次）
  - 在冥想和修炼时效果翻倍
- **背景故事**: 这颗心法宝物是远古仙人留下的遗产，只有真正理解"力量本质"的人才能与之产生共鸣。

#### 【旁观者之眼】
- **品质**: 红色 (神器) - 唯一装备
- **类型**: 眼镜/护目镜
- **获取途径**: 第五时代洞察路径"裂痕"，成功引爆西域联盟内部矛盾
- **装备描述**: 一副看似普通的眼镜，但透过镜片能看到常人无法察觉的真相。镜框上刻着微小的符文。
- **基础属性**: 洞察 +20, 外交 +10
- **专属词条**:
  - 可看透NPC的真实想法和倾向
  - 在所有间谍和情报活动中获得巨大优势
  - 免疫"欺骗"和"伪装"效果
  - 可发现隐藏的剧情线索和秘密
- **背景故事**: 这副眼镜据说能让佩戴者看穿一切表象，洞察事物的本质。但过于清晰的真相有时也是一种负担。

#### 【伪装者的面具】
- **品质**: 红色 (神器) - 唯一装备
- **类型**: 面具
- **获取途径**: 第六时代智谋路径"特洛伊木马"，完美完成所有破坏任务
- **装备描述**: 一张纯白的面具，表面光滑如镜。佩戴时面具会自动变化，让人看到他们想看到的面孔。
- **基础属性**: 智谋 +20, 魅力 +15
- **专属词条**:
  - 可变换外貌，伪装成任何人
  - 在潜行和间谍任务中成功率 +50%
  - 免疫所有识别和探测效果
  - 可获得目标的身份和记忆片段
- **背景故事**: 这张面具是间谍艺术的极致体现，佩戴者可以成为任何人，但也可能在无数面孔中迷失真正的自我。

### 📜 技能书类专属道具

#### 【技能书：威严光环】
- **品质**: 紫色 (史诗)
- **类型**: 技能学习道具
- **获取途径**: 第四时代铁血路径"禁地的守护者"，成功威压"求道者·苍"及其部众
- **效果**: 学习技能"威严光环" - 被动技能，战斗中所有友方单位攻击力 +20%，敌方单位士气 -15%
- **学习要求**: 威望 ≥ 20, 军事 ≥ 15
- **描述**: 这本技能书记录了如何散发个人威严，让友军更加勇敢，让敌军胆怯的秘法。

#### 【技能书：心灵之眼】
- **品质**: 紫色 (史诗)
- **类型**: 技能学习道具
- **获取途径**: 第四时代洞察路径"先贤之梦"，正确解读所有远古谜语
- **效果**: 学习技能"心灵之眼" - 主动技能，可洞察目标的弱点，下次攻击必定暴击且伤害翻倍
- **学习要求**: 洞察 ≥ 20, 体质 ≥ 15
- **描述**: 这本技能书包含了远古仙人的修炼心得，能让人获得洞察一切的能力。

#### 【技能书：破胆怒吼】
- **品质**: 蓝色 (精良)
- **类型**: 技能学习道具
- **获取途径**: 第三时代"血色征召"支线，全程采用强硬手段处理
- **效果**: 学习技能"破胆怒吼" - 主动技能，对周围敌人造成"恐惧"状态，降低其攻击力和移动速度
- **学习要求**: 威望 ≥ 15, 军事 ≥ 10
- **描述**: 记录了如何发出震慑敌胆的战吼技巧，是军中将领的必备技能。

#### 【技能书：精准射击】
- **品质**: 蓝色 (精良)
- **类型**: 技能学习道具
- **获取途径**: 第三时代"峡谷伏兵"支线，全歼敌方斥候
- **效果**: 学习技能"精准射击" - 主动技能，下次远程攻击必定命中且无视防御
- **学习要求**: 敏捷 ≥ 15, 军事 ≥ 10
- **描述**: 详细记录了弓箭手的瞄准技巧和呼吸控制方法，能大幅提升射击精度。

### 🔨 配方类专属道具

#### 【配方：民兵胸甲】
- **品质**: 蓝色 (精良)
- **类型**: 装备制作配方
- **获取途径**: 第三时代"血色征召"支线，通过安抚或属性检定成功处理矛盾
- **制作要求**: 铁匠技能 ≥ 3级, 铁锭×20, 皮革×10
- **制作装备**: 蓝色品质轻甲，防御力 +35, 生命值 +200
- **特殊效果**: 制作的胸甲附带"民心所向"词条 - 佩戴时民心值获得速度 +25%
- **描述**: 这份配方记录了如何制作适合平民武装的实用胸甲，强调实用性和经济性。

#### 【配方：和平主义者徽章】
- **品质**: 绿色 (优秀)
- **类型**: 饰品制作配方
- **获取途径**: 第三时代"屈辱的贡品"支线，完美护送贡品
- **制作要求**: 工匠技能 ≥ 2级, 银锭×5, 宝石×1
- **制作装备**: 绿色品质饰品，外交 +8, 仁德 +5
- **特殊效果**: 制作的徽章附带"和平光辉"词条 - 在外交事件中获得额外和平选项
- **描述**: 这份配方展示了如何制作象征和平与仁德的徽章，佩戴者更容易获得他人信任。

#### 【配方：刺客的匕首】
- **品质**: 蓝色 (精良)
- **类型**: 武器制作配方
- **获取途径**: 第三时代"深入敌营"支线，成功散播致命谣言
- **制作要求**: 铁匠技能 ≥ 4级, 精钢×15, 毒囊×3
- **制作装备**: 蓝色品质匕首，攻击力 +40, 暴击率 +8%
- **特殊效果**: 制作的匕首附带"致命毒素"词条 - 攻击时20%几率造成中毒效果
- **描述**: 这份配方来自职业刺客的秘密手册，记录了如何制作涂毒暗器的方法。

#### 【配方：静心焚香】
- **品质**: 蓝色 (精良)
- **类型**: 消耗品制作配方
- **获取途径**: 第四时代"理性的代价"支线，成功解救所有平民
- **制作要求**: 炼金技能 ≥ 3级, 灵芝×5, 檀香木×10, 甘露×3
- **制作道具**: 一次性消耗品，使用后获得"静心"状态（1小时）
- **静心状态效果**: 技能冷却时间 -25%, 体质恢复速度 +50%, 免疫负面精神状态
- **描述**: 这份配方记录了如何制作能够安定心神的特殊熏香，对修炼者极为有用。

### 💎 材料类专属道具

#### 【血浸的水晶】
- **品质**: 紫色 (史诗)
- **类型**: 强化材料
- **获取途径**: 第三时代"屈辱的贡品"支线，惨淡收场时获得
- **数量**: 5个
- **用途**: 武器强化材料，提供额外暴击伤害加成
- **特殊效果**: 使用此材料强化的武器会获得"嗜血"词条 - 击杀敌人时恢复生命值
- **描述**: 这些水晶在屈辱和愤怒中诞生，蕴含着强烈的复仇意志。

#### 【褪色的符文石】
- **品质**: 紫色 (史诗)
- **类型**: 研究材料
- **获取途径**: 第四时代"理性的代价"支线，成功净化初代畸变体
- **用途**: 可用于研究武仙功法或制作高级符文装备
- **特殊效果**: 研究后可解锁"符文工艺"科技树
- **描述**: 这块符文石曾经光芒四射，但畸变的影响让它失去了原有的力量，却也因此蕴含了特殊的研究价值。

### 📋 情报类专属道具

#### 【苍的修炼笔记】
- **品质**: 橙色 (传说)
- **类型**: 情报道具
- **获取途径**: 第四时代"禁地的守护者"支线，战胜"求道者·苍"后选择囚禁
- **效果**: 永久解锁"极端武道"修炼路线，获得特殊武技配方
- **额外信息**: 揭示武仙遗迹的部分秘密，为后续探索提供线索
- **描述**: "求道者·苍"多年的修炼心得，记录了许多危险但强大的武道技巧。

#### 【敌军指挥官的私人信件】
- **品质**: 蓝色 (精良)
- **类型**: 情报道具
- **获取途径**: 第三时代"深入敌营"支线，集齐三条情报
- **效果**: 揭示敌军内部矛盾，降低后续战斗难度15%
- **额外信息**: 获得敌军部署和弱点信息
- **描述**: 这些私人信件暴露了敌军指挥层的内部问题，是珍贵的军事情报。

#### 【西域联盟的作战地图】
- **品质**: 蓝色 (精良)
- **类型**: 情报道具
- **获取途径**: 第五时代"第一波浪潮"支线，三战全胜后获得
- **效果**: 降低所有与西域联盟相关战斗的难度20%
- **额外信息**: 显示西域联盟的军事部署和补给路线
- **描述**: 详细的作战地图，标注了西域联盟的军事设施和战略要点。

#### 【西域渗透计划书】
- **品质**: 橙色 (传说)
- **类型**: 情报道具
- **获取途径**: 第六时代"特洛伊木马"支线，刺杀幕后黑手白须翁后获得
- **效果**: 揭示西域联盟的长期渗透计划，永久提升反间谍能力
- **额外信息**: 获得西域联盟间谍网络的详细信息
- **描述**: 这份秘密文件详细记录了西域联盟在东方的间谍活动计划，价值连城。

### 🔧 工具类专属道具

#### 【玲珑的万能钥匙】
- **品质**: 橙色 (传说)
- **类型**: 特殊工具
- **获取途径**: 第五时代"阴影中的第一步"支线，完美潜入后获得
- **效果**: 可开启任何机械锁和密码锁，潜行任务成功率 +30%
- **特殊功能**: 在探索和间谍任务中自动解锁额外区域
- **描述**: 玲珑亲手制作的精密工具，据说没有它打不开的锁。

#### 【流寇的信物】
- **品质**: 绿色 (优秀)
- **类型**: 特殊道具
- **获取途径**: 第三时代"屈辱的贡品"支线，与流寇进行交易
- **效果**: 在与盗贼、流寇等"非法组织"交涉时获得优势
- **特殊功能**: 可通过黑市获得稀有物品和情报
- **描述**: 流寇首领的个人信物，在黑道中享有一定威望。

### 📖 学术类专属道具

#### 【白须翁的药经】
- **品质**: 橙色 (传说)
- **类型**: 学术典籍
- **获取途径**: 第六时代"解药"支线，成功救回虎子后获得
- **效果**: 永久提升家族炼金水平，解锁高级药剂配方
- **学习收益**: 炼金技能上限 +2级，获得"毒理学"专业知识
- **描述**: 白须翁毕生的炼金研究成果，包含了许多失传的药方和毒方。

#### 【镇族之宝】
- **品质**: 红色 (神器)
- **类型**: 家族传承
- **获取途径**: 第六时代"审判"支线，完美定罪白须翁后石山长老交出
- **效果**: 家族威望永久 +50，解锁家族秘传武学
- **特殊意义**: 家族最珍贵的传承物品，代表着家族的荣耀和历史
- **描述**: 这件传家之宝承载着家族数代人的心血，是家族精神的象征。

### 💫 特殊技能专属道具

#### 【言语之力】
- **品质**: 红色 (神器)
- **类型**: 唯一技能
- **获取途径**: 第六时代"审判"支线，完美定罪后获得
- **效果**: 在所有对话和外交事件中获得绝对优势，可说服任何理智的NPC
- **冷却时间**: 每天只能使用3次，但效果极其强大
- **描述**: 这是掌握真理者的特殊能力，真诚的话语拥有改变人心的力量。

### 🎯 获取途径统计

#### 按时代分类：
- **第三时代** (强邻入侵): 9件装备道具
- **第四时代** (武仙启蒙): 6件装备道具  
- **第五时代** (初次交锋): 4件装备道具
- **第六时代** (血脉原罪): 6件装备道具
- **第七时代** (霸业崛起): 1件装备道具

#### 按品质分类：
- **红色 (神器)**: 8件 - 最珍贵的唯一装备
- **橙色 (传说)**: 6件 - 高价值装备和道具
- **紫色 (史诗)**: 5件 - 稀有材料和技能书
- **蓝色 (精良)**: 10件 - 主力装备和配方
- **绿色 (优秀)**: 2件 - 过渡性装备

#### 按类型分类：
- **武器**: 3件 (矿镐、血刃、统帅剑)
- **防具**: 3件 (斗篷、盾牌、法袍)
- **饰品**: 5件 (血玉、共鸣之心、旁观者之眼、伪装面具等)
- **技能书**: 4件 (威严光环、心灵之眼、破胆怒吼、精准射击)
- **配方**: 4件 (胸甲、徽章、匕首、焚香)
- **材料**: 2件 (血浸水晶、符文石)
- **情报**: 4件 (修炼笔记、信件、地图、计划书)
- **工具**: 2件 (万能钥匙、流寇信物)
- **学术**: 2件 (药经、镇族之宝)
- **特殊技能**: 1件 (言语之力)

### 📝 设计备注

1. **平衡性考虑**: 所有神器级装备都有获取条件和使用限制，避免过于强大破坏游戏平衡
2. **故事关联**: 每件装备都与具体的剧情事件紧密相关，增强玩家的代入感
3. **收集价值**: 不同路径获得不同装备，鼓励玩家多周目体验
4. **成长导向**: 装备强度与时代发展同步，符合角色成长曲线

---

**文档版本**: v1.1  
**创建日期**: 2024年12月19日  
**最后更新**: 2024年12月20日  
**相关文档**: README.md, 技能系统设计.md, 地图系统设计.md, 剧情系统相关文档  
**维护者**: CHX 

---
## 附录：主线故事专属装备清单

本清单列出了在主线故事和关键支线中可以获得的专属装备，它们不仅属性强大，更承载着家族在特定时代的历史记忆。

---
### **第一时代：奠基**

#### **处决者系列 (套装)**
*   **获取方式**: 完成第一时代"建立法典"主线，并挑战"初诞者法庭"历史回响地标。
*   **套装效果**: (2件) 对所有拥有"罪犯"或"叛乱"标签的单位，最终伤害提升20%。(3件) 你的"威慑"和"恐惧"类效果持续时间延长50%。

1.  **处决者的断头斧**
    *   **品质**: 紫色
    *   **部位**: 武器 (双手斧)
    *   **故事**: "在最初的法律颁布时，这柄斧头是秩序最无情的象征。它斩下的每一个头颅，都在宣告着混乱的终结。"
    *   **词条**:
        *   `[专属] 有5%的概率立即处决生命值低于15%的非首领敌人。`
        *   `攻击时有10%概率使敌人"恐惧"，持续2秒。`
        *   `力量+15`

2.  **处决者的罪罚肩铠**
    *   **品质**: 紫色
    *   **部位**: 护甲
    *   **故事**: "这副肩铠上沾染的血迹早已洗不掉，它们属于那些胆敢挑战初生文明权威的人。"
    *   **词条**:
        *   `[专属] 每场战斗中，第一次受到的致命伤害无效，并恢复10%的生命值。`
        *   `受到攻击时，有15%的概率使攻击者"威慑"，攻击力降低10%，持续3秒。`
        *   `体质+15`

3.  **处决者的凝视**
    *   **品质**: 紫色
    *   **部位**: 头盔
    *   **故事**: "戴上它，你将不再是自己，而是行走的律法。你的目光所及，皆为审判之地。"
    *   **词条**:
        *   `[专属] 你的单体攻击技能，有20%概率锁定目标，使其闪避率和格挡率降低30%，持续5秒。`
        *   `暴击率+5%`
        *   `敏捷+10`

---
### **第三时代：强邻入侵**

#### **英烈传承系列 (套装)**
*   **获取方式**: 完成第三时代"背水一战"主线，并挑战"无名英雄纪念碑"历史回响地标。
*   **套装效果**: (2件) 格挡率+15%。(3件) 成功格挡后，下一次技能伤害提升30%。(4件) 当你生命值首次低于30%时，召唤一位先祖英魂为你作战15秒。

4.  **英烈的守护之盾**
    *   **品质**: 紫色
    *   **部位**: 武器 (盾牌)
    *   **故事**: "当强邻的铁蹄踏碎和平的幻梦，无数先辈用胸膛化作盾墙。这面盾牌，便是他们意志的最后余光。"
    *   **词条**:
        *   `[专属] 成功格挡后，为全体友方单位（若有）施加一个能吸收少量伤害的护盾。`
        *   `你的"嘲讽"或"守护"类技能效果提升30%。`
        *   `体质+20`

5.  **英烈的残破胸甲**
    *   **品质**: 紫色
    *   **部位**: 护甲
    *   **故事**: "胸甲上的每一道裂痕，都代表着一次致命的攻击和一次不屈的站立。"
    *   **词条**:
        *   `[专属] 你受到的单次伤害不会超过你最大生命值的30%。`
        *   `受击时有10%的概率回复2%最大生命值。`
        *   `所有抗性+10%`

6.  **英烈的折断之刃**
    *   **品质**: 紫色
    *   **部位**: 武器 (单手剑)
    *   **故事**: "剑已折，锋芒犹在。它不再渴望杀戮，只为守护而挥舞。"
    *   **词条**:
        *   `[专属] 你的"反击"类技能伤害提升50%，触发率提升10%。`
        *   `格挡成功后，你的下一次攻击必定暴击。`
        *   `力量+10, 敏捷+10`

7.  **英烈的希望**
    *   **品质**: 橙色
    *   **部位**: 饰品
    *   **故事**: "在最黑暗的年代，希望是比黄金更珍贵的宝藏。它由无数牺牲者的信念凝聚而成。"
    *   **词条**:
        *   `[专属] 战斗开始时，若队伍中有超过1名成员，全体士气+20，所有技能触发率提升5%。`
        *   `[专属] 你死亡时，所有其他友方单位立刻恢复25%的生命值和技能冷却。`
        *   `所有基础属性+5%`

---
### **第四时代：武仙启蒙**

#### **求道者系列 (套装)**
*   **获取方式**: 完成第四时代"古仙传承"主线，挑战"羽化台"历史回响地标。
*   **套装效果**: (2件) 所有引气技能触发率+10%。(3件) 你释放的第一个引气技能效果提升100%。

8.  **求道者拂尘**
    *   **品质**: 紫色
    *   **部位**: 武器 (法器)
    *   **故事**: "拂去心中尘埃，方能窥见大道。这曾是第一位引气者的随身之物。"
    *   **词条**:
        *   `[专属] 你的"五行"类技能可以额外附加一种随机的次级五行效果。`
        *   `引气技能伤害+15%。`
        *   `智力+20` (假设存在智力属性)

9.  **求道者法袍**
    *   **品质**: 紫色
    *   **部位**: 护甲
    *   **故事**: "衣袍之上并非凡间的丝线，而是流动的灵气。它能帮助穿着者更好地与天地沟通。"
    *   **词条**:
        *   `[专属] 你的"持续伤害"或"持续治疗"类效果，其持续时间延长2秒。`
        *   `法术抗性+20%。`
        *   `体质+15`

---
### **第六时代：血脉原罪**

#### **血脉枷锁系列 (套装)**
*   **获取方式**: 完成第六时代"血脉原罪"主线，并挑战"悔罪深渊"历史回响地标。
*   **套装效果**: (2件) 吸血效果提升50%。(3件) 当你通过吸血恢复生命时，有20%的概率获得一层"血怒"效果（伤害+5%，可叠加）。

10. **血脉的诅咒之刃**
    *   **品质**: 橙色
    *   **部位**: 武器 (匕首)
    *   **故事**: "这柄武器渴望鲜血，无论是敌人的，还是主人的。它是家族黑暗历史的具象化。"
    *   **词条**:
        *   `[专属] 你的所有攻击都附带"重伤"效果，使目标的受治疗效果降低25%。`
        *   `[专属] 每次攻击会牺牲你当前1%的生命值，转化为本次攻击2%的额外伤害。`
        *   `攻击速度+10%`

11. **血脉的悔恨之泪**
    *   **品质**: 橙色
    *   **部位**: 饰品
    *   **故事**: "一滴凝固的泪珠，其中包含了无数因血脉诅咒而逝去族人的悔恨与不甘。"
    *   **词条**:
        *   `[专属] 你的"百分比斩杀"类技能，其斩杀阈值提升2%（例如从10%提升至12%）。`
        *   `你的"生命值越低，伤害越高"类词条效果提升20%。`
        *   `所有技能触发率+3%`

---
### **第七时代：霸业崛起**

#### **开拓者系列 (套装)**
*   **获取方式**: 完成第七时代"霸业崛起"主线，挑战"中央王城"历史回响地标。
*   **套装效果**: (2件) 所有技能的"技能间隔"缩短10%。(4件) 你每释放5次不同的技能，就会发射一次"开阔者之炮"，造成一次无视防御的范围伤害。

12. **开拓者的议定之锤**
    *   **品质**: 橙色
    *   **部位**: 武器 (双手锤)
    *   **故事**: "这柄战锤敲定的不是钉子，而是一个个奠定帝国基石的法案。当然，偶尔也用来敲碎反对者的头颅。"
    *   **词条**:
        *   `[专属] 你的攻击有15%概率对目标及周围单位造成"破甲"和"减速"效果。`
        *   `你的"范围伤害"类技能，其影响范围扩大20%。`
        *   `力量+25`

13. **开拓者的无尽疆域图**
    *   **品质**: 橙色
    *   **部位**: 饰品 (卷轴)
    *   **故事**: "地图上标注的每一个点，都代表着家族的荣耀与野心。据说，当最后一个空白被填满时，持有者将看到世界的尽头。"
    *   **词条**:
        *   `[专属] 战斗中，你每击杀一个敌人，全属性提升1%，最多可叠加10层。`
        *   `非战斗状态下，地图探索速度+20%。`
        *   `所有基础属性+8%`

14. **开拓者的指令战靴**
    *   **品质**: 橙色
    *   **部位**: 靴子
    *   **故事**: "穿上它，你迈出的每一步都是命令。追随者们将紧随其后，踏平一切障碍。"
    *   **词条**:
        *   `[专属] 你的"位移"或"突进"类技能，使用后会使你下一次攻击的伤害提升50%。`
        *   `你的"攻击速度"词条效果提升15%。`
        *   `敏捷+25`

---
### **第九时代：薪火升华**

#### **薪火传承系列 (套装)**
*   **获取方式**: 完成第九时代"薪火升华"最终主线。
*   **套装效果**: (2件) 所有仙道技能触发时，有25%概率不进入技能间隔。(3件) 获得"薪火"效果：你死亡后，会化作一团不灭的火焰，在10秒内持续为你最强的盟友提供巨额属性加成，并治疗他们。

15. **薪火的最后一缕光**
    *   **品质**: 红色 (传说)
    *   **部位**: 武器 (法杖)
    *   **故事**: "当世界归于沉寂，最后的希望凝聚于此。它不再是武器，而是下一个纪元的火种。"
    *   **词条**:
        *   `[专属] 你的所有治疗效果和护盾效果，会将其20%溢出部分转化为对目标的永久生命值上限提升（战斗内）。`
        *   `[专属] 你的"复活"类技能，会使被复活的目标获得短暂的无敌效果。`
        *   `所有技能触发概率+10%`

16. **薪火的守望者誓言**
    *   **品质**: 红色 (传说)
    *   **部位**: 护甲
    *   **故事**: "我将在此守望，直到星辰熄灭，直到下一个春天来临。——末代家主"
    *   **词条**:
        *   `[专属] 当你存活时，所有友方单位受到的伤害降低15%。`
        *   `[专属] 你免疫所有"即死"效果。`
        *   `所有抗性+25%`

</rewritten_file>