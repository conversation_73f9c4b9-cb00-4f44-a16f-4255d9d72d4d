---
alwaysApply: true
---
---
description: "Context7 MCP工具使用指南，指导AI在处理技术问题时自动使用最新文档，确保代码和方案基于最新实践"
globs:
  - "*.js"
  - "*.ts"
  - "*.jsx"
  - "*.tsx"
  - "*.vue"
  - "*.py"
  - "*.go"
  - "*.rs"
  - "*.wxml"
  - "*.wxss"
  - "*.json"
  - "app.json"
  - "project.config.json"
  - "project.miniapp.json"
  - "app.miniapp.json"
alwaysApply: true
---

# Context7 MCP 工具使用指南

## 核心目标
本规则旨在指导 AI 在处理技术问题时，自主判断并使用 `Context7` MCP工具，确保提供的代码、解释和方案基于相关库或框架的最新、最准确的文档。

## 触发条件
当用户的请求涉及以下情况时，AI 应优先考虑使用 `Context7`：

1.  **特定库与框架**: 提问内容包含具体的、快速迭代的前端或后端技术，例如：
    *   前端: React, Next.js, Vue, Svelte
    *   后端: Express, NestJS, Django, FastAPI
    *   其他: tRPC, GraphQL, etc.

2.  **外部 API 调用**: 需要使用第三方服务 API，例如：
    *   支付: Stripe, PayPal
    *   地图: Google Maps, Mapbox
    *   云服务: AWS SDK, Google Cloud APIs
    *   通讯: Twilio

3.  **版本敏感性**: 当用户提及特定版本号，或项目依赖中存在明确的版本约束时。

4.  **知识不确定性**: 当 AI 意识到自身知识库可能落后于当前最新实践时。

## 执行方式
一旦满足上述任一条件，AI 在处理请求时，应在内部查询或生成代码的提示词（Prompt）末尾附加 `use context7`。

### 示例
**用户提问**: "如何用最新的 React Hooks 来获取数据？"

**AI 内部处理**: 将用户的提问转化为附带 `use context7` 的指令，然后执行后续操作。这样做可以确保获取到 React 最新的官方推荐方法，而不是使用已过时的生命周期函数。

## 最终目的
通过本规则，最大化地利用 `Context7` 的实时信息获取能力，减少因信息滞后而导致的错误或低效建议，提升代码质量和开发效率。

