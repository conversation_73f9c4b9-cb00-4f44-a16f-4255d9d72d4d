# 核心数据结构设计

**文档类型**: 数据设计
**版本号**: v2.0
**创建日期**: 2024-12-19
**最后更新**: 2025-01-05
**维护者**: 数据架构师
**状态**: 已发布

---

## 📖 文档概述

本文档定义月球RPG的核心数据结构，是所有系统（战斗、技能、装备、AI等）交互的基石。提供统一的数据模型和接口规范，确保系统间的数据一致性。

## 🎯 设计目标

1. **单一数据源**：作为所有核心实体数据结构的权威定义
2. **抽象与继承**：通过基础结构减少重复，提高扩展性
3. **清晰解耦**：结构定义明确，模块间数据解耦
4. **组件化设计**：复杂实体采用组件化架构，便于组合扩展

## 📋 相关文档

### 系统设计
- [角色属性系统](../03_系统设计/03_04_角色属性系统.md) - 角色属性组件定义
- [装备系统](../03_系统设计/03_01_装备系统.md) - 装备数据结构
- [技能系统](../03_系统设计/03_03_技能系统.md) - 技能数据结构
- [材料系统](../03_系统设计/03_02_材料系统.md) - 材料数据结构

### 技术规范
- [系统依赖关系图](../系统依赖关系图.md) - 数据流向和接口规范
- [参数系统](./05_01_参数系统.md) - 游戏参数数据结构

---

## 🎯 设计原则

### 1. 单一数据源 (Single Source of Truth)
本文档是游戏中所有核心实体数据结构的唯一权威定义。其他设计文档应引用而非重复定义。

### 2. 抽象与继承
通过定义通用的基础结构（如 `BaseEntity`, `BaseItem`），最大限度地减少重复，使设计更简洁、更易于扩展。

### 3. 清晰与解耦
结构定义清晰，字段名明确。不同模块的数据尽可能解耦，以提高系统的灵活性和可维护性。

### 4. 面向组件 (Component-Oriented)
部分复杂实体（如角色）采用面向组件的设计思想，将不同功能（如属性、状态、技能列表）拆分为独立的组件，便于组合和扩展。

---

## 1. 基础实体 (Base Entities)

### 1.1 `BaseEntity` - 基础战斗实体

所有能够参与战斗的单位（玩家、NPC、敌人）都继承自此结构。

```json
{
  "id": "string", // 唯一标识符, e.g., "player_001", "enemy_goblin_005"
  "name": "string", // 显示名称, e.g., "哥布林投手"
  "level": "number", // 等级
  "attributes": "AttributeComponent", // 核心属性组件
  "status": "StatusComponent", // 实时状态组件 (生命、护盾等)
  "state": "StateComponent", // 状态效果组件 (buff/debuff)
  "skills": "SkillComponent" // 技能组件
}
```

### 1.2 `Character` - 玩家角色

继承自 `BaseEntity`，并增加了玩家特有的系统。

```json
{
  // --- Inherited from BaseEntity ---
  "id": "string",
  "name": "string", 
  "level": "number",
  "attributes": "AttributeComponent",
  "status": "StatusComponent",
  "state": "StateComponent",
  "skills": "SkillComponent",
  // --- Character Specific ---
  "experience": "number", // 当前经验值
  "experienceToNextLevel": "number", // 升级所需经验值
  "attributePoints": "number", // 可用属性点
  "equipment": "EquipmentComponent", // 装备组件
  "inventory": "InventoryComponent", // 背包组件
  "currency": { // 货币
    "gold": "number"
  }
}
```

### 1.3 `Enemy` - 敌人

继承自 `BaseEntity`，并增加了AI和掉落相关定义。

```json
{
  // --- Inherited from BaseEntity ---
  "id": "string",
  "name": "string", 
  "level": "number",
  "attributes": "AttributeComponent",
  "status": "StatusComponent",
  "state": "StateComponent",
  "skills": "SkillComponent",
  // --- Enemy Specific ---
  "enemyType": "string", // e.g., "Normal", "Elite", "Boss"
  "faction": "string", // 势力, e.g., "Wilderness", "WesternArmy"
  "lootTableId": "string" // 关联的掉落表ID
}
```

---

## 2. 核心组件 (Core Components)

这些是构成 `BaseEntity` 的可复用模块。

### 2.1 `AttributeComponent` - 属性组件

存储角色的核心三维属性和衍生的战斗参数。

```json
{
  // 核心三维属性 (分配点数)
  "strength": "number",
  "agility": "number",
  "constitution": "number",

  // 战斗参数 (由三维、装备、Buff等计算得出)
  "attack": "number", // 攻击力
  "healthMax": "number", // 最大生命值
  "defense": "number", // 防御
  "hitRate": "number", // 命中率
  "dodgeRate": "number", // 闪躲率
  "critRate": "number", // 暴击率
  "critDamage": "number", // 暴击伤害
  "comboRate": "number", // 连击率
  
  // 高级战斗参数
  "lifeSteal": "number", // 吸血
  "damageReflection": "number", // 伤害反弹
  "armorPenetration": "number", // 护甲穿透
  "magicPenetration": "number", // 法术穿透
  "movementSpeed": "number" // 移动速度
}
```

### 2.2 `StatusComponent` - 状态组件

存储战斗中频繁变化的数值。

```json
{
  "currentHealth": "number",
  "currentShield": "number"
}
```

### 2.3 `StateComponent` - 状态效果组件

管理实体身上的所有临时状态效果（Buffs / Debuffs）。

```json
{
  "activeStates": [ // 效果数组
    {
      "stateId": "string", // e.g., "buff_strength_up", "debuff_poison"
      "name": "string", // e.g., "力量祝福", "中毒"
      "duration": "number", // 剩余持续时间 (秒), -1 为永久
      "tickRate": "number", // 每隔多少秒触发一次效果 (用于DoT/HoT)
      "effects": [ // 具体效果描述
        {
          "attribute": "string", // 影响的属性, e.g., "strength", "currentHealth"
          "modifier": "string", // 修改方式, e.g., "ADD", "MULTIPLY"
          "value": "number" // 修改值
        }
      ],
      "source": "string" // 效果来源, e.g., "skill_holy_light"
    }
  ]
}
```

### 2.4 `SkillComponent` - 技能组件

```json
{
  "knownSkills": [
    {
      "skillId": "string", // 技能的唯一ID, e.g., "skill_fireball"
      "level": "number" // 技能等级
    }
  ]
}
```

### 2.5 `EquipmentComponent` - 装备组件

管理角色身上穿戴的装备。

```json
{
  "weapon": "string | null", // 装备实例的唯一ID
  "armor": "string | null",
  "helmet": "string | null",
  "gloves": "string | null",
  "belt": "string | null",
  "boots": "string | null"
}
```

### 2.6 `InventoryComponent` - 背包组件

```json
{
  "capacity": "number", // 背包容量
  "items": [
    {
      "itemId": "string", // 物品的模板ID, e.g., "material_iron_ore"
      "instanceId": "string | null", // 物品的实例ID (仅装备类物品拥有)
      "quantity": "number" // 数量
    }
  ]
}
```

---

## 3. 物品系统 (Item System)

### 3.1 `BaseItem` - 基础物品

所有物品的基类，定义了通用属性。

```json
{
  "id": "string", // 物品模板ID, e.g., "eq_sword_01", "mat_iron_ore"
  "name": "string", // 名称
  "description": "string", // 描述
  "icon": "string", // 图标资源路径
  "quality": "string", // 品质: White, Green, Blue, Purple, Orange, Red
  "itemType": "string", // 类型: Equipment, Material, Consumable
  "stackable": "boolean", // 是否可堆叠
  "maxStack": "number" // 最大堆叠数量
}
```

### 3.2 `EquipmentItem` - 装备物品

继承自 `BaseItem`。这是装备的 **模板定义**。

```json
{
  // --- Inherited from BaseItem ---
  "id": "string",
  "name": "string",
  // ...
  "itemType": "Equipment",
  "stackable": false,
  "maxStack": 1,
  // --- Equipment Specific ---
  "slot": "string", // 装备部位: Weapon, Armor, etc.
  "levelRequirement": "number", // 穿戴等级
  "baseStats": { // 基础属性范围
    "attack": { "min": "number", "max": "number" }
  },
  "affixPool": ["string"] // 可能出现的词条ID池
}
```

### 3.3 `EquipmentInstance` - 装备实例

当一件装备被"创造"（掉落、打造）出来后，会根据 `EquipmentItem` 模板生成一个拥有具体数值的实例。**玩家背包里和身上的都是实例。**

```json
{
  "instanceId": "string", // 装备实例的唯一ID, e.g., "inst_eq_sword_01_xyz"
  "templateId": "string", // 模板ID, 关联到 EquipmentItem
  "baseStats": { // 本次生成的具体基础属性
    "attack": "number"
  },
  "affixes": [ // 本次生成的具体词条
    {
      "affixId": "string",
      "value": "number"
    }
  ],
  "enhancementLevel": 0 // 强化等级
}
```

### 3.4 `MaterialItem` - 材料物品

继承自 `BaseItem`。

```json
{
  // --- Inherited from BaseItem ---
  "id": "string",
  "name": "string",
  // ...
  "itemType": "Material",
  "stackable": true,
  "maxStack": 999
  // --- Material Specific ---
  // (No extra fields needed for now)
}
```

### 3.5 `ConsumableItem` - 消耗品

继承自 `BaseItem`。

```json
{
  // --- Inherited from BaseItem ---
  "id": "string",
  "name": "string",
  // ...
  "itemType": "Consumable",
  "stackable": true,
  "maxStack": 99,
  // --- Consumable Specific ---
  "useEffect": { // 使用效果
    "type": "string", // e.g., "HEAL", "APPLY_BUFF"
    "value": "number | string" // e.g., 100 (for HEAL), "buff_haste" (for APPLY_BUFF)
  }
}
```

---

## 4. 技能系统 (Skill System)

### 4.1 `Skill` - 技能定义

```json
{
  "id": "string",
  "name": "string",
  "description": "string",
  "icon": "string",
  "maxLevel": "number",
  "type": "string", // e.g., "Passive", "Active_Targeted", "Active_AOE"
  "cooldown": "number", // 冷却时间 (秒)
  "resourceCost": "number", // 资源消耗 (e.g., mana, rage)
  "effects": [ // 技能效果，随等级成长
    {
      "level": 1,
      "description": "造成120%武器伤害",
      "actions": [
        {
          "type": "DAMAGE",
          "damageType": "PHYSICAL",
          "scale": 1.2, // 伤害倍率
          "base": 10 // 基础伤害
        }
      ]
    },
    {
      "level": 2,
      "description": "造成140%武器伤害，并附带减速",
      "actions": [
        { "type": "...", "scale": 1.4, "base": 20 },
        { "type": "APPLY_STATE", "stateId": "debuff_slow" }
      ]
    }
  ]
}
```

---
**文档版本**: v1.0
**创建日期**: 2024-07-30
**设计者**: AI 