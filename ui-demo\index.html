<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>月球(Yueqiu) - 古风RPG移动端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            background: linear-gradient(135deg, #2c1810 0%, #1a0f0a 100%);
            color: #f4e4bc;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 古风背景纹理 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60"><g fill-opacity="0.03"><polygon fill="%23f4e4bc" points="30,0 60,30 30,60 0,30"/></g></svg>');
            z-index: -1;
        }

        /* 顶部状态栏 */
        .status-bar {
            height: 60px;
            background: linear-gradient(90deg, rgba(139, 69, 19, 0.9) 0%, rgba(101, 67, 33, 0.9) 100%);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            border-bottom: 2px solid #d4af37;
            box-shadow: 0 2px 10px rgba(212, 175, 55, 0.3);
        }

        .clan-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .clan-emblem {
            font-size: 1.8em;
            filter: drop-shadow(0 0 8px #d4af37);
        }

        .clan-details {
            line-height: 1.2;
        }

        .clan-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #d4af37;
        }

        .generation {
            font-size: 0.8em;
            color: #c9b037;
        }

        .resources {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .resource-item {
            display: flex;
            align-items: center;
            gap: 3px;
            background: rgba(0, 0, 0, 0.4);
            padding: 3px 6px;
            border-radius: 10px;
            border: 1px solid #d4af37;
            font-size: 0.75em;
        }

        .resource-value {
            font-weight: bold;
            color: #ffd700;
        }

        /* 主内容区 */
        .main-content {
            height: calc(100vh - 60px - 70px);
            overflow-y: auto;
            overflow-x: hidden;
            position: relative;
        }

        .content-panel {
            display: none;
            padding: 15px;
            min-height: 100%;
        }

        .content-panel.active {
            display: block;
        }

        /* 底部导航 */
        .bottom-nav {
            height: 70px;
            background: linear-gradient(90deg, rgba(139, 69, 19, 0.95) 0%, rgba(101, 67, 33, 0.95) 100%);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 2px solid #d4af37;
            box-shadow: 0 -2px 10px rgba(212, 175, 55, 0.3);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            background: none;
            border: none;
            color: #c9b037;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
            flex: 1;
            max-width: 80px;
        }

        .nav-item.active {
            background: rgba(212, 175, 55, 0.2);
            color: #d4af37;
            transform: scale(1.05);
        }

        .nav-icon {
            font-size: 1.4em;
            filter: drop-shadow(0 0 4px currentColor);
        }

        .nav-label {
            font-size: 0.7em;
            font-weight: bold;
        }

        /* 通用样式 */
        .panel-title {
            font-size: 1.4em;
            font-weight: bold;
            color: #d4af37;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .card {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .btn {
            background: linear-gradient(45deg, #d4af37, #b8941f);
            color: #2c1810;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
        }

        .btn:active {
            transform: scale(0.95);
            box-shadow: 0 1px 4px rgba(212, 175, 55, 0.5);
        }

        /* 主城样式 */
        .city-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .city-building {
            background: linear-gradient(135deg, rgba(139, 69, 19, 0.4) 0%, rgba(101, 67, 33, 0.4) 100%);
            border: 2px solid #d4af37;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .city-building:active {
            transform: scale(0.95);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
        }

        .building-icon {
            font-size: 2.5em;
            margin-bottom: 8px;
            filter: drop-shadow(0 0 8px #d4af37);
        }

        .building-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 4px;
        }

        .building-desc {
            font-size: 0.8em;
            color: #c9b037;
            opacity: 0.9;
        }

        .talent-tree-entrance {
            background: linear-gradient(90deg, rgba(139, 69, 19, 0.6) 0%, rgba(101, 67, 33, 0.6) 100%);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .talent-tree-entrance:active {
            transform: scale(0.98);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.5);
        }

        .talent-icon {
            font-size: 2.5em;
            filter: drop-shadow(0 0 8px #d4af37);
        }

        .talent-info {
            flex: 1;
        }

        .talent-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 4px;
        }

        .talent-desc {
            font-size: 0.9em;
            color: #c9b037;
            opacity: 0.9;
        }

        .talent-arrow {
            font-size: 1.5em;
            color: #d4af37;
        }

        /* 角色模块样式 */
        .character-portrait {
            text-align: center;
            margin-bottom: 20px;
        }

        .portrait-frame {
            position: relative;
            display: inline-block;
            margin-bottom: 10px;
        }

        .character-avatar {
            font-size: 4em;
            filter: drop-shadow(0 0 15px #d4af37);
        }

        .character-level {
            position: absolute;
            top: -5px;
            right: -10px;
            background: linear-gradient(45deg, #d4af37, #b8941f);
            color: #2c1810;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7em;
            font-weight: bold;
        }

        .character-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 4px;
        }

        .character-title {
            font-size: 0.9em;
            color: #c9b037;
            margin-bottom: 15px;
        }

        .exp-section {
            background: rgba(139, 69, 19, 0.3);
            border: 1px solid #d4af37;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .exp-label {
            font-size: 0.9em;
            color: #c9b037;
            margin-bottom: 8px;
        }

        .exp-bar {
            width: 100%;
            height: 12px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .exp-fill {
            height: 100%;
            background: linear-gradient(90deg, #d4af37, #ffd700);
            transition: width 0.3s ease;
        }

        .exp-text {
            font-size: 0.8em;
            color: #ffd700;
            text-align: center;
        }

        .equipment-section, .attributes-section, .profession-section {
            margin-bottom: 20px;
        }

        .equipment-title, .attr-title, .profession-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 15px;
            text-align: center;
        }

        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .equipment-slot {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid;
            border-radius: 10px;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .equipment-slot.legendary { border-color: #ff6b35; }
        .equipment-slot.epic { border-color: #9b59b6; }
        .equipment-slot.rare { border-color: #3498db; }
        .equipment-slot.uncommon { border-color: #27ae60; }
        .equipment-slot.common { border-color: #95a5a6; }
        .equipment-slot.empty { border-color: #34495e; }

        .slot-icon {
            font-size: 1.5em;
            margin-bottom: 5px;
        }

        .slot-name {
            font-size: 0.7em;
            color: #c9b037;
            margin-bottom: 3px;
        }

        .item-name {
            font-size: 0.6em;
            font-weight: bold;
            color: #d4af37;
        }

        .attr-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .attr-group {
            background: rgba(139, 69, 19, 0.3);
            border: 1px solid #d4af37;
            border-radius: 10px;
            padding: 15px;
        }

        .attr-group-title {
            font-size: 0.9em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 10px;
            text-align: center;
        }

        .attr-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .attr-name {
            font-size: 0.8em;
            color: #c9b037;
        }

        .attr-value {
            font-size: 0.8em;
            font-weight: bold;
            color: #ffd700;
        }

        .profession-section {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .profession-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .profession-icon {
            font-size: 2em;
            filter: drop-shadow(0 0 6px #d4af37);
        }

        .profession-info {
            flex: 1;
        }

        .profession-name {
            font-size: 1em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 4px;
        }

        .profession-level {
            font-size: 0.8em;
            color: #c9b037;
            margin-bottom: 6px;
        }

        .profession-bar {
            width: 100%;
            height: 8px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 4px;
            overflow: hidden;
        }

        .profession-fill {
            height: 100%;
            background: linear-gradient(90deg, #d4af37, #ffd700);
            transition: width 0.3s ease;
        }

        .profession-arrow {
            font-size: 1.2em;
            color: #d4af37;
        }

        /* 仓库模块样式 */
        .storage-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
        }

        .storage-tab {
            flex: 1;
            background: rgba(139, 69, 19, 0.3);
            border: 1px solid #d4af37;
            border-radius: 8px;
            padding: 8px 12px;
            color: #c9b037;
            font-size: 0.8em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .storage-tab.active {
            background: linear-gradient(45deg, #d4af37, #b8941f);
            color: #2c1810;
            font-weight: bold;
        }

        .storage-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(139, 69, 19, 0.3);
            border: 1px solid #d4af37;
            border-radius: 8px;
        }

        .storage-capacity {
            font-size: 0.9em;
            color: #d4af37;
            font-weight: bold;
        }

        .expand-btn {
            font-size: 0.8em;
            padding: 6px 12px;
        }

        .storage-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
        }

        .storage-item {
            aspect-ratio: 1;
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
        }

        .storage-item.empty {
            border-color: #34495e;
            background: rgba(52, 73, 94, 0.2);
        }

        .storage-item.legendary { border-color: #ff6b35; }
        .storage-item.epic { border-color: #9b59b6; }
        .storage-item.rare { border-color: #3498db; }
        .storage-item.uncommon { border-color: #27ae60; }
        .storage-item.common { border-color: #95a5a6; }

        .storage-item:active {
            transform: scale(0.95);
        }

        .item-icon {
            font-size: 1.2em;
            margin-bottom: 2px;
        }

        .item-count {
            position: absolute;
            bottom: 2px;
            right: 2px;
            background: rgba(0, 0, 0, 0.8);
            color: #ffd700;
            font-size: 0.6em;
            font-weight: bold;
            padding: 1px 3px;
            border-radius: 3px;
            min-width: 12px;
            text-align: center;
        }

        /* 冒险模块样式 */
        .current-area {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .area-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .area-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #d4af37;
        }

        .area-progress {
            font-size: 0.9em;
            color: #4ecdc4;
        }

        .area-description {
            font-size: 0.8em;
            color: #c9b037;
            margin-bottom: 15px;
            font-style: italic;
        }

        .area-rewards {
            display: flex;
            gap: 15px;
        }

        .reward-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.8em;
        }

        .reward-icon {
            font-size: 1.2em;
        }

        .reward-text {
            color: #ffd700;
        }

        .battle-area {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .enemy-display {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .enemy-avatar {
            font-size: 3em;
            filter: drop-shadow(0 0 10px #ff6b6b);
        }

        .enemy-info {
            flex: 1;
        }

        .enemy-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 5px;
        }

        .enemy-level {
            font-size: 0.8em;
            color: #ff9f43;
            margin-bottom: 10px;
        }

        .health-bar {
            width: 100%;
            height: 12px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 5px;
        }

        .health-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ff4757);
            transition: width 0.3s ease;
        }

        .health-text {
            font-size: 0.8em;
            color: #ff6b6b;
            text-align: center;
        }

        .battle-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .battle-btn {
            flex: 1;
            background: linear-gradient(45deg, #d4af37, #b8941f);
            color: #2c1810;
            border: none;
            padding: 12px;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .battle-btn:active {
            transform: scale(0.95);
        }

        .btn-icon {
            font-size: 1.2em;
        }

        .battle-stats {
            display: flex;
            justify-content: space-around;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 10px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-label {
            font-size: 0.8em;
            color: #c9b037;
        }

        .stat-value {
            font-size: 1em;
            font-weight: bold;
            color: #ffd700;
        }

        .era-selection {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 15px;
        }

        .era-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #d4af37;
            text-align: center;
            margin-bottom: 15px;
        }

        .era-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .era-item {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #666;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .era-item.unlocked {
            border-color: #d4af37;
        }

        .era-item.current {
            background: rgba(212, 175, 55, 0.2);
            border-color: #ffd700;
        }

        .era-item.locked {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .era-icon {
            font-size: 1.8em;
            margin-bottom: 8px;
        }

        .era-name {
            font-size: 0.9em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 4px;
        }

        .era-desc {
            font-size: 0.7em;
            color: #c9b037;
        }

        /* 技能模块样式 */
        .skill-categories {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }

        .skill-category {
            flex: 1;
            background: rgba(139, 69, 19, 0.3);
            border: 1px solid #d4af37;
            border-radius: 8px;
            padding: 8px 12px;
            color: #c9b037;
            font-size: 0.8em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .skill-category.active {
            background: linear-gradient(45deg, #d4af37, #b8941f);
            color: #2c1810;
            font-weight: bold;
        }

        .skill-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .skill-item {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .skill-item.locked {
            opacity: 0.6;
            border-color: #666;
        }

        .skill-icon {
            font-size: 2em;
            filter: drop-shadow(0 0 6px #d4af37);
        }

        .skill-info {
            flex: 1;
        }

        .skill-name {
            font-size: 1em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 4px;
        }

        .skill-level {
            font-size: 0.8em;
            color: #c9b037;
            margin-bottom: 6px;
        }

        .skill-desc {
            font-size: 0.7em;
            color: #b8941f;
            margin-bottom: 8px;
        }

        .skill-progress {
            width: 100%;
            height: 6px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 3px;
            overflow: hidden;
        }

        .skill-fill {
            height: 100%;
            background: linear-gradient(90deg, #d4af37, #ffd700);
            transition: width 0.3s ease;
        }

        .skill-upgrade-btn {
            background: linear-gradient(45deg, #d4af37, #b8941f);
            color: #2c1810;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: bold;
            cursor: pointer;
            font-size: 0.8em;
        }

        /* 技能装备栏样式 */
        .skill-equipped-section {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .equipped-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 15px;
            text-align: center;
        }

        .equipped-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }

        .skill-slot {
            background: rgba(139, 69, 19, 0.4);
            border: 2px solid #666;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .skill-slot.equipped {
            border-color: #d4af37;
            background: rgba(212, 175, 55, 0.2);
        }

        .skill-slot.empty {
            border-style: dashed;
            opacity: 0.6;
        }

        .skill-slot.selected {
            border-color: #ffd700;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
            transform: scale(1.05);
        }

        .skill-slot .skill-icon {
            font-size: 1.5em;
            margin-bottom: 5px;
        }

        .skill-slot .skill-name {
            font-size: 0.7em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 2px;
        }

        .skill-slot .skill-level {
            font-size: 0.6em;
            color: #c9b037;
        }

        .skill-slot .skill-probability {
            font-size: 0.65em;
            color: #4ecdc4;
            font-weight: bold;
            margin-top: 2px;
        }

        .skill-equipped-mark {
            position: absolute;
            top: 2px;
            right: 2px;
            background: #4ecdc4;
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 0.6em;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 概率面板样式 */
        .probability-panel {
            background: rgba(78, 205, 196, 0.1);
            border: 1px solid #4ecdc4;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .probability-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background: rgba(78, 205, 196, 0.2);
        }

        .probability-label {
            font-size: 0.9em;
            color: #4ecdc4;
            font-weight: bold;
        }

        .probability-toggle {
            background: none;
            border: 1px solid #4ecdc4;
            border-radius: 4px;
            color: #4ecdc4;
            font-size: 0.7em;
            padding: 4px 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .probability-toggle:hover {
            background: rgba(78, 205, 196, 0.2);
        }

        .probability-details {
            padding: 0 15px 12px 15px;
        }

        .probability-breakdown {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .breakdown-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8em;
            color: #b8941f;
        }

        .breakdown-value {
            color: #4ecdc4;
            font-weight: bold;
        }

        #total-probability {
            color: #4ecdc4;
            font-weight: bold;
            font-size: 1.1em;
        }

        /* 技能背包样式 */
        .skill-inventory {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
        }

        /* ===== 冒险系统样式 ===== */
        
        /* 模式选择界面 */
        .mode-selection {
            padding: 20px;
        }
        
        .adventure-modes {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .mode-card {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .mode-card:hover {
            border-color: #ffd700;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
            transform: translateY(-2px);
        }
        
        .mode-icon {
            font-size: 2.5em;
            width: 60px;
            text-align: center;
        }
        
        .mode-info {
            flex: 1;
        }
        
        .mode-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 5px;
        }
        
        .mode-desc {
            font-size: 0.9em;
            color: #c9b037;
            margin-bottom: 5px;
        }
        
        .mode-progress {
            font-size: 0.8em;
            color: #4ecdc4;
            font-weight: bold;
        }
        
        .mode-rewards {
            display: flex;
            flex-direction: column;
            gap: 3px;
            align-items: flex-end;
        }
        
        .mode-rewards .reward {
            font-size: 0.7em;
            color: #b8941f;
            background: rgba(0, 0, 0, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        /* 地图选择界面 */
        .map-selection {
            padding: 15px;
        }
        
        .map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #d4af37;
        }
        
        .back-btn {
            background: rgba(139, 69, 19, 0.5);
            border: 1px solid #d4af37;
            border-radius: 6px;
            color: #d4af37;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            background: rgba(212, 175, 55, 0.2);
            border-color: #ffd700;
        }
        
        .map-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #d4af37;
        }
        
        .map-progress {
            font-size: 0.9em;
            color: #4ecdc4;
            font-weight: bold;
        }
        
        /* 关卡地图 */
        .stage-map {
            margin-bottom: 20px;
            background: rgba(139, 69, 19, 0.2);
            border-radius: 12px;
            padding: 20px;
        }
        
        .stage-path {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }
        
        .stage-node {
            background: rgba(139, 69, 19, 0.4);
            border: 2px solid #666;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .stage-node.completed {
            border-color: #4ecdc4;
            background: rgba(78, 205, 196, 0.15);
        }
        
        .stage-node.current {
            border-color: #d4af37;
            background: rgba(212, 175, 55, 0.2);
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
        }
        
        .stage-node.locked {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .stage-node.boss {
            border-color: #e74c3c;
            background: rgba(231, 76, 60, 0.15);
        }
        
        .stage-node.selected {
            border-color: #ffd700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
            transform: scale(1.05);
        }
        
        .stage-node:hover:not(.locked) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .stage-icon {
            font-size: 2em;
            margin-bottom: 8px;
        }
        
        .stage-name {
            font-size: 0.9em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 5px;
        }
        
        .stage-level {
            font-size: 0.7em;
            color: #c9b037;
        }
        
        /* 关卡信息 */
        .stage-info {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 20px;
        }
        
        .info-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #d4af37;
        }
        
        .stage-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #d4af37;
        }
        
        .stage-difficulty {
            font-size: 0.9em;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 6px;
            background: rgba(0, 0, 0, 0.3);
        }
        
        .stage-description {
            font-size: 0.9em;
            color: #c9b037;
            line-height: 1.4;
            margin-bottom: 15px;
        }
        
        .stage-requirements {
            background: rgba(78, 205, 196, 0.1);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
        }
        
        .req-item {
            font-size: 0.8em;
            color: #4ecdc4;
            margin-bottom: 3px;
        }
        
        .stage-rewards {
            background: rgba(212, 175, 55, 0.1);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
        }
        
        .reward-title {
            font-size: 0.9em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 8px;
        }
        
        .reward-list {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }
        
        .reward-item {
            font-size: 0.8em;
            color: #c9b037;
        }
        
        .enter-battle-btn {
            width: 100%;
            background: linear-gradient(45deg, #d4af37, #ffd700);
            color: #2c1810;
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .enter-battle-btn:hover {
            background: linear-gradient(45deg, #ffd700, #d4af37);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
        }
        
        /* 战斗界面 */
        .battle-screen {
            padding: 15px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .battle-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #d4af37;
        }
        
        .battle-back-btn {
            background: rgba(231, 76, 60, 0.3);
            border: 1px solid #e74c3c;
            border-radius: 50%;
            color: #e74c3c;
            width: 35px;
            height: 35px;
            cursor: pointer;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .battle-back-btn:hover {
            background: rgba(231, 76, 60, 0.5);
            border-color: #ff6b6b;
        }
        
        .battle-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #d4af37;
        }
        
        .battle-wave {
            font-size: 0.9em;
            color: #4ecdc4;
            font-weight: bold;
        }
        
        /* 战斗队伍 */
        .player-team, .enemy-team {
            margin-bottom: 15px;
        }
        
        .team-side {
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        
        .hero-slot, .enemy-slot {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 12px;
            text-align: center;
            min-width: 80px;
        }
        
        .enemy-slot {
            border-color: #e74c3c;
            background: rgba(231, 76, 60, 0.2);
        }
        
        .enemy-slot.active {
            border-color: #ff6b6b;
            box-shadow: 0 0 15px rgba(231, 76, 60, 0.5);
        }
        
        .hero-avatar, .enemy-avatar {
            font-size: 2em;
            margin-bottom: 5px;
        }
        
        .hero-name, .enemy-name {
            font-size: 0.8em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 5px;
        }
        
        .enemy-name {
            color: #e74c3c;
        }
        
        .enemy-level {
            font-size: 0.7em;
            color: #c9b037;
            margin-bottom: 5px;
        }
        
        .hero-hp, .hero-mp, .enemy-hp {
            margin-bottom: 3px;
        }
        
        .hp-bar, .mp-bar {
            width: 100%;
            height: 6px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 2px;
        }
        
        .hp-fill {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c, #ff6b6b);
            transition: width 0.5s ease;
        }
        
        .mp-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #5dade2);
            transition: width 0.5s ease;
        }
        
        .hp-text, .mp-text {
            font-size: 0.6em;
            color: #c9b037;
        }
        
        /* 战斗控制 */
        .battle-controls {
            margin-bottom: 15px;
        }
        
        /* 装备技能显示区域 */
        .equipped-skills-display {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 15px;
            border: 2px solid #3498db;
        }
        
        .skills-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #4a6741;
        }
        
        .skills-title {
            font-size: 16px;
            font-weight: bold;
            color: #3498db;
        }
        
        .total-trigger-rate {
            font-size: 14px;
            font-weight: bold;
            color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
        }
        
        .skills-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .skill-display-item {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.05);
            padding: 8px 12px;
            border-radius: 8px;
            border-left: 3px solid #f39c12;
        }
        
        .skill-display-item .skill-icon {
            font-size: 20px;
            margin-right: 12px;
            width: 24px;
            text-align: center;
        }
        
        .skill-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex: 1;
        }
        
        .skill-name {
            font-size: 14px;
            color: #ecf0f1;
            font-weight: 500;
        }
        
        .skill-rate {
            font-size: 13px;
            color: #2ecc71;
            font-weight: bold;
            background: rgba(46, 204, 113, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        /* 自动战斗控制区域 */
        .auto-battle-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #444;
        }
        
        .battle-control-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .battle-control-btn.start-battle {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
        }
        
        .battle-control-btn.start-battle:hover {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            transform: translateY(-2px);
        }
        
        .battle-control-btn.stop-battle {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }
        
        .battle-control-btn.stop-battle:hover {
            background: linear-gradient(45deg, #c0392b, #e74c3c);
            transform: translateY(-2px);
        }
        
        .battle-options {
            display: flex;
            justify-content: center;
            gap: 10px;
        }
        
        .option-btn {
            background: rgba(139, 69, 19, 0.4);
            border: 1px solid #d4af37;
            border-radius: 6px;
            color: #d4af37;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }
        
        .option-btn:hover {
            background: rgba(212, 175, 55, 0.2);
            border-color: #ffd700;
        }
        
        /* 战斗信息 */
        .battle-info {
            margin-bottom: 15px;
        }
        
        .battle-stats {
            display: flex;
            justify-content: space-around;
            background: rgba(139, 69, 19, 0.3);
            border-radius: 8px;
            padding: 10px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-label {
            font-size: 0.7em;
            color: #c9b037;
        }
        
        .stat-value {
            font-size: 0.8em;
            font-weight: bold;
            color: #4ecdc4;
        }
        
        /* 战斗日志 */
        .battle-log {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #666;
            border-radius: 8px;
            padding: 10px;
            height: 80px;
            overflow-y: auto;
            flex: 1;
        }
        
        .log-item {
            font-size: 0.7em;
            color: #c9b037;
            margin-bottom: 3px;
            line-height: 1.3;
        }

        .inventory-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 15px;
            text-align: center;
        }

        .skill-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .skill-card {
            background: rgba(139, 69, 19, 0.4);
            border: 2px solid #666;
            border-radius: 10px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .skill-card.martial {
            border-color: #cd853f;
        }

        .skill-card.qi {
            border-color: #4ecdc4;
        }

        .skill-card.immortal {
            border-color: #9b59b6;
        }

        .skill-card.equipped {
            background: rgba(212, 175, 55, 0.15);
            border-color: #d4af37;
        }

        .skill-card.locked {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .skill-card:not(.locked):hover {
            transform: scale(1.02);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .skill-card .skill-icon {
            font-size: 1.5em;
            margin-bottom: 8px;
            text-align: center;
        }

        .skill-card .skill-name {
            font-size: 0.9em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 4px;
        }

        .skill-card .skill-level {
            font-size: 0.7em;
            color: #c9b037;
            margin-bottom: 6px;
        }

        .skill-card .skill-desc {
            font-size: 0.6em;
            color: #b8941f;
            line-height: 1.3;
        }

        .skill-equipped-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #4ecdc4;
            color: white;
            font-size: 0.6em;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: bold;
        }

        /* 技能预设样式 */
        .skill-presets {
            background: rgba(139, 69, 19, 0.3);
            border: 2px solid #d4af37;
            border-radius: 12px;
            padding: 15px;
        }

        .presets-title {
            font-size: 1.1em;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 15px;
            text-align: center;
        }

        .preset-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .preset-btn {
            background: rgba(139, 69, 19, 0.4);
            border: 1px solid #d4af37;
            border-radius: 8px;
            padding: 8px 12px;
            color: #c9b037;
            font-size: 0.8em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .preset-btn.active {
            background: linear-gradient(45deg, #d4af37, #b8941f);
            color: #2c1810;
            font-weight: bold;
        }

        .preset-btn.save {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4caf50;
            color: #4caf50;
        }

        .preset-btn:hover:not(.active) {
            background: rgba(212, 175, 55, 0.2);
        }
    </style>
</head>
<body>
    <!-- 顶部状态栏 -->
    <header class="status-bar">
        <div class="clan-info">
            <div class="clan-emblem">🐲</div>
            <div class="clan-details">
                <div class="clan-name">烈阳一族</div>
                <div class="generation">第三代族长</div>
            </div>
        </div>
        <div class="resources">
            <div class="resource-item">
                <span>🪙</span>
                <span class="resource-value">125,438</span>
            </div>
            <div class="resource-item">
                <span>💎</span>
                <span class="resource-value">2,451</span>
            </div>
            <div class="resource-item">
                <span>⚡</span>
                <span class="resource-value">85/100</span>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="main-content">
        <!-- 主城模块 -->
        <div id="city-content" class="content-panel active">
            <div class="panel-title">🏯 家族主城</div>
            
            <!-- 主城功能网格 -->
            <div class="city-grid">
                <div class="city-building" onclick="openBuilding('blacksmith')">
                    <div class="building-icon">⚒️</div>
                    <div class="building-name">铁匠铺</div>
                    <div class="building-desc">装备强化·制作</div>
                </div>
                
                <div class="city-building" onclick="openBuilding('shop')">
                    <div class="building-icon">🏪</div>
                    <div class="building-name">商店</div>
                    <div class="building-desc">购买道具</div>
                </div>
                
                <div class="city-building" onclick="openBuilding('blackmarket')">
                    <div class="building-icon">🌙</div>
                    <div class="building-name">黑市</div>
                    <div class="building-desc">特殊交易</div>
                </div>
                
                <div class="city-building" onclick="openBuilding('shrine')">
                    <div class="building-icon">🏛️</div>
                    <div class="building-name">祖祠</div>
                    <div class="building-desc">家族传承</div>
                </div>
                
                <div class="city-building" onclick="openBuilding('pharmacy')">
                    <div class="building-icon">🧪</div>
                    <div class="building-name">药店</div>
                    <div class="building-desc">购买·制作药剂</div>
                </div>
                
                <div class="city-building" onclick="openBuilding('training')">
                    <div class="building-icon">🎯</div>
                    <div class="building-name">训练营</div>
                    <div class="building-desc">技能测试</div>
                </div>
            </div>
            

        </div>

        <!-- 角色模块 -->
        <div id="character-content" class="content-panel">
            <div class="panel-title">👤 角色信息</div>
            
            <!-- 人物立绘区域 -->
            <div class="character-portrait">
                <div class="portrait-frame">
                    <div class="character-avatar">🧙‍♂️</div>
                    <div class="character-level">Lv.47</div>
                </div>
                <div class="character-name">李炎阳</div>
                <div class="character-title">烈阳一族 · 第三代族长</div>
            </div>
            
            <!-- 经验条 -->
            <div class="exp-section">
                <div class="exp-label">经验值</div>
                <div class="exp-bar">
                    <div class="exp-fill" style="width: 73%"></div>
                </div>
                <div class="exp-text">2,341,567 / 3,200,000</div>
            </div>
            
            <!-- 装备展示区域 -->
            <div class="equipment-section">
                <div class="equipment-title">⚔️ 装备展示</div>
                <div class="equipment-grid">
                    <div class="equipment-slot legendary" data-slot="weapon" onclick="showEquipmentDetails('weapon')">
                        <div class="slot-icon">⚔️</div>
                        <div class="slot-name">武器</div>
                        <div class="item-name">烈阳神剑</div>
                    </div>
                    <div class="equipment-slot epic" data-slot="armor" onclick="showEquipmentDetails('armor')">
                        <div class="slot-icon">🛡️</div>
                        <div class="slot-name">护甲</div>
                        <div class="item-name">龙鳞战甲</div>
                    </div>
                    <div class="equipment-slot rare" data-slot="helmet" onclick="showEquipmentDetails('helmet')">
                        <div class="slot-icon">⛑️</div>
                        <div class="slot-name">头盔</div>
                        <div class="item-name">凤翎冠</div>
                    </div>
                    <div class="equipment-slot uncommon" data-slot="boots" onclick="showEquipmentDetails('boots')">
                        <div class="slot-icon">🥾</div>
                        <div class="slot-name">靴子</div>
                        <div class="item-name">疾风靴</div>
                    </div>
                    <div class="equipment-slot common" data-slot="accessory1" onclick="showEquipmentDetails('accessory1')">
                        <div class="slot-icon">💍</div>
                        <div class="slot-name">饰品1</div>
                        <div class="item-name">力量之戒</div>
                    </div>
                    <div class="equipment-slot empty" data-slot="accessory2" onclick="showEquipmentDetails('accessory2')">
                        <div class="slot-icon">💍</div>
                        <div class="slot-name">饰品2</div>
                        <div class="item-name">未装备</div>
                    </div>
                </div>
            </div>
            
            <!-- 属性面板 -->
            <div class="attributes-section" onclick="showCharacterDetails()">
                <div class="attr-title">📊 角色属性 (点击查看详情)</div>
                <div class="attr-grid">
                    <div class="attr-group">
                        <div class="attr-group-title">基础属性</div>
                        <div class="attr-item">
                            <span class="attr-name">力量</span>
                            <span class="attr-value">156</span>
                        </div>
                        <div class="attr-item">
                            <span class="attr-name">敏捷</span>
                            <span class="attr-value">134</span>
                        </div>
                        <div class="attr-item">
                            <span class="attr-name">体质</span>
                            <span class="attr-value">142</span>
                        </div>
                    </div>
                    <div class="attr-group">
                        <div class="attr-group-title">战斗属性</div>
                        <div class="attr-item">
                            <span class="attr-name">攻击力</span>
                            <span class="attr-value">2,456</span>
                        </div>
                        <div class="attr-item">
                            <span class="attr-name">防御力</span>
                            <span class="attr-value">1,843</span>
                        </div>
                        <div class="attr-item">
                            <span class="attr-name">生命值</span>
                            <span class="attr-value">8,540</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 副职业进度 -->
            <div class="profession-section" onclick="showProfessionDetails()">
                <div class="profession-title">🔨 副职业等级</div>
                <div class="profession-item">
                    <div class="profession-icon">⚒️</div>
                    <div class="profession-info">
                        <div class="profession-name">铁匠</div>
                        <div class="profession-level">等级 67/100</div>
                        <div class="profession-bar">
                            <div class="profession-fill" style="width: 34%"></div>
                        </div>
                    </div>
                    <div class="profession-arrow">▶</div>
                </div>
            </div>
        </div>

        <!-- 冒险模块 -->
        <div id="adventure-content" class="content-panel">
            <!-- 冒险模式选择界面 -->
            <div id="adventure-mode-selection" class="mode-selection">
                <div class="panel-title">🗺️ 冒险世界</div>
                
                <!-- 冒险模式分类 -->
                <div class="adventure-modes">
                    <div class="mode-card" onclick="showMapSelection('era')">
                        <div class="mode-icon">📚</div>
                        <div class="mode-info">
                            <div class="mode-name">时代征程</div>
                            <div class="mode-desc">体验九大时代的家族传奇</div>
                            <div class="mode-progress">进度: 2/9 时代</div>
                        </div>
                        <div class="mode-rewards">
                            <span class="reward">💰 金币</span>
                            <span class="reward">⚡ 经验</span>
                            <span class="reward">🎁 装备</span>
                        </div>
                    </div>
                    
                    <div class="mode-card" onclick="showMapSelection('challenge')">
                        <div class="mode-icon">⚔️</div>
                        <div class="mode-info">
                            <div class="mode-name">挑战秘境</div>
                            <div class="mode-desc">高难度副本，丰厚奖励</div>
                            <div class="mode-progress">今日: 2/5 次</div>
                        </div>
                        <div class="mode-rewards">
                            <span class="reward">💎 钻石</span>
                            <span class="reward">🌟 稀有装备</span>
                            <span class="reward">📜 技能书</span>
                        </div>
                    </div>
                    
                    <div class="mode-card" onclick="showMapSelection('trial')">
                        <div class="mode-icon">🗼</div>
                        <div class="mode-info">
                            <div class="mode-name">试炼之塔</div>
                            <div class="mode-desc">层层递进的无尽挑战</div>
                            <div class="mode-progress">当前: 45/100 层</div>
                        </div>
                        <div class="mode-rewards">
                            <span class="reward">🔥 传承星火</span>
                            <span class="reward">💫 天赋点</span>
                            <span class="reward">👑 称号</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 地图选择界面 -->
            <div id="adventure-map-selection" class="map-selection" style="display: none;">
                <div class="map-header">
                    <button class="back-btn" onclick="backToModeSelection()">← 返回</button>
                    <div class="map-title">第二时代 - 血脉分歧</div>
                    <div class="map-progress">进度: 8/12</div>
                </div>
                
                <!-- 关卡地图 -->
                <div class="stage-map">
                    <div class="stage-path">
                        <!-- 已完成关卡 -->
                        <div class="stage-node completed" onclick="selectStage(1, '血脉觉醒')">
                            <div class="stage-icon">✅</div>
                            <div class="stage-name">血脉觉醒</div>
                            <div class="stage-level">Lv.40</div>
                        </div>
                        
                        <div class="stage-node completed" onclick="selectStage(2, '族内纷争')">
                            <div class="stage-icon">✅</div>
                            <div class="stage-name">族内纷争</div>
                            <div class="stage-level">Lv.42</div>
                        </div>
                        
                        <div class="stage-node completed" onclick="selectStage(3, '长老试练')">
                            <div class="stage-icon">✅</div>
                            <div class="stage-name">长老试练</div>
                            <div class="stage-level">Lv.44</div>
                        </div>
                        
                        <!-- 当前关卡 -->
                        <div class="stage-node current" onclick="selectStage(4, '分支之战')">
                            <div class="stage-icon">🔥</div>
                            <div class="stage-name">分支之战</div>
                            <div class="stage-level">Lv.46</div>
                        </div>
                        
                        <!-- 未解锁关卡 -->
                        <div class="stage-node locked">
                            <div class="stage-icon">🔒</div>
                            <div class="stage-name">血脉融合</div>
                            <div class="stage-level">Lv.48</div>
                        </div>
                        
                        <!-- Boss关卡 -->
                        <div class="stage-node boss locked">
                            <div class="stage-icon">👑</div>
                            <div class="stage-name">时代之主</div>
                            <div class="stage-level">Lv.50</div>
                        </div>
                    </div>
                </div>
                
                <!-- 关卡信息 -->
                <div class="stage-info">
                    <div class="info-header">
                        <div class="stage-title">分支之战</div>
                        <div class="stage-difficulty">💀 困难</div>
                    </div>
                    <div class="stage-description">
                        家族血脉分化到了关键时刻，各分支为了传承权展开激烈争夺...
                    </div>
                    <div class="stage-requirements">
                        <div class="req-item">💪 推荐战力: 12,000</div>
                        <div class="req-item">⚡ 体力消耗: 10</div>
                        <div class="req-item">🏆 通关奖励: 首次×2</div>
                    </div>
                    <div class="stage-rewards">
                        <div class="reward-title">关卡奖励</div>
                        <div class="reward-list">
                            <div class="reward-item">💰 金币 2,000-3,500</div>
                            <div class="reward-item">⚡ 经验 5,000</div>
                            <div class="reward-item">🎁 装备掉落几率 15%</div>
                            <div class="reward-item">📜 技能书碎片 × 3</div>
                        </div>
                    </div>
                    <button class="enter-battle-btn" onclick="enterBattle('分支之战', 4)">
                        进入战斗
                    </button>
                </div>
            </div>
            
            <!-- 战斗界面 -->
            <div id="adventure-battle" class="battle-screen" style="display: none;">
                <div class="battle-header">
                    <button class="battle-back-btn" onclick="exitBattle()">✕</button>
                    <div class="battle-title">分支之战 - 第1波</div>
                    <div class="battle-wave">1/3</div>
                </div>
                
                <!-- 我方阵容 -->
                <div class="player-team">
                    <div class="team-side">
                        <div class="hero-slot main-hero">
                            <div class="hero-avatar">🧙‍♂️</div>
                            <div class="hero-name">李炎阳</div>
                            <div class="hero-hp">
                                <div class="hp-bar">
                                    <div class="hp-fill" style="width: 85%"></div>
                                </div>
                                <div class="hp-text">8,540/10,000</div>
                            </div>
                            <div class="hero-mp">
                                <div class="mp-bar">
                                    <div class="mp-fill" style="width: 60%"></div>
                                </div>
                                <div class="mp-text">600/1,000</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 敌方阵容 -->
                <div class="enemy-team">
                    <div class="team-side">
                        <div class="enemy-slot active">
                            <div class="enemy-avatar">⚔️</div>
                            <div class="enemy-name">分支族长</div>
                            <div class="enemy-level">Lv.46</div>
                            <div class="enemy-hp">
                                <div class="hp-bar">
                                    <div class="hp-fill" style="width: 65%"></div>
                                </div>
                                <div class="hp-text">32,500/50,000</div>
                            </div>
                        </div>
                        <div class="enemy-slot">
                            <div class="enemy-avatar">🛡️</div>
                            <div class="enemy-name">护卫</div>
                            <div class="enemy-level">Lv.44</div>
                            <div class="enemy-hp">
                                <div class="hp-bar">
                                    <div class="hp-fill" style="width: 100%"></div>
                                </div>
                                <div class="hp-text">25,000/25,000</div>
                            </div>
                        </div>
                        <div class="enemy-slot">
                            <div class="enemy-avatar">🏹</div>
                            <div class="enemy-name">弓手</div>
                            <div class="enemy-level">Lv.43</div>
                            <div class="enemy-hp">
                                <div class="hp-bar">
                                    <div class="hp-fill" style="width: 90%"></div>
                                </div>
                                <div class="hp-text">18,000/20,000</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 战斗控制 -->
                <div class="battle-controls">
                    <!-- 装备技能显示 -->
                    <div class="equipped-skills-display">
                        <div class="skills-header">
                            <span class="skills-title">装备技能</span>
                            <span class="total-trigger-rate" id="total-trigger-rate">总触发率: 85.3%</span>
                        </div>
                        <div class="skills-list">
                            <div class="skill-display-item">
                                <div class="skill-icon">🔥</div>
                                <div class="skill-info">
                                    <span class="skill-name">烈阳斩</span>
                                    <span class="skill-rate">28.5%</span>
                                </div>
                            </div>
                            <div class="skill-display-item">
                                <div class="skill-icon">⚡</div>
                                <div class="skill-info">
                                    <span class="skill-name">雷霆击</span>
                                    <span class="skill-rate">25.2%</span>
                                </div>
                            </div>
                            <div class="skill-display-item">
                                <div class="skill-icon">🌟</div>
                                <div class="skill-info">
                                    <span class="skill-name">家族秘技</span>
                                    <span class="skill-rate">31.6%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 自动战斗控制 -->
                    <div class="auto-battle-controls">
                        <button class="battle-control-btn start-battle" onclick="toggleAutoBattle()" id="auto-battle-btn">
                            <span class="btn-icon">▶️</span>
                            <span class="btn-text">开始战斗</span>
                        </button>
                        <div class="battle-options">
                            <button class="option-btn" onclick="toggleBattleSpeed()">
                                <span id="speed-text">⚡ 1x</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 战斗信息 -->
                <div class="battle-info">
                    <div class="battle-stats">
                        <div class="stat-item">
                            <span class="stat-label">回合:</span>
                            <span class="stat-value">15</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">伤害:</span>
                            <span class="stat-value">45,230</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">时间:</span>
                            <span class="stat-value">2:30</span>
                        </div>
                    </div>
                </div>
                
                <!-- 战斗日志区域 -->
                <div class="battle-log">
                    <div class="log-item">🔥 李炎阳 使用 烈阳斩，对分支族长造成 3,245 点伤害</div>
                    <div class="log-item">⚔️ 分支族长 普攻，对李炎阳造成 1,560 点伤害</div>
                    <div class="log-item">⚡ 李炎阳 技能触发：雷霆印记，额外伤害 856</div>
                </div>
            </div>
        </div>

        <!-- 仓库模块 -->
        <div id="storage-content" class="content-panel">
            <div class="panel-title">📦 家族仓库</div>
            
            <!-- 仓库分类标签 -->
            <div class="storage-tabs">
                <button class="storage-tab active" onclick="switchStorageTab('equipment')">装备</button>
                <button class="storage-tab" onclick="switchStorageTab('items')">道具</button>
                <button class="storage-tab" onclick="switchStorageTab('materials')">材料</button>
                <button class="storage-tab" onclick="switchStorageTab('others')">其他</button>
            </div>
            
            <!-- 仓库信息 -->
            <div class="storage-info">
                <div class="storage-capacity">容量: 18/30</div>
                <button class="btn expand-btn" onclick="expandStorage()">扩容仓库</button>
            </div>
            
            <!-- 物品网格 -->
            <div class="storage-grid">
                <!-- 装备类物品 -->
                <div class="storage-item legendary" data-type="equipment" onclick="showStorageItemDetails('sword2')">
                    <div class="item-icon">⚔️</div>
                    <div class="item-count">1</div>
                </div>
                <div class="storage-item epic" data-type="equipment" onclick="showStorageItemDetails('armor2')">
                    <div class="item-icon">🛡️</div>
                    <div class="item-count">1</div>
                </div>
                <div class="storage-item rare" data-type="equipment" onclick="alert('🏹 精钢弩 (稀有武器)\\n\\n攻击力 +156\\n射程 +30%\\n\\n点击查看详情功能开发中...')">
                    <div class="item-icon">🏹</div>
                    <div class="item-count">2</div>
                </div>
                
                <!-- 道具类物品 -->
                <div class="storage-item common" data-type="items">
                    <div class="item-icon">🧪</div>
                    <div class="item-count">15</div>
                </div>
                <div class="storage-item common" data-type="items">
                    <div class="item-icon">💊</div>
                    <div class="item-count">32</div>
                </div>
                <div class="storage-item uncommon" data-type="items">
                    <div class="item-icon">📜</div>
                    <div class="item-count">5</div>
                </div>
                
                <!-- 材料类物品 -->
                <div class="storage-item common" data-type="materials">
                    <div class="item-icon">⛏️</div>
                    <div class="item-count">124</div>
                </div>
                <div class="storage-item common" data-type="materials">
                    <div class="item-icon">🌿</div>
                    <div class="item-count">87</div>
                </div>
                <div class="storage-item rare" data-type="materials">
                    <div class="item-icon">💎</div>
                    <div class="item-count">8</div>
                </div>
                
                <!-- 其他类物品 -->
                <div class="storage-item common" data-type="others">
                    <div class="item-icon">🗝️</div>
                    <div class="item-count">3</div>
                </div>
                <div class="storage-item common" data-type="others">
                    <div class="item-icon">📦</div>
                    <div class="item-count">6</div>
                </div>
                
                <!-- 空槽位 -->
                <div class="storage-item empty"></div>
                <div class="storage-item empty"></div>
                <div class="storage-item empty"></div>
                <div class="storage-item empty"></div>
                <div class="storage-item empty"></div>
                <div class="storage-item empty"></div>
                <div class="storage-item empty"></div>
                <div class="storage-item empty"></div>
            </div>
        </div>

        <!-- 技能模块 -->
        <div id="skill-content" class="content-panel">
            <div class="panel-title">🌟 技能修炼</div>
            
            <!-- 技能装备栏 -->
            <div class="skill-equipped-section">
                <div class="equipped-title">⚔️ 上阵技能 (5/8) - 总概率: <span id="total-probability">0.0%</span></div>
                
                <!-- 概率统计面板 -->
                <div class="probability-panel">
                    <div class="probability-header">
                        <span class="probability-label">释放概率统计</span>
                        <button class="probability-toggle" onclick="toggleProbabilityDetails()">详情 ▼</button>
                    </div>
                    <div class="probability-details" id="probability-details" style="display: none;">
                        <div class="probability-breakdown">
                            <div class="breakdown-item">
                                <span>装备加成:</span>
                                <span class="breakdown-value">+12.5%</span>
                            </div>
                            <div class="breakdown-item">
                                <span>套装加成:</span>
                                <span class="breakdown-value">+8.0%</span>
                            </div>
                            <div class="breakdown-item">
                                <span>等级加成:</span>
                                <span class="breakdown-value" id="level-bonus">+0.0%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="equipped-grid">
                    <div class="skill-slot equipped" data-slot="0" onclick="selectSkillSlot(0)">
                        <div class="skill-icon">⚔️</div>
                        <div class="skill-name">烈阳剑法</div>
                        <div class="skill-level">Lv.15</div>
                        <div class="skill-probability">32.0%</div>
                        <div class="skill-equipped-mark">✓</div>
                    </div>
                    <div class="skill-slot equipped" data-slot="1" onclick="selectSkillSlot(1)">
                        <div class="skill-icon">🛡️</div>
                        <div class="skill-name">龙鳞护体</div>
                        <div class="skill-level">Lv.12</div>
                        <div class="skill-probability">30.3%</div>
                        <div class="skill-equipped-mark">✓</div>
                    </div>
                    <div class="skill-slot equipped" data-slot="2" onclick="selectSkillSlot(2)">
                        <div class="skill-icon">⚡</div>
                        <div class="skill-name">疾风步</div>
                        <div class="skill-level">Lv.8</div>
                        <div class="skill-probability">28.7%</div>
                        <div class="skill-equipped-mark">✓</div>
                    </div>
                    <div class="skill-slot equipped" data-slot="3" onclick="selectSkillSlot(3)">
                        <div class="skill-icon">🔥</div>
                        <div class="skill-name">烈火掌</div>
                        <div class="skill-level">Lv.6</div>
                        <div class="skill-probability">24.0%</div>
                        <div class="skill-equipped-mark">✓</div>
                    </div>
                    <div class="skill-slot equipped" data-slot="4" onclick="selectSkillSlot(4)">
                        <div class="skill-icon">❄️</div>
                        <div class="skill-name">寒冰诀</div>
                        <div class="skill-level">Lv.4</div>
                        <div class="skill-probability">23.3%</div>
                        <div class="skill-equipped-mark">✓</div>
                    </div>
                    <div class="skill-slot empty" data-slot="5" onclick="selectSkillSlot(5)">
                        <div class="skill-icon">+</div>
                        <div class="skill-name">空槽位</div>
                    </div>
                    <div class="skill-slot empty" data-slot="6" onclick="selectSkillSlot(6)">
                        <div class="skill-icon">+</div>
                        <div class="skill-name">空槽位</div>
                    </div>
                    <div class="skill-slot empty" data-slot="7" onclick="selectSkillSlot(7)">
                        <div class="skill-icon">+</div>
                        <div class="skill-name">空槽位</div>
                    </div>
                </div>
            </div>
            
            <!-- 技能分类 -->
            <div class="skill-categories">
                <button class="skill-category active" onclick="switchSkillCategory('martial')">武道传承</button>
                <button class="skill-category" onclick="switchSkillCategory('qi')">引气修炼</button>
                <button class="skill-category" onclick="switchSkillCategory('immortal')">仙道超越</button>
            </div>
            
            <!-- 技能背包 -->
            <div class="skill-inventory">
                <div class="inventory-title">📚 技能背包</div>
                <div class="skill-grid">
                    <!-- 武道技能 -->
                    <div class="skill-card martial equipped" data-skill="sword" onclick="toggleSkillEquip('sword')">
                        <div class="skill-icon">⚔️</div>
                        <div class="skill-name">烈阳剑法</div>
                        <div class="skill-level">Lv.15</div>
                        <div class="skill-desc">攻击时15%概率造成额外伤害</div>
                        <div class="skill-equipped-badge">已装备</div>
                    </div>
                    
                    <div class="skill-card martial equipped" data-skill="defense" onclick="toggleSkillEquip('defense')">
                        <div class="skill-icon">🛡️</div>
                        <div class="skill-name">龙鳞护体</div>
                        <div class="skill-level">Lv.12</div>
                        <div class="skill-desc">受攻击时12%概率减免伤害</div>
                        <div class="skill-equipped-badge">已装备</div>
                    </div>
                    
                    <div class="skill-card martial equipped" data-skill="speed" onclick="toggleSkillEquip('speed')">
                        <div class="skill-icon">⚡</div>
                        <div class="skill-name">疾风步</div>
                        <div class="skill-level">Lv.8</div>
                        <div class="skill-desc">攻击速度提升8%</div>
                        <div class="skill-equipped-badge">已装备</div>
                    </div>
                    
                    <div class="skill-card martial" data-skill="heavy_strike" onclick="toggleSkillEquip('heavy_strike')">
                        <div class="skill-icon">💪</div>
                        <div class="skill-name">重击</div>
                        <div class="skill-level">Lv.5</div>
                        <div class="skill-desc">攻击时5%概率造成200%伤害</div>
                    </div>
                    
                    <div class="skill-card martial" data-skill="counter" onclick="toggleSkillEquip('counter')">
                        <div class="skill-icon">🔄</div>
                        <div class="skill-name">反击</div>
                        <div class="skill-level">Lv.3</div>
                        <div class="skill-desc">受攻击时3%概率立即反击</div>
                    </div>
                    
                    <!-- 引气技能 -->
                    <div class="skill-card qi equipped" data-skill="fire" onclick="toggleSkillEquip('fire')" style="display: none;">
                        <div class="skill-icon">🔥</div>
                        <div class="skill-name">烈火掌</div>
                        <div class="skill-level">Lv.6</div>
                        <div class="skill-desc">攻击时6%概率造成灼烧效果</div>
                        <div class="skill-equipped-badge">已装备</div>
                    </div>
                    
                    <div class="skill-card qi equipped" data-skill="ice" onclick="toggleSkillEquip('ice')" style="display: none;">
                        <div class="skill-icon">❄️</div>
                        <div class="skill-name">寒冰诀</div>
                        <div class="skill-level">Lv.4</div>
                        <div class="skill-desc">攻击时4%概率冰冻敌人</div>
                        <div class="skill-equipped-badge">已装备</div>
                    </div>
                    
                    <div class="skill-card qi" data-skill="heal" onclick="toggleSkillEquip('heal')" style="display: none;">
                        <div class="skill-icon">💚</div>
                        <div class="skill-name">回春术</div>
                        <div class="skill-level">Lv.7</div>
                        <div class="skill-desc">攻击时7%概率恢复生命值</div>
                    </div>
                    
                    <!-- 仙道技能 -->
                    <div class="skill-card immortal" data-skill="thunder" onclick="toggleSkillEquip('thunder')" style="display: none;">
                        <div class="skill-icon">⚡</div>
                        <div class="skill-name">九天雷法</div>
                        <div class="skill-level">Lv.2</div>
                        <div class="skill-desc">攻击时2%概率召唤雷劫</div>
                    </div>
                    
                    <div class="skill-card immortal locked" data-skill="divine" style="display: none;">
                        <div class="skill-icon">🔒</div>
                        <div class="skill-name">神通术</div>
                        <div class="skill-level">未解锁</div>
                        <div class="skill-desc">需要达到仙道境界</div>
                    </div>
                </div>
            </div>
            
            <!-- 技能预设 -->
            <div class="skill-presets">
                <div class="presets-title">📋 技能预设</div>
                <div class="preset-buttons">
                    <button class="preset-btn active" onclick="loadPreset(1)">战斗配置</button>
                    <button class="preset-btn" onclick="loadPreset(2)">防御配置</button>
                    <button class="preset-btn" onclick="loadPreset(3)">自定义</button>
                    <button class="preset-btn save" onclick="savePreset()">保存当前</button>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <button class="nav-item active" onclick="switchPanel('city')">
            <div class="nav-icon">🏯</div>
            <div class="nav-label">主城</div>
        </button>
        <button class="nav-item" onclick="switchPanel('character')">
            <div class="nav-icon">👤</div>
            <div class="nav-label">角色</div>
        </button>
        <button class="nav-item" onclick="switchPanel('adventure')">
            <div class="nav-icon">🗺️</div>
            <div class="nav-label">冒险</div>
        </button>
        <button class="nav-item" onclick="switchPanel('storage')">
            <div class="nav-icon">📦</div>
            <div class="nav-label">仓库</div>
        </button>
        <button class="nav-item" onclick="switchPanel('skill')">
            <div class="nav-icon">🌟</div>
            <div class="nav-label">技能</div>
        </button>
    </nav>

    <script>
        // 面板切换功能
        function switchPanel(panelName) {
            // 隐藏所有面板
            document.querySelectorAll('.content-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            
            // 移除所有导航active状态
            document.querySelectorAll('.nav-item').forEach(nav => {
                nav.classList.remove('active');
            });
            
            // 显示目标面板
            document.getElementById(panelName + '-content').classList.add('active');
            
            // 设置当前导航为active
            event.target.closest('.nav-item').classList.add('active');
        }

        // 游戏状态
        let gameState = {
            autoMode: true,
            currentEnemy: {
                name: "前代族长分身",
                level: 45,
                currentHp: 37200,
                maxHp: 49600
            }
        };

        // 主城功能
        function openBuilding(buildingType) {
            switch(buildingType) {
                case 'training':
                    openTrainingGround();
                    break;
                case 'blacksmith':
                    openBlacksmith();
                    break;
                case 'shop':
                    openShop();
                    break;
                case 'blackmarket':
                    openMarket();
                    break;
                case 'shrine':
                    openShrine();
                    break;
                case 'pharmacy':
                    openPharmacy();
                    break;
                default:
                    alert("建筑功能开发中...");
            }
        }

        // 训练营系统
        let trainingState = {
            isTraining: false,
            totalDamage: 0,
            hitCount: 0,
            skillTriggers: 0,
            startTime: 0,
            dpsHistory: []
        };

        function openTrainingGround() {
            const trainingHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: #d4af37; margin-bottom: 15px;">🎯 训练营 - 技能测试场</h3>
                    
                    <div style="background: rgba(139, 69, 19, 0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">🎭 训练假人</h4>
                        <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 10px;">
                            <div style="font-size: 3em;">🎯</div>
                            <div>
                                <div style="color: #4ecdc4; font-weight: bold;">无限血量训练假人</div>
                                <div style="color: #c9b037; font-size: 0.9em;">不会反击，专门用于技能测试</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 10px;">📊 实时数据统计</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9em;">
                            <div>
                                <span style="color: #b8941f;">总伤害:</span>
                                <span style="color: #4ecdc4; font-weight: bold;" id="training-total-damage">0</span>
                            </div>
                            <div>
                                <span style="color: #b8941f;">攻击次数:</span>
                                <span style="color: #4ecdc4; font-weight: bold;" id="training-hit-count">0</span>
                            </div>
                            <div>
                                <span style="color: #b8941f;">技能触发:</span>
                                <span style="color: #4ecdc4; font-weight: bold;" id="training-skill-triggers">0</span>
                            </div>
                            <div>
                                <span style="color: #b8941f;">平均DPS:</span>
                                <span style="color: #4ecdc4; font-weight: bold;" id="training-dps">0</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">⚔️ 技能触发记录</h4>
                        <div style="max-height: 100px; overflow-y: auto; font-size: 0.8em;" id="training-skill-log">
                            <div style="color: #c9b037;">等待开始训练...</div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button id="training-start-btn" onclick="startTraining()" style="background: #d4af37; color: #2c1810; border: none; padding: 12px 24px; border-radius: 8px; margin-right: 10px; font-weight: bold;">开始训练</button>
                        <button onclick="resetTrainingData()" style="background: #e74c3c; color: white; border: none; padding: 12px 24px; border-radius: 8px; margin-right: 10px;">重置数据</button>
                        <button onclick="closeTrainingGround()" style="background: #666; color: white; border: none; padding: 12px 24px; border-radius: 8px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('训练营', trainingHtml);
        }

        function startTraining() {
            const startBtn = document.getElementById('training-start-btn');
            
            if (!trainingState.isTraining) {
                // 开始训练
                trainingState.isTraining = true;
                trainingState.startTime = Date.now();
                startBtn.textContent = '停止训练';
                startBtn.style.background = '#e74c3c';
                
                // 开始训练循环
                trainingLoop();
            } else {
                // 停止训练
                trainingState.isTraining = false;
                startBtn.textContent = '开始训练';
                startBtn.style.background = '#d4af37';
            }
        }

        function trainingLoop() {
            if (!trainingState.isTraining) return;
            
            // 模拟攻击
            const damage = calculateAttackDamage();
            trainingState.totalDamage += damage;
            trainingState.hitCount++;
            
            // 检查技能触发
            const skillTrigger = checkSkillTrigger();
            if (skillTrigger) {
                trainingState.skillTriggers++;
                logSkillTrigger(skillTrigger);
            }
            
            // 更新显示
            updateTrainingDisplay();
            
            // 继续循环
            setTimeout(trainingLoop, 800 + Math.random() * 400); // 0.8-1.2秒间隔
        }

        function calculateAttackDamage() {
            // 基础攻击力 (模拟计算)
            const baseAttack = 1200 + Math.random() * 400;
            const criticalChance = 0.15; // 15%暴击率
            const criticalMultiplier = 2.0;
            
            let finalDamage = baseAttack;
            if (Math.random() < criticalChance) {
                finalDamage *= criticalMultiplier;
            }
            
            return Math.floor(finalDamage);
        }

        function checkSkillTrigger() {
            // 获取当前技能触发概率
            const totalProbability = skillState.totalProbability || calculateTotalProbability();
            
            if (Math.random() * 100 < totalProbability) {
                // 随机选择一个技能
                const equippedSkills = skillState.allSkills.filter(skill => 
                    document.querySelector(`[data-skill="${skill.id}"]`)?.classList.contains('equipped')
                );
                
                if (equippedSkills.length > 0) {
                    const randomSkill = equippedSkills[Math.floor(Math.random() * equippedSkills.length)];
                    return {
                        skill: randomSkill,
                        damage: calculateSkillDamage(randomSkill),
                        time: Date.now()
                    };
                }
            }
            
            return null;
        }

        function calculateSkillDamage(skill) {
            const baseDamage = 1800 + Math.random() * 600;
            const skillMultiplier = 1.5 + (skill.level * 0.1);
            return Math.floor(baseDamage * skillMultiplier);
        }

        function logSkillTrigger(trigger) {
            const skillLog = document.getElementById('training-skill-log');
            if (skillLog) {
                const logEntry = document.createElement('div');
                logEntry.style.cssText = 'color: #4ecdc4; margin-bottom: 2px;';
                logEntry.textContent = `${trigger.skill.icon} ${trigger.skill.name} - 伤害: ${trigger.damage.toLocaleString()}`;
                skillLog.insertBefore(logEntry, skillLog.firstChild);
                
                // 限制日志条数
                while (skillLog.children.length > 8) {
                    skillLog.removeChild(skillLog.lastChild);
                }
            }
        }

        function updateTrainingDisplay() {
            const totalDamageEl = document.getElementById('training-total-damage');
            const hitCountEl = document.getElementById('training-hit-count');
            const skillTriggersEl = document.getElementById('training-skill-triggers');
            const dpsEl = document.getElementById('training-dps');
            
            if (totalDamageEl) totalDamageEl.textContent = trainingState.totalDamage.toLocaleString();
            if (hitCountEl) hitCountEl.textContent = trainingState.hitCount.toLocaleString();
            if (skillTriggersEl) skillTriggersEl.textContent = trainingState.skillTriggers.toLocaleString();
            
            // 计算DPS
            const elapsedTime = (Date.now() - trainingState.startTime) / 1000;
            const dps = elapsedTime > 0 ? Math.floor(trainingState.totalDamage / elapsedTime) : 0;
            if (dpsEl) dpsEl.textContent = dps.toLocaleString();
        }

        function resetTrainingData() {
            trainingState.isTraining = false;
            trainingState.totalDamage = 0;
            trainingState.hitCount = 0;
            trainingState.skillTriggers = 0;
            trainingState.startTime = 0;
            
            // 重置UI
            const startBtn = document.getElementById('training-start-btn');
            if (startBtn) {
                startBtn.textContent = '开始训练';
                startBtn.style.background = '#d4af37';
            }
            
            const skillLog = document.getElementById('training-skill-log');
            if (skillLog) {
                skillLog.innerHTML = '<div style="color: #c9b037;">等待开始训练...</div>';
            }
            
            updateTrainingDisplay();
        }

        function closeTrainingGround() {
            trainingState.isTraining = false;
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }



        // 铁匠铺系统
        function openBlacksmith() {
            const blacksmithHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: #d4af37; margin-bottom: 15px;">⚒️ 铁匠铺 - 装备强化</h3>
                    
                    <div style="background: rgba(139, 69, 19, 0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">🔨 强化服务</h4>
                        <div style="margin-bottom: 10px;">
                            <div style="color: #4ecdc4; font-weight: bold;">装备强化 (+0 → +10)</div>
                            <div style="color: #c9b037; font-size: 0.9em; margin-top: 5px;">
                                • +1-+3: 100%成功率，材料：低级强化石<br/>
                                • +4-+6: 80%成功率，材料：中级强化石<br/>
                                • +7-+10: 60%成功率，材料：高级强化石
                            </div>
                        </div>
                        <div style="margin-top: 10px;">
                            <button onclick="startEnhancement()" style="background: #d4af37; color: #2c1810; border: none; padding: 8px 16px; border-radius: 5px; margin-right: 10px;">选择装备强化</button>
                        </div>
                    </div>
                    
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 10px;">🔧 装备制作</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9em;">
                            <div style="background: rgba(139, 69, 19, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="color: #4ecdc4; font-weight: bold;">基础制作</div>
                                <div style="color: #c9b037; font-size: 0.8em;">95%成功率<br/>白色-绿色装备</div>
                                <div style="margin-top: 5px;">
                                    <button onclick="showCraftingDetails('basic')" style="background: #4ecdc4; color: #2c1810; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em;">查看</button>
                                </div>
                            </div>
                            <div style="background: rgba(139, 69, 19, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="color: #8e44ad; font-weight: bold;">高级制作</div>
                                <div style="color: #c9b037; font-size: 0.8em;">70%成功率<br/>蓝色-紫色装备</div>
                                <div style="margin-top: 5px;">
                                    <button onclick="showCraftingDetails('advanced')" style="background: #8e44ad; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em;">查看</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">💰 当前材料</h4>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; font-size: 0.8em;">
                            <div style="text-align: center;">
                                <div style="color: #4ecdc4;">🔹 低级强化石</div>
                                <div style="color: #ffd700; font-weight: bold;">237</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="color: #8e44ad;">🔷 中级强化石</div>
                                <div style="color: #ffd700; font-weight: bold;">64</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="color: #e74c3c;">🔶 高级强化石</div>
                                <div style="color: #ffd700; font-weight: bold;">12</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeBlacksmith()" style="background: #666; color: white; border: none; padding: 12px 24px; border-radius: 8px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('铁匠铺', blacksmithHtml);
        }

        function startEnhancement() {
            alert("⚒️ 装备强化\n\n请选择要强化的装备：\n\n⚔️ 烈阳神剑 (+6)\n强化至 +7 需要：\n• 高级强化石 ×3\n• 金币 ×50,000\n• 成功率：60%");
        }

        function showCraftingDetails(type) {
            if (type === 'basic') {
                alert("🔧 基础制作\n\n可制作装备：\n• 精钢长剑 (绿色武器)\n• 龙鳞护甲 (绿色护甲)\n• 疾风靴 (绿色靴子)\n\n材料需求：\n• 基础材料 ×5\n• 金币 ×10,000");
            } else {
                alert("🔧 高级制作\n\n可制作装备：\n• 烈焰之刃 (紫色武器)\n• 不灭战甲 (紫色护甲)\n• 天行靴 (紫色靴子)\n\n材料需求：\n• 稀有材料 ×3\n• 金币 ×100,000");
            }
        }

        function closeBlacksmith() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // 商店系统
        function openShop() {
            const shopHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: #d4af37; margin-bottom: 15px;">🏪 商店 - 装备道具</h3>
                    
                    <div style="background: rgba(139, 69, 19, 0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">⚔️ 装备商品</h4>
                        <div style="display: grid; grid-template-columns: 1fr; gap: 8px; font-size: 0.9em;">
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div>
                                    <span style="color: #4ecdc4;">🗡️ 精钢长剑 (绿色)</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">攻击力 +180</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ffd700; font-weight: bold;">15,000 金币</div>
                                    <button onclick="buyItem('sword')" style="background: #4ecdc4; color: #2c1810; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">购买</button>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div>
                                    <span style="color: #8e44ad;">🛡️ 龙鳞护甲 (蓝色)</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">防御力 +240</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ffd700; font-weight: bold;">35,000 金币</div>
                                    <button onclick="buyItem('armor')" style="background: #8e44ad; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">购买</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 10px;">💊 消耗道具</h4>
                        <div style="display: grid; grid-template-columns: 1fr; gap: 8px; font-size: 0.9em;">
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div>
                                    <span style="color: #e74c3c;">❤️ 大型治疗药水</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">恢复50%生命值</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ffd700; font-weight: bold;">500 金币</div>
                                    <button onclick="buyItem('heal_potion')" style="background: #e74c3c; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">购买</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">💰 当前金币</h4>
                        <div style="text-align: center; font-size: 1.2em; color: #ffd700; font-weight: bold;">
                            🪙 156,420 金币
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeShop()" style="background: #666; color: white; border: none; padding: 12px 24px; border-radius: 8px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('商店', shopHtml);
        }

        function buyItem(itemId) {
            const items = {
                sword: { name: '精钢长剑', price: 15000 },
                armor: { name: '龙鳞护甲', price: 35000 },
                heal_potion: { name: '大型治疗药水', price: 500 }
            };
            
            const item = items[itemId];
            if (item) {
                if (confirm(`确认购买 ${item.name}？\n价格：${item.price.toLocaleString()} 金币`)) {
                    alert(`✅ 购买成功！\n\n获得物品：${item.name}\n花费金币：${item.price.toLocaleString()}`);
                }
            }
        }

        function closeShop() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // 黑市系统
        function openMarket() {
            const marketHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: #d4af37; margin-bottom: 15px;">🌙 黑市 - 稀有交易</h3>
                    
                    <div style="background: rgba(139, 69, 19, 0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">💎 稀有物品</h4>
                        <div style="color: #c9b037; font-size: 0.9em; margin-bottom: 10px;">
                            黑市每日更新，稀有物品限量供应
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr; gap: 8px; font-size: 0.9em;">
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(255, 69, 0, 0.2); padding: 8px; border-radius: 5px; border: 1px solid #ff4500;">
                                <div>
                                    <span style="color: #ff4500;">🔥 传说技能书：天雷剑诀</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">稀有度：传说 | 剩余：1个</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ffd700; font-weight: bold;">2,000 积分</div>
                                    <button onclick="buyRareItem('thunder_skill')" style="background: #ff4500; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">兑换</button>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(138, 43, 226, 0.2); padding: 8px; border-radius: 5px; border: 1px solid #8a2be2;">
                                <div>
                                    <span style="color: #8a2be2;">🌟 紫色装备随机宝箱</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">必出紫色装备 | 剩余：3个</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ffd700; font-weight: bold;">800 积分</div>
                                    <button onclick="buyRareItem('purple_box')" style="background: #8a2be2; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">兑换</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 10px;">🔄 积分兑换</h4>
                        <div style="color: #c9b037; font-size: 0.9em; margin-bottom: 10px;">
                            通过分解东方装备获得积分
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; font-size: 0.9em;">
                            <div style="text-align: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="color: #4ecdc4;">普通抽奖</div>
                                <div style="color: #ffd700; font-weight: bold;">10 积分</div>
                                <div style="color: #c9b037; font-size: 0.8em;">保底绿色</div>
                                <button onclick="drawLottery('normal')" style="background: #4ecdc4; color: #2c1810; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 5px;">抽奖</button>
                            </div>
                            <div style="text-align: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="color: #8e44ad;">高级抽奖</div>
                                <div style="color: #ffd700; font-weight: bold;">50 积分</div>
                                <div style="color: #c9b037; font-size: 0.8em;">保底蓝色</div>
                                <button onclick="drawLottery('advanced')" style="background: #8e44ad; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 5px;">抽奖</button>
                            </div>
                            <div style="text-align: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="color: #ff4500;">顶级抽奖</div>
                                <div style="color: #ffd700; font-weight: bold;">200 积分</div>
                                <div style="color: #c9b037; font-size: 0.8em;">保底紫色</div>
                                <button onclick="drawLottery('premium')" style="background: #ff4500; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 5px;">抽奖</button>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">💰 当前积分</h4>
                        <div style="text-align: center; font-size: 1.2em; color: #ffd700; font-weight: bold;">
                            💎 1,247 积分
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeMarket()" style="background: #666; color: white; border: none; padding: 12px 24px; border-radius: 8px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('黑市', marketHtml);
        }

        function buyRareItem(itemId) {
            const items = {
                thunder_skill: { name: '天雷剑诀技能书', price: 2000 },
                purple_box: { name: '紫色装备随机宝箱', price: 800 }
            };
            
            const item = items[itemId];
            if (item) {
                if (confirm(`确认兑换 ${item.name}？\n价格：${item.price.toLocaleString()} 积分`)) {
                    alert(`✅ 兑换成功！\n\n获得物品：${item.name}\n花费积分：${item.price.toLocaleString()}`);
                }
            }
        }

        function drawLottery(type) {
            const lotteries = {
                normal: { name: '普通抽奖', price: 10, guarantee: '绿色装备' },
                advanced: { name: '高级抽奖', price: 50, guarantee: '蓝色装备' },
                premium: { name: '顶级抽奖', price: 200, guarantee: '紫色装备' }
            };
            
            const lottery = lotteries[type];
            if (lottery) {
                if (confirm(`确认进行 ${lottery.name}？\n价格：${lottery.price} 积分\n保底：${lottery.guarantee}`)) {
                    // 模拟抽奖结果
                    const rewards = ['精钢长剑', '龙鳞护甲', '疾风靴', '力量之戒'];
                    const randomReward = rewards[Math.floor(Math.random() * rewards.length)];
                    alert(`🎉 抽奖结果！\n\n获得物品：${randomReward} (${lottery.guarantee})\n花费积分：${lottery.price}`);
                }
            }
        }

        function closeMarket() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // 祖祠系统
        function openShrine() {
            const shrineHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: #d4af37; margin-bottom: 15px;">🏛️ 祖祠 - 家族传承</h3>
                    
                    <div style="background: rgba(139, 69, 19, 0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">🌳 家族天赋树</h4>
                        <div style="color: #c9b037; font-size: 0.9em; margin-bottom: 10px;">
                            血脉传承，永久属性提升
                        </div>
                        <div style="margin-bottom: 10px;">
                            <div style="color: #4ecdc4; font-weight: bold;">可用天赋点：15</div>
                            <div style="color: #c9b037; font-size: 0.9em;">通过轮回获得天赋点</div>
                        </div>
                        <button onclick="openTalentTreeDetails()" style="background: #d4af37; color: #2c1810; border: none; padding: 8px 16px; border-radius: 5px;">查看天赋树</button>
                    </div>
                    
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 10px;">👑 历代家主</h4>
                        <div style="display: grid; grid-template-columns: 1fr; gap: 8px; font-size: 0.9em;">
                            <div style="background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="color: #d4af37; font-weight: bold;">第三代：李炎阳 (当代家主)</div>
                                <div style="color: #c9b037; font-size: 0.8em;">称号：烈阳之子 | 轮回次数：47次</div>
                            </div>
                            <div style="background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="color: #8e44ad; font-weight: bold;">第二代：李天明</div>
                                <div style="color: #c9b037; font-size: 0.8em;">称号：天明破晓 | 轮回次数：156次</div>
                                <button onclick="viewAncestorDetails(2)" style="background: #8e44ad; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">查看详情</button>
                            </div>
                            <div style="background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="color: #ff4500; font-weight: bold;">第一代：李破天</div>
                                <div style="color: #c9b037; font-size: 0.8em;">称号：破天开族 | 轮回次数：89次</div>
                                <button onclick="viewAncestorDetails(1)" style="background: #ff4500; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">查看详情</button>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">⚡ 传承星火</h4>
                        <div style="text-align: center;">
                            <div style="color: #ffd700; font-weight: bold; font-size: 1.1em;">🔥 传承星火：7</div>
                            <div style="color: #c9b037; font-size: 0.9em; margin-top: 5px;">
                                用于扩展祖祠传承装备使用次数
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeShrine()" style="background: #666; color: white; border: none; padding: 12px 24px; border-radius: 8px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('祖祠', shrineHtml);
        }

        function openTalentTreeDetails() {
            const talentTreeHtml = `
                <div style="text-align: left; line-height: 1.5; max-height: 80vh; overflow-y: auto;">
                    <h3 style="color: #d4af37; margin-bottom: 15px;">🌳 家族天赋树 - 血脉传承</h3>
                    
                    <!-- 天赋点显示 -->
                    <div style="background: rgba(212, 175, 55, 0.2); padding: 15px; border-radius: 10px; margin-bottom: 15px; text-align: center;">
                        <div style="color: #4ecdc4; font-size: 1.2em; font-weight: bold; margin-bottom: 5px;">💫 可用天赋点：15</div>
                        <div style="color: #c9b037; font-size: 0.9em;">通过轮回获得天赋点，每次轮回根据成就获得1-3点</div>
                    </div>
                    
                    <!-- 武道传承天赋树 -->
                    <div style="background: rgba(231, 76, 60, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #e74c3c; margin-bottom: 10px;">⚔️ 武道传承天赋树</h4>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; font-size: 0.8em;">
                            <div onclick="upgradeTalent('warrior_power')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #4ecdc4;">
                                <div style="color: #4ecdc4; font-weight: bold;">🗡️ 武者之力</div>
                                <div style="color: #c9b037;">等级: 5/5 (已满)</div>
                                <div style="color: #666; font-size: 0.9em;">攻击力 +25%</div>
                            </div>
                            <div onclick="upgradeTalent('combat_mastery')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #f39c12;">
                                <div style="color: #f39c12; font-weight: bold;">🎯 战斗精通</div>
                                <div style="color: #c9b037;">等级: 3/5</div>
                                <div style="color: #666; font-size: 0.9em;">暴击率 +15%</div>
                                <div style="color: #e74c3c; font-size: 0.8em;">需要: 1天赋点</div>
                            </div>
                            <div onclick="upgradeTalent('weapon_expert')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #666;">
                                <div style="color: #666; font-weight: bold;">🏹 兵器专家</div>
                                <div style="color: #c9b037;">等级: 0/5</div>
                                <div style="color: #666; font-size: 0.9em;">全武器伤害 +10%</div>
                                <div style="color: #666; font-size: 0.8em;">需要: 战斗精通 Lv.3</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 引气修炼天赋树 -->
                    <div style="background: rgba(52, 152, 219, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin-bottom: 10px;">🌀 引气修炼天赋树</h4>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; font-size: 0.8em;">
                            <div onclick="upgradeTalent('qi_foundation')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #4ecdc4;">
                                <div style="color: #4ecdc4; font-weight: bold;">💨 气脉根基</div>
                                <div style="color: #c9b037;">等级: 5/5 (已满)</div>
                                <div style="color: #666; font-size: 0.9em;">内力上限 +50%</div>
                            </div>
                            <div onclick="upgradeTalent('qi_recovery')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #f39c12;">
                                <div style="color: #f39c12; font-weight: bold;">🔄 气息回复</div>
                                <div style="color: #c9b037;">等级: 2/5</div>
                                <div style="color: #666; font-size: 0.9em;">内力回复 +20%</div>
                                <div style="color: #e74c3c; font-size: 0.8em;">需要: 1天赋点</div>
                            </div>
                            <div onclick="upgradeTalent('qi_mastery')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #666;">
                                <div style="color: #666; font-weight: bold;">⚡ 真气掌控</div>
                                <div style="color: #c9b037;">等级: 0/3</div>
                                <div style="color: #666; font-size: 0.9em;">技能冷却 -15%</div>
                                <div style="color: #666; font-size: 0.8em;">需要: 气息回复 Lv.3</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 仙道超越天赋树 -->
                    <div style="background: rgba(155, 89, 182, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #9b59b6; margin-bottom: 10px;">🌟 仙道超越天赋树</h4>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; font-size: 0.8em;">
                            <div onclick="upgradeTalent('immortal_insight')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #f39c12;">
                                <div style="color: #f39c12; font-weight: bold;">👁️ 仙道感悟</div>
                                <div style="color: #c9b037;">等级: 1/3</div>
                                <div style="color: #666; font-size: 0.9em;">经验获得 +30%</div>
                                <div style="color: #e74c3c; font-size: 0.8em;">需要: 2天赋点</div>
                            </div>
                            <div onclick="upgradeTalent('transcendence')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #666;">
                                <div style="color: #666; font-weight: bold;">🌠 超凡入圣</div>
                                <div style="color: #c9b037;">等级: 0/1</div>
                                <div style="color: #666; font-size: 0.9em;">解锁仙级技能</div>
                                <div style="color: #666; font-size: 0.8em;">需要: 仙道感悟 Lv.3</div>
                            </div>
                            <div onclick="upgradeTalent('divine_protection')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #666;">
                                <div style="color: #666; font-weight: bold;">🛡️ 仙庇护持</div>
                                <div style="color: #c9b037;">等级: 0/5</div>
                                <div style="color: #666; font-size: 0.9em;">减伤 +25%</div>
                                <div style="color: #666; font-size: 0.8em;">需要: 超凡入圣</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 工艺传承天赋树 -->
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">🔨 工艺传承天赋树</h4>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; font-size: 0.8em;">
                            <div onclick="upgradeTalent('craft_skill')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #4ecdc4;">
                                <div style="color: #4ecdc4; font-weight: bold;">⚒️ 工艺技巧</div>
                                <div style="color: #c9b037;">等级: 3/5 (已满)</div>
                                <div style="color: #666; font-size: 0.9em;">制作成功率 +15%</div>
                            </div>
                            <div onclick="upgradeTalent('material_sense')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #f39c12;">
                                <div style="color: #f39c12; font-weight: bold;">💎 材料感知</div>
                                <div style="color: #c9b037;">等级: 2/3</div>
                                <div style="color: #666; font-size: 0.9em;">材料获得 +25%</div>
                                <div style="color: #e74c3c; font-size: 0.8em;">需要: 1天赋点</div>
                            </div>
                            <div onclick="upgradeTalent('master_craftsman')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #666;">
                                <div style="color: #666; font-weight: bold;">👑 大师工匠</div>
                                <div style="color: #c9b037;">等级: 0/1</div>
                                <div style="color: #666; font-size: 0.9em;">解锁传说制作</div>
                                <div style="color: #666; font-size: 0.8em;">需要: 材料感知 Lv.3</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 家主威望天赋树 -->
                    <div style="background: rgba(142, 68, 173, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #8e44ad; margin-bottom: 10px;">👑 家主威望天赋树</h4>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 8px; font-size: 0.8em;">
                            <div onclick="upgradeTalent('leadership')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #f39c12;">
                                <div style="color: #f39c12; font-weight: bold;">📯 统御之力</div>
                                <div style="color: #c9b037;">等级: 2/5</div>
                                <div style="color: #666; font-size: 0.9em;">金币获得 +20%</div>
                                <div style="color: #e74c3c; font-size: 0.8em;">需要: 1天赋点</div>
                            </div>
                            <div onclick="upgradeTalent('family_glory')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #666;">
                                <div style="color: #666; font-weight: bold;">✨ 家族荣耀</div>
                                <div style="color: #c9b037;">等级: 0/3</div>
                                <div style="color: #666; font-size: 0.9em;">全属性 +10%</div>
                                <div style="color: #666; font-size: 0.8em;">需要: 统御之力 Lv.3</div>
                            </div>
                            <div onclick="upgradeTalent('eternal_legacy')" style="background: rgba(0, 0, 0, 0.3); padding: 8px; border-radius: 5px; cursor: pointer; border: 2px solid #666;">
                                <div style="color: #666; font-weight: bold;">🌟 永恒传承</div>
                                <div style="color: #c9b037;">等级: 0/1</div>
                                <div style="color: #666; font-size: 0.9em;">轮回奖励翻倍</div>
                                <div style="color: #666; font-size: 0.8em;">需要: 家族荣耀 Lv.3</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="resetTalents()" style="background: #e74c3c; color: white; border: none; padding: 8px 16px; border-radius: 5px; margin-right: 10px;">重置天赋</button>
                        <button onclick="closeTalentTree()" style="background: #666; color: white; border: none; padding: 8px 16px; border-radius: 5px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('家族天赋树', talentTreeHtml);
        }

        function upgradeTalent(talentId) {
            const talents = {
                'combat_mastery': { name: '战斗精通', cost: 1, currentLevel: 3, maxLevel: 5 },
                'qi_recovery': { name: '气息回复', cost: 1, currentLevel: 2, maxLevel: 5 },
                'immortal_insight': { name: '仙道感悟', cost: 2, currentLevel: 1, maxLevel: 3 },
                'material_sense': { name: '材料感知', cost: 1, currentLevel: 2, maxLevel: 3 },
                'leadership': { name: '统御之力', cost: 1, currentLevel: 2, maxLevel: 5 }
            };
            
            const talent = talents[talentId];
            if (talent && talent.currentLevel < talent.maxLevel) {
                if (confirm(`升级 ${talent.name}？\n\n当前等级：${talent.currentLevel}/${talent.maxLevel}\n消耗天赋点：${talent.cost}\n剩余天赋点：15`)) {
                    alert(`✅ 天赋升级成功！\n\n${talent.name} 升级至 Lv.${talent.currentLevel + 1}\n消耗天赋点：${talent.cost}\n剩余天赋点：${15 - talent.cost}`);
                    // 这里会刷新天赋树显示
                    openTalentTreeDetails();
                }
            } else if (talent && talent.currentLevel >= talent.maxLevel) {
                alert(`${talent.name} 已达到最高等级！`);
            } else {
                alert("该天赋暂未解锁，请先满足前置条件！");
            }
        }

        function resetTalents() {
            if (confirm("确认重置所有天赋？\n\n这将返还所有已投入的天赋点\n需要消耗 1 传承星火")) {
                alert("✅ 天赋重置成功！\n\n• 已返还 35 天赋点\n• 消耗传承星火 ×1\n• 剩余传承星火：6");
                openTalentTreeDetails();
            }
        }

        function closeTalentTree() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        function viewAncestorDetails(generation) {
            if (generation === 2) {
                alert("👑 第二代家主：李天明\n\n生涯回顾：\n• 继承家业时年仅20岁\n• 将家族实力提升了300%\n• 开创了引气修炼法门\n• 最终败于第六时代的修仙者\n\n传承遗物：天明玉佩\n传承能力：引气效率 +50%");
            } else if (generation === 1) {
                alert("👑 第一代家主：李破天\n\n生涯回顾：\n• 白手起家创立李氏家族\n• 在乱世中建立武道传承\n• 制定了家族基本准则\n• 最终败于第三时代的血脉争夺\n\n传承遗物：破天剑\n传承能力：攻击力 +30%");
            }
        }

        function closeShrine() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // 药店系统
        function openPharmacy() {
            const pharmacyHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: #d4af37; margin-bottom: 15px;">🧪 药店 - 丹药炼制</h3>
                    
                    <div style="background: rgba(139, 69, 19, 0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">💊 恢复丹药</h4>
                        <div style="display: grid; grid-template-columns: 1fr; gap: 8px; font-size: 0.9em;">
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div>
                                    <span style="color: #e74c3c;">❤️ 回血丹</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">瞬间恢复50%生命值</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ffd700; font-weight: bold;">500 金币</div>
                                    <button onclick="buyPotion('heal')" style="background: #e74c3c; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">购买</button>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div>
                                    <span style="color: #3498db;">🔵 回气丹</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">瞬间恢复50%内力值</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ffd700; font-weight: bold;">500 金币</div>
                                    <button onclick="buyPotion('mana')" style="background: #3498db; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">购买</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 10px;">⚡ 增益丹药</h4>
                        <div style="display: grid; grid-template-columns: 1fr; gap: 8px; font-size: 0.9em;">
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div>
                                    <span style="color: #f39c12;">⚡ 狂暴丹</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">攻击力 +50%，持续30分钟</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ffd700; font-weight: bold;">2,000 金币</div>
                                    <button onclick="buyPotion('rage')" style="background: #f39c12; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">购买</button>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div>
                                    <span style="color: #9b59b6;">🛡️ 护体丹</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">防御力 +40%，持续30分钟</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #ffd700; font-weight: bold;">1,800 金币</div>
                                    <button onclick="buyPotion('defense')" style="background: #9b59b6; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">购买</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">🧬 炼制服务</h4>
                        <div style="color: #c9b037; font-size: 0.9em; margin-bottom: 10px;">
                            提供材料，委托炼制高级丹药
                        </div>
                        <button onclick="showCraftingService()" style="background: #d4af37; color: #2c1810; border: none; padding: 8px 16px; border-radius: 5px;">炼制委托</button>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closePharmacy()" style="background: #666; color: white; border: none; padding: 12px 24px; border-radius: 8px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('药店', pharmacyHtml);
        }

        function buyPotion(type) {
            const potions = {
                heal: { name: '回血丹', price: 500, effect: '恢复50%生命值' },
                mana: { name: '回气丹', price: 500, effect: '恢复50%内力值' },
                rage: { name: '狂暴丹', price: 2000, effect: '攻击力+50%，30分钟' },
                defense: { name: '护体丹', price: 1800, effect: '防御力+40%，30分钟' }
            };
            
            const potion = potions[type];
            if (potion) {
                if (confirm(`确认购买 ${potion.name}？\n效果：${potion.effect}\n价格：${potion.price.toLocaleString()} 金币`)) {
                    alert(`✅ 购买成功！\n\n获得物品：${potion.name}\n效果：${potion.effect}\n花费金币：${potion.price.toLocaleString()}`);
                }
            }
        }

        function showCraftingService() {
            alert("🧬 炼制委托\n\n高级丹药炼制：\n• 大还丹：全回复生命内力\n• 破境丹：临时提升境界\n• 洗髓丹：重置属性点\n\n需要：\n• 对应药材×5\n• 炼制费用\n• 成功率60-90%");
        }

        function closePharmacy() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // 仓库功能
        function switchStorageTab(type) {
            document.querySelectorAll('.storage-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // 过滤显示对应类型的物品
            document.querySelectorAll('.storage-item').forEach(item => {
                const itemType = item.dataset.type;
                if (itemType === type || item.classList.contains('empty')) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function expandStorage() {
            const expandHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: #d4af37; margin-bottom: 15px;">📦 仓库扩容</h3>
                    
                    <div style="background: rgba(139, 69, 19, 0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">🎒 当前容量</h4>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="color: #c9b037;">当前容量:</span>
                            <span style="color: #4ecdc4; font-weight: bold;">30 格</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="color: #c9b037;">已使用:</span>
                            <span style="color: #e74c3c; font-weight: bold;">26 格</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: #c9b037;">剩余空间:</span>
                            <span style="color: #f39c12; font-weight: bold;">4 格</span>
                        </div>
                    </div>
                    
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 10px;">🔄 扩容选项</h4>
                        <div style="display: grid; grid-template-columns: 1fr; gap: 8px; font-size: 0.9em;">
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div>
                                    <span style="color: #4ecdc4; font-weight: bold;">+10 格扩容</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">扩容至 40 格</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #e74c3c; font-weight: bold;">💎 50 钻石</div>
                                    <button onclick="confirmExpansion(10, 50)" style="background: #4ecdc4; color: #2c1810; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">扩容</button>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div>
                                    <span style="color: #8e44ad; font-weight: bold;">+20 格扩容</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">扩容至 50 格</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #e74c3c; font-weight: bold;">💎 90 钻石</div>
                                    <button onclick="confirmExpansion(20, 90)" style="background: #8e44ad; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">扩容</button>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div>
                                    <span style="color: #ff4500; font-weight: bold;">+50 格扩容</span><br/>
                                    <span style="color: #c9b037; font-size: 0.8em;">扩容至 80 格</span>
                                </div>
                                <div style="text-align: right;">
                                    <div style="color: #e74c3c; font-weight: bold;">💎 200 钻石</div>
                                    <button onclick="confirmExpansion(50, 200)" style="background: #ff4500; color: white; border: none; padding: 4px 8px; border-radius: 3px; font-size: 0.8em; margin-top: 2px;">扩容</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">💎 当前钻石</h4>
                        <div style="text-align: center; font-size: 1.2em; color: #e74c3c; font-weight: bold;">
                            💎 347 钻石
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeExpansion()" style="background: #666; color: white; border: none; padding: 12px 24px; border-radius: 8px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('仓库扩容', expandHtml);
        }

        function confirmExpansion(slots, cost) {
            if (confirm(`确认扩容仓库？\n\n扩容 +${slots} 格\n花费：${cost} 钻石`)) {
                alert(`✅ 扩容成功！\n\n仓库容量 +${slots} 格\n花费钻石：${cost}`);
                closeExpansion();
            }
        }

        function closeExpansion() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // 仓库物品详情查看
        function showStorageItemDetails(itemType) {
            const itemData = {
                sword2: {
                    name: '精钢长剑',
                    quality: '稀有',
                    type: '单手剑',
                    level: '+0',
                    stats: [
                        { name: '攻击力', value: '+280', color: '#e74c3c' },
                        { name: '暴击率', value: '+5%', color: '#f39c12' }
                    ],
                    affixes: [],
                    canEquip: true,
                    canSell: true,
                    value: 15000,
                    count: 1
                }
            };

            const item = itemData[itemType];
            if (!item) {
                alert("该物品暂无详细信息");
                return;
            }

            const qualityColors = {
                '传说': '#ff4500',
                '史诗': '#8a2be2',
                '稀有': '#4169e1',
                '罕见': '#32cd32',
                '普通': '#dcdcdc'
            };

            const itemHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: ${qualityColors[item.quality] || '#d4af37'}; margin-bottom: 15px;">
                        ${item.name} ${item.level} ${item.count > 1 ? `×${item.count}` : ''}
                    </h3>
                    
                    <div style="background: rgba(139, 69, 19, 0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <span style="color: #d4af37; font-weight: bold;">${item.type}</span>
                            <span style="color: ${qualityColors[item.quality]}; font-weight: bold;">${item.quality}</span>
                        </div>
                        
                        <h4 style="color: #4ecdc4; margin-bottom: 8px;">📊 属性</h4>
                        ${item.stats.map(stat => `
                            <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                                <span style="color: #c9b037;">${stat.name}:</span>
                                <span style="color: ${stat.color}; font-weight: bold;">${stat.value}</span>
                            </div>
                        `).join('')}
                    </div>
                    
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 10px;">🎒 操作选项</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            ${item.canEquip ? `
                                <button onclick="equipItemFromStorage('${itemType}')" style="background: #4ecdc4; color: #2c1810; border: none; padding: 8px 12px; border-radius: 5px; font-weight: bold;">装备</button>
                            ` : ''}
                            ${item.canSell ? `
                                <button onclick="sellItem('${itemType}')" style="background: #f39c12; color: #2c1810; border: none; padding: 8px 12px; border-radius: 5px; font-weight: bold;">出售</button>
                            ` : ''}
                            <button onclick="decomposeItem('${itemType}')" style="background: #8e44ad; color: white; border: none; padding: 8px 12px; border-radius: 5px; font-weight: bold;">分解</button>
                            <button onclick="dropItem('${itemType}')" style="background: #e74c3c; color: white; border: none; padding: 8px 12px; border-radius: 5px; font-weight: bold;">丢弃</button>
                        </div>
                    </div>
                    
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">💰 价值信息</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9em;">
                            <div>
                                <span style="color: #c9b037;">出售价格:</span>
                                <span style="color: #ffd700; font-weight: bold;">${item.value.toLocaleString()} 金币</span>
                            </div>
                            <div>
                                <span style="color: #c9b037;">分解获得:</span>
                                <span style="color: #8e44ad; font-weight: bold;">${Math.floor(item.value/100)} 积分</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeItemDetails()" style="background: #666; color: white; border: none; padding: 12px 24px; border-radius: 8px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('物品详情', itemHtml);
        }

        function equipItemFromStorage(itemType) {
            alert("✅ 装备成功！\n\n物品已装备，原装备已放入背包");
            closeItemDetails();
        }

        function sellItem(itemType) {
            if (confirm("确认出售此物品？\n出售后无法恢复")) {
                alert("✅ 出售成功！\n\n获得金币：15,000");
                closeItemDetails();
            }
        }

        function decomposeItem(itemType) {
            if (confirm("确认分解此物品？\n分解后无法恢复")) {
                alert("✅ 分解成功！\n\n获得积分：150");
                closeItemDetails();
            }
        }

        function dropItem(itemType) {
            if (confirm("确认丢弃此物品？\n丢弃后无法恢复")) {
                alert("✅ 物品已丢弃");
                closeItemDetails();
            }
        }

        function closeItemDetails() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // 角色功能
        function showProfessionDetails() {
            alert("🔨 副职业详情\n\n铁匠等级 67/100\n经验值: 67,425/100,000\n\n当前能力:\n• 装备强化成功率 +13%\n• 制作装备品质加成 +8%");
        }

        // 角色属性详情查看
        function showCharacterDetails() {
            const characterHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: #d4af37; margin-bottom: 15px;">👤 角色详细信息</h3>
                    
                    <div style="background: rgba(139, 69, 19, 0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">⚔️ 战斗力计算</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9em;">
                            <div>
                                <span style="color: #c9b037;">基础攻击:</span>
                                <span style="color: #4ecdc4; font-weight: bold;">1,456</span>
                            </div>
                            <div>
                                <span style="color: #c9b037;">装备加成:</span>
                                <span style="color: #4ecdc4; font-weight: bold;">+1,000</span>
                            </div>
                            <div>
                                <span style="color: #c9b037;">基础防御:</span>
                                <span style="color: #4ecdc4; font-weight: bold;">1,043</span>
                            </div>
                            <div>
                                <span style="color: #c9b037;">装备加成:</span>
                                <span style="color: #4ecdc4; font-weight: bold;">+800</span>
                            </div>
                            <div>
                                <span style="color: #c9b037;">基础生命:</span>
                                <span style="color: #4ecdc4; font-weight: bold;">6,540</span>
                            </div>
                            <div>
                                <span style="color: #c9b037;">装备加成:</span>
                                <span style="color: #4ecdc4; font-weight: bold;">+2,000</span>
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 10px; padding-top: 10px; border-top: 1px solid #8b4513;">
                            <div style="color: #ffd700; font-weight: bold; font-size: 1.1em;">总战斗力: 47,856</div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 10px;">📊 三维属性分解</h4>
                        <div style="display: grid; grid-template-columns: 1fr; gap: 8px; font-size: 0.9em;">
                            <div style="background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="display: flex; justify-content: space-between;">
                                    <span style="color: #e74c3c; font-weight: bold;">💪 力量 156</span>
                                    <span style="color: #c9b037;">(基础98 + 装备58)</span>
                                </div>
                                <div style="color: #c9b037; font-size: 0.8em;">影响攻击力、暴击伤害</div>
                            </div>
                            <div style="background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="display: flex; justify-content: space-between;">
                                    <span style="color: #f39c12; font-weight: bold;">⚡ 敏捷 134</span>
                                    <span style="color: #c9b037;">(基础89 + 装备45)</span>
                                </div>
                                <div style="color: #c9b037; font-size: 0.8em;">影响暴击率、闪避率、攻击速度</div>
                            </div>
                            <div style="background: rgba(0, 0, 0, 0.2); padding: 8px; border-radius: 5px;">
                                <div style="display: flex; justify-content: space-between;">
                                    <span style="color: #27ae60; font-weight: bold;">🛡️ 体质 142</span>
                                    <span style="color: #c9b037;">(基础95 + 装备47)</span>
                                </div>
                                <div style="color: #c9b037; font-size: 0.8em;">影响生命值、防御力</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">📈 升级预览</h4>
                        <div style="color: #c9b037; font-size: 0.9em; margin-bottom: 10px;">
                            升级到 Lv.48 需要经验: 858,433
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9em;">
                            <div>
                                <span style="color: #c9b037;">攻击力:</span>
                                <span style="color: #4ecdc4;">2,456 → 2,512</span>
                            </div>
                            <div>
                                <span style="color: #c9b037;">防御力:</span>
                                <span style="color: #4ecdc4;">1,843 → 1,885</span>
                            </div>
                            <div>
                                <span style="color: #c9b037;">生命值:</span>
                                <span style="color: #4ecdc4;">8,540 → 8,720</span>
                            </div>
                            <div>
                                <span style="color: #c9b037;">可分配点:</span>
                                <span style="color: #4ecdc4;">+5</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeCharacterDetails()" style="background: #666; color: white; border: none; padding: 12px 24px; border-radius: 8px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('角色详情', characterHtml);
        }

        function closeCharacterDetails() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // 装备详情查看
        function showEquipmentDetails(slotType) {
            const equipmentData = {
                weapon: {
                    name: '烈阳神剑',
                    quality: '传说',
                    level: '+6',
                    type: '单手剑',
                    stats: [
                        { name: '攻击力', value: '+458', color: '#e74c3c' },
                        { name: '暴击率', value: '+12%', color: '#f39c12' },
                        { name: '暴击伤害', value: '+35%', color: '#f39c12' }
                    ],
                    affixes: [
                        { name: '烈阳之怒', desc: '攻击时15%概率触发烈阳斩击', color: '#ff4500' },
                        { name: '血腥', desc: '击败敌人时恢复10%生命值', color: '#e74c3c' }
                    ],
                    setInfo: '烈阳套装 (2/5)',
                    setBonus: '2件套：攻击力 +25%',
                    canUpgrade: true,
                    upgradeInfo: '强化至 +7 需要：高级强化石×3, 金币×50,000',
                    value: 250000
                },
                armor: {
                    name: '龙鳞战甲',
                    quality: '史诗',
                    level: '+4',
                    type: '重甲',
                    stats: [
                        { name: '防御力', value: '+324', color: '#3498db' },
                        { name: '生命值', value: '+1,250', color: '#27ae60' },
                        { name: '格挡率', value: '+8%', color: '#9b59b6' }
                    ],
                    affixes: [
                        { name: '龙鳞护体', desc: '受到攻击时8%概率触发护盾', color: '#8a2be2' }
                    ],
                    setInfo: '无套装',
                    setBonus: '',
                    canUpgrade: true,
                    upgradeInfo: '强化至 +5 需要：中级强化石×5, 金币×25,000',
                    value: 120000
                },
                helmet: {
                    name: '凤翎冠',
                    quality: '稀有',
                    level: '+2',
                    type: '法帽',
                    stats: [
                        { name: '防御力', value: '+156', color: '#3498db' },
                        { name: '敏捷', value: '+18', color: '#f39c12' }
                    ],
                    affixes: [
                        { name: '疾风', desc: '移动速度 +15%', color: '#4ecdc4' }
                    ],
                    setInfo: '无套装',
                    setBonus: '',
                    canUpgrade: true,
                    upgradeInfo: '强化至 +3 需要：低级强化石×8, 金币×8,000',
                    value: 45000
                },
                boots: {
                    name: '疾风靴',
                    quality: '罕见',
                    level: '+1',
                    type: '轻甲靴',
                    stats: [
                        { name: '防御力', value: '+89', color: '#3498db' },
                        { name: '敏捷', value: '+12', color: '#f39c12' }
                    ],
                    affixes: [],
                    setInfo: '无套装',
                    setBonus: '',
                    canUpgrade: true,
                    upgradeInfo: '强化至 +2 需要：低级强化石×4, 金币×3,000',
                    value: 15000
                },
                accessory1: {
                    name: '力量之戒',
                    quality: '普通',
                    level: '+0',
                    type: '戒指',
                    stats: [
                        { name: '力量', value: '+15', color: '#e74c3c' }
                    ],
                    affixes: [],
                    setInfo: '无套装',
                    setBonus: '',
                    canUpgrade: true,
                    upgradeInfo: '强化至 +1 需要：低级强化石×2, 金币×1,000',
                    value: 5000
                },
                accessory2: {
                    name: '空槽位',
                    quality: '',
                    level: '',
                    type: '未装备',
                    stats: [],
                    affixes: [],
                    setInfo: '',
                    setBonus: '',
                    canUpgrade: false,
                    upgradeInfo: '',
                    value: 0
                }
            };

            const equipment = equipmentData[slotType];
            
            if (equipment.name === '空槽位') {
                alert("该槽位暂未装备物品");
                return;
            }

            const qualityColors = {
                '传说': '#ff4500',
                '史诗': '#8a2be2',
                '稀有': '#4169e1',
                '罕见': '#32cd32',
                '普通': '#dcdcdc'
            };

            const equipmentHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: ${qualityColors[equipment.quality] || '#d4af37'}; margin-bottom: 15px;">
                        ${equipment.name} ${equipment.level}
                    </h3>
                    
                    <div style="background: rgba(139, 69, 19, 0.3); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                            <span style="color: #d4af37; font-weight: bold;">${equipment.type}</span>
                            <span style="color: ${qualityColors[equipment.quality]}; font-weight: bold;">${equipment.quality}</span>
                        </div>
                        
                        <h4 style="color: #4ecdc4; margin-bottom: 8px;">📊 基础属性</h4>
                        ${equipment.stats.map(stat => `
                            <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
                                <span style="color: #c9b037;">${stat.name}:</span>
                                <span style="color: ${stat.color}; font-weight: bold;">${stat.value}</span>
                            </div>
                        `).join('')}
                        
                        ${equipment.affixes.length > 0 ? `
                            <h4 style="color: #d4af37; margin: 15px 0 8px 0;">✨ 特殊词条</h4>
                            ${equipment.affixes.map(affix => `
                                <div style="background: rgba(0, 0, 0, 0.2); padding: 5px 8px; border-radius: 3px; margin-bottom: 5px;">
                                    <div style="color: ${affix.color}; font-weight: bold;">${affix.name}</div>
                                    <div style="color: #c9b037; font-size: 0.9em;">${affix.desc}</div>
                                </div>
                            `).join('')}
                        ` : ''}
                        
                        ${equipment.setInfo ? `
                            <h4 style="color: #8a2be2; margin: 15px 0 8px 0;">🔗 套装信息</h4>
                            <div style="color: #8a2be2; font-weight: bold;">${equipment.setInfo}</div>
                            ${equipment.setBonus ? `<div style="color: #c9b037; font-size: 0.9em;">${equipment.setBonus}</div>` : ''}
                        ` : ''}
                    </div>
                    
                    <div style="background: rgba(78, 205, 196, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 10px;">🔧 强化信息</h4>
                        ${equipment.canUpgrade ? `
                            <div style="color: #c9b037; font-size: 0.9em; margin-bottom: 10px;">
                                ${equipment.upgradeInfo}
                            </div>
                            <button onclick="upgradeEquipment('${slotType}')" style="background: #4ecdc4; color: #2c1810; border: none; padding: 8px 16px; border-radius: 5px; margin-right: 10px;">强化装备</button>
                        ` : `
                            <div style="color: #c9b037; font-size: 0.9em;">该装备无法强化</div>
                        `}
                    </div>
                    
                    <div style="background: rgba(212, 175, 55, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                        <h4 style="color: #d4af37; margin-bottom: 10px;">💰 价值信息</h4>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="color: #c9b037;">估值：<span style="color: #ffd700; font-weight: bold;">${equipment.value.toLocaleString()} 金币</span></div>
                                <div style="color: #c9b037; font-size: 0.9em;">分解可获得积分</div>
                            </div>
                            <div>
                                <button onclick="unequipItem('${slotType}')" style="background: #e74c3c; color: white; border: none; padding: 6px 12px; border-radius: 3px; font-size: 0.9em;">卸下装备</button>
                            </div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="closeEquipmentDetails()" style="background: #666; color: white; border: none; padding: 12px 24px; border-radius: 8px;">关闭</button>
                    </div>
                </div>
            `;
            
            showModal('装备详情', equipmentHtml);
        }

        function upgradeEquipment(slotType) {
            alert("⚒️ 装备强化\n\n将前往铁匠铺进行装备强化\n(需要消耗强化石和金币)");
        }

        function unequipItem(slotType) {
            if (confirm("确认卸下此装备？\n卸下后装备将放入背包")) {
                alert("✅ 装备已卸下\n已放入背包中");
                // 这里可以添加实际的卸下装备逻辑
            }
        }

        function closeEquipmentDetails() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // 战斗功能
        function attackEnemy() {
            const baseDamage = Math.floor(Math.random() * 3000) + 2000; // 2000-5000伤害
            const isCritical = Math.random() < 0.15; // 15%暴击率
            const finalDamage = isCritical ? Math.floor(baseDamage * 2) : baseDamage;
            
            gameState.currentEnemy.currentHp = Math.max(0, gameState.currentEnemy.currentHp - finalDamage);
            
            // 显示伤害飘字效果
            showDamageFloat(finalDamage, isCritical);
            
            updateEnemyDisplay();
            
            // 检查技能触发
            checkAndTriggerSkills();
            
            if (gameState.currentEnemy.currentHp <= 0) {
                setTimeout(() => {
                    showVictory();
                    resetEnemy();
                }, 500);
            }
        }

        // 显示伤害飘字
        function showDamageFloat(damage, isCritical) {
            const damageDiv = document.createElement('div');
            damageDiv.style.cssText = `
                position: absolute; 
                top: 60%; 
                left: 50%; 
                transform: translate(-50%, -50%);
                color: ${isCritical ? '#ff6b6b' : '#ffd700'}; 
                font-size: ${isCritical ? '2.5em' : '2em'}; 
                font-weight: bold;
                z-index: 1000; 
                pointer-events: none;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
                animation: damageFloat 1.5s ease-out forwards;
            `;
            damageDiv.textContent = isCritical ? `${damage} CRIT!` : damage;
            
            const battleContainer = document.querySelector('.adventure-content .current-area');
            if (battleContainer) {
                battleContainer.appendChild(damageDiv);
                
                setTimeout(() => {
                    if (damageDiv.parentNode) {
                        damageDiv.parentNode.removeChild(damageDiv);
                    }
                }, 1500);
            }
        }

        // 检查并触发技能
        function checkAndTriggerSkills() {
            const equippedSlots = document.querySelectorAll('.skill-slot.equipped');
            
            equippedSlots.forEach(slot => {
                const skillName = slot.querySelector('.skill-name').textContent;
                const skill = skillState.allSkills.find(s => s.name === skillName);
                
                if (skill) {
                    const triggerChance = calculateSkillProbability(skill) / 100;
                    if (Math.random() < triggerChance) {
                        setTimeout(() => {
                            triggerSkillEffect(skill);
                        }, 300);
                    }
                }
            });
        }

        // 触发技能效果
        function triggerSkillEffect(skill) {
            let skillDamage = 0;
            let effectText = '';
            
            switch (skill.type) {
                case 'martial':
                    skillDamage = Math.floor(Math.random() * 2000) + 1500;
                    effectText = `触发 ${skill.icon} ${skill.name}！造成 ${skillDamage} 点额外伤害`;
                    break;
                case 'qi':
                    skillDamage = Math.floor(Math.random() * 2500) + 2000;
                    effectText = `触发 ${skill.icon} ${skill.name}！造成 ${skillDamage} 点元素伤害`;
                    break;
                case 'immortal':
                    skillDamage = Math.floor(Math.random() * 4000) + 3000;
                    effectText = `触发 ${skill.icon} ${skill.name}！造成 ${skillDamage} 点仙法伤害`;
                    break;
            }
            
            if (skillDamage > 0) {
                gameState.currentEnemy.currentHp = Math.max(0, gameState.currentEnemy.currentHp - skillDamage);
                showSkillEffect(skill, skillDamage);
                updateEnemyDisplay();
                
                if (gameState.currentEnemy.currentHp <= 0) {
                    setTimeout(() => {
                        showVictory();
                        resetEnemy();
                    }, 500);
                }
            }
        }

        // 显示技能效果
        function showSkillEffect(skill, damage) {
            const effectDiv = document.createElement('div');
            effectDiv.style.cssText = `
                position: absolute; 
                top: 40%; 
                left: 50%; 
                transform: translate(-50%, -50%);
                color: #9b59b6; 
                font-size: 1.5em; 
                font-weight: bold;
                z-index: 1001; 
                pointer-events: none;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
                animation: skillFloat 2s ease-out forwards;
            `;
            effectDiv.innerHTML = `${skill.icon} ${skill.name}<br/>${damage}`;
            
            const battleContainer = document.querySelector('.adventure-content .current-area');
            if (battleContainer) {
                battleContainer.appendChild(effectDiv);
                
                setTimeout(() => {
                    if (effectDiv.parentNode) {
                        effectDiv.parentNode.removeChild(effectDiv);
                    }
                }, 2000);
            }
        }

        function toggleAuto() {
            gameState.autoMode = !gameState.autoMode;
            const btn = event.target.closest('.battle-btn');
            
            if (gameState.autoMode) {
                btn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">自动战斗</span>';
            } else {
                btn.innerHTML = '<span class="btn-icon">⏸️</span><span class="btn-text">手动模式</span>';
            }
        }

        function updateEnemyDisplay() {
            const healthPercent = (gameState.currentEnemy.currentHp / gameState.currentEnemy.maxHp) * 100;
            document.querySelector('.health-fill').style.width = healthPercent + '%';
            document.querySelector('.health-text').textContent = 
                gameState.currentEnemy.currentHp.toLocaleString() + '/' + gameState.currentEnemy.maxHp.toLocaleString();
        }

        function showVictory() {
            const victoryDiv = document.createElement('div');
            victoryDiv.style.cssText = `
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.9); color: #ffd700; padding: 20px 30px;
                border-radius: 15px; font-size: 1.2em; font-weight: bold;
                z-index: 1000; border: 2px solid #d4af37; text-align: center;
            `;
            victoryDiv.innerHTML = `
                🎉 胜利！<br/>
                <div style="font-size: 0.8em; margin-top: 10px; color: #4ecdc4;">
                    获得经验 +2500<br/>
                    获得金币 +1250
                </div>
            `;
            document.body.appendChild(victoryDiv);
            
            setTimeout(() => {
                document.body.removeChild(victoryDiv);
            }, 2000);
        }

        function resetEnemy() {
            gameState.currentEnemy.currentHp = gameState.currentEnemy.maxHp;
            updateEnemyDisplay();
        }

        // 技能管理状态
        let skillState = {
            selectedSlot: -1,
            equippedSkills: [
                { id: 'sword', name: '烈阳剑法', icon: '⚔️', level: 15, type: 'martial', baseProbability: 4.5 },
                { id: 'defense', name: '龙鳞护体', icon: '🛡️', level: 12, type: 'martial', baseProbability: 3.8 },
                { id: 'speed', name: '疾风步', icon: '⚡', level: 8, type: 'martial', baseProbability: 4.2 },
                { id: 'fire', name: '烈火掌', icon: '🔥', level: 6, type: 'qi', baseProbability: 2.5 },
                { id: 'ice', name: '寒冰诀', icon: '❄️', level: 4, type: 'qi', baseProbability: 2.8 }
            ],
            allSkills: [
                // 武道技能
                { id: 'sword', name: '烈阳剑法', icon: '⚔️', level: 15, type: 'martial', baseProbability: 4.5 },
                { id: 'defense', name: '龙鳞护体', icon: '🛡️', level: 12, type: 'martial', baseProbability: 3.8 },
                { id: 'speed', name: '疾风步', icon: '⚡', level: 8, type: 'martial', baseProbability: 4.2 },
                { id: 'heavy_strike', name: '重击', icon: '💪', level: 5, type: 'martial', baseProbability: 3.5 },
                { id: 'counter', name: '反击', icon: '🔄', level: 3, type: 'martial', baseProbability: 4.0 },
                // 引气技能
                { id: 'fire', name: '烈火掌', icon: '🔥', level: 6, type: 'qi', baseProbability: 2.5 },
                { id: 'ice', name: '寒冰诀', icon: '❄️', level: 4, type: 'qi', baseProbability: 2.8 },
                { id: 'heal', name: '回春术', icon: '💚', level: 7, type: 'qi', baseProbability: 2.2 },
                // 仙道技能
                { id: 'thunder', name: '九天雷法', icon: '⚡', level: 2, type: 'immortal', baseProbability: 1.5 }
            ],
            currentCategory: 'martial',
            // 概率加成设置
            equipmentBonus: 12.5, // 装备加成
            setBonus: 8.0,       // 套装加成
            totalProbability: 0   // 总触发概率
        };

        // 技能概率计算函数
        function calculateSkillProbability(skill) {
            // 基础概率 + 等级加成(每级0.5%) + 装备加成 + 套装加成
            const levelBonus = skill.level * 0.5;
            const finalProbability = skill.baseProbability + levelBonus + skillState.equipmentBonus + skillState.setBonus;
            return Math.min(finalProbability, 95); // 最高不超过95%
        }

        // 计算总触发概率
        function calculateTotalProbability() {
            let total = 0;
            const equippedSlots = document.querySelectorAll('.skill-slot.equipped');
            
            equippedSlots.forEach(slot => {
                const skillName = slot.querySelector('.skill-name').textContent;
                const skill = skillState.allSkills.find(s => s.name === skillName);
                if (skill) {
                    total += calculateSkillProbability(skill);
                }
            });
            
            skillState.totalProbability = Math.min(total, 100); // 最高不超过100%
            return skillState.totalProbability;
        }

        // 更新概率显示
        function updateProbabilityDisplay() {
            // 更新总概率显示
            const totalProb = calculateTotalProbability();
            document.getElementById('total-probability').textContent = totalProb.toFixed(1) + '%';
            
            // 更新每个技能槽位的概率
            const equippedSlots = document.querySelectorAll('.skill-slot.equipped');
            equippedSlots.forEach(slot => {
                const skillName = slot.querySelector('.skill-name').textContent;
                const skill = skillState.allSkills.find(s => s.name === skillName);
                if (skill) {
                    const probability = calculateSkillProbability(skill);
                    const probabilityElement = slot.querySelector('.skill-probability');
                    if (probabilityElement) {
                        probabilityElement.textContent = probability.toFixed(1) + '%';
                    }
                }
            });
            
            // 更新等级加成显示
            let totalLevelBonus = 0;
            equippedSlots.forEach(slot => {
                const skillName = slot.querySelector('.skill-name').textContent;
                const skill = skillState.allSkills.find(s => s.name === skillName);
                if (skill) {
                    totalLevelBonus += skill.level * 0.5;
                }
            });
            document.getElementById('level-bonus').textContent = '+' + totalLevelBonus.toFixed(1) + '%';
        }

        // 切换概率详情显示
        function toggleProbabilityDetails() {
            const details = document.getElementById('probability-details');
            const toggleBtn = document.querySelector('.probability-toggle');
            
            if (details.style.display === 'none') {
                details.style.display = 'block';
                toggleBtn.textContent = '详情 ▲';
            } else {
                details.style.display = 'none';
                toggleBtn.textContent = '详情 ▼';
            }
        }

        // 技能功能
        function switchSkillCategory(category) {
            document.querySelectorAll('.skill-category').forEach(cat => cat.classList.remove('active'));
            event.target.classList.add('active');
            
            skillState.currentCategory = category;
            
            // 显示/隐藏对应分类的技能
            document.querySelectorAll('.skill-card').forEach(card => {
                if (card.classList.contains(category)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // 选择技能槽位
        function selectSkillSlot(slotIndex) {
            // 移除所有槽位的选中状态
            document.querySelectorAll('.skill-slot').forEach(slot => {
                slot.classList.remove('selected');
            });
            
            // 选中当前槽位
            const currentSlot = document.querySelector(`[data-slot="${slotIndex}"]`);
            currentSlot.classList.add('selected');
            skillState.selectedSlot = slotIndex;
            
            // 如果槽位有技能，显示详细信息或卸载选项
            if (currentSlot.classList.contains('equipped')) {
                const skillName = currentSlot.querySelector('.skill-name').textContent;
                const skill = skillState.allSkills.find(s => s.name === skillName);
                
                if (skill) {
                    showSkillDetails(skill);
                } else {
                    // 备选：显示卸载选项
                    if (confirm(`是否要卸载技能"${skillName}"？`)) {
                        unequipSkillFromSlot(slotIndex);
                    }
                }
            }
        }

        // 显示技能详细信息
        function showSkillDetails(skill) {
            const probability = calculateSkillProbability(skill);
            const baseProbability = skill.baseProbability;
            const levelBonus = skill.level * 0.5;
            const equipmentBonus = skillState.equipmentBonus;
            const setBonus = skillState.setBonus;
            
            const detailsHtml = `
                <div style="text-align: left; line-height: 1.5;">
                    <h3 style="color: #d4af37; margin-bottom: 10px;">${skill.icon} ${skill.name}</h3>
                    <div style="margin-bottom: 15px;">
                        <strong>等级：</strong>Lv.${skill.level}<br/>
                        <strong>类型：</strong>${skill.type === 'martial' ? '武道技能' : skill.type === 'qi' ? '引气技能' : '仙道技能'}
                    </div>
                    <div style="background: rgba(78, 205, 196, 0.2); padding: 10px; border-radius: 8px; margin-bottom: 15px;">
                        <h4 style="color: #4ecdc4; margin-bottom: 8px;">🎲 释放概率详解</h4>
                        <div style="font-size: 0.9em;">
                            基础概率: ${baseProbability.toFixed(1)}%<br/>
                            等级加成: +${levelBonus.toFixed(1)}% (Lv.${skill.level} × 0.5%)<br/>
                            装备加成: +${equipmentBonus.toFixed(1)}%<br/>
                            套装加成: +${setBonus.toFixed(1)}%<br/>
                            <hr style="margin: 8px 0; border-color: #4ecdc4;">
                            <strong style="color: #4ecdc4;">最终概率: ${probability.toFixed(1)}%</strong>
                        </div>
                    </div>
                    <div style="text-align: center;">
                        <button onclick="closeSkillDetails()" style="background: #d4af37; color: #2c1810; border: none; padding: 8px 20px; border-radius: 5px; margin-right: 10px;">关闭</button>
                        <button onclick="unequipSkillByName('${skill.name}')" style="background: #e74c3c; color: white; border: none; padding: 8px 20px; border-radius: 5px;">卸载技能</button>
                    </div>
                </div>
            `;
            
            showModal('技能详情', detailsHtml);
        }

        // 显示模态框
        function showModal(title, content) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0, 0, 0, 0.8); z-index: 2000;
                display: flex; align-items: center; justify-content: center;
                padding: 20px; box-sizing: border-box;
            `;
            
            modal.innerHTML = `
                <div style="
                    background: linear-gradient(135deg, #2c1810, #4a2c1a);
                    border: 2px solid #d4af37; border-radius: 15px;
                    padding: 20px; max-width: 90%; max-height: 80%;
                    overflow-y: auto; color: #e6d3a3;
                ">
                    <h2 style="color: #d4af37; text-align: center; margin-bottom: 15px;">${title}</h2>
                    ${content}
                </div>
            `;
            
            modal.id = 'skill-details-modal';
            document.body.appendChild(modal);
            
            // 点击背景关闭
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeSkillDetails();
                }
            });
        }

        // 关闭技能详情
        function closeSkillDetails() {
            const modal = document.getElementById('skill-details-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        // 通过技能名称卸载技能
        function unequipSkillByName(skillName) {
            closeSkillDetails();
            
            // 找到对应的技能槽
            const slots = document.querySelectorAll('.skill-slot.equipped');
            for (let slot of slots) {
                const slotSkillName = slot.querySelector('.skill-name').textContent;
                if (slotSkillName === skillName) {
                    const slotIndex = parseInt(slot.dataset.slot);
                    unequipSkillFromSlot(slotIndex);
                    break;
                }
            }
        }

        // 装备/卸载技能
        function toggleSkillEquip(skillId) {
            const skillCard = document.querySelector(`[data-skill="${skillId}"]`);
            
            if (skillCard.classList.contains('equipped')) {
                // 卸载技能
                unequipSkill(skillId);
            } else {
                // 装备技能
                equipSkill(skillId);
            }
        }

        // 装备技能
        function equipSkill(skillId) {
            // 检查是否有空槽位
            const emptySlot = document.querySelector('.skill-slot.empty');
            if (!emptySlot) {
                alert('技能槽位已满，请先卸载其他技能');
                return;
            }
            
            // 获取技能信息
            const skillCard = document.querySelector(`[data-skill="${skillId}"]`);
            const skillIcon = skillCard.querySelector('.skill-icon').textContent;
            const skillName = skillCard.querySelector('.skill-name').textContent;
            const skillLevel = skillCard.querySelector('.skill-level').textContent;
            
            // 获取技能数据计算概率
            const skill = skillState.allSkills.find(s => s.name === skillName);
            const probability = skill ? calculateSkillProbability(skill) : 0;
            
            // 更新技能槽
            const slotIndex = parseInt(emptySlot.dataset.slot);
            emptySlot.classList.remove('empty');
            emptySlot.classList.add('equipped');
            emptySlot.innerHTML = `
                <div class="skill-icon">${skillIcon}</div>
                <div class="skill-name">${skillName}</div>
                <div class="skill-level">${skillLevel}</div>
                <div class="skill-probability">${probability.toFixed(1)}%</div>
                <div class="skill-equipped-mark">✓</div>
            `;
            
            // 更新技能卡片状态
            skillCard.classList.add('equipped');
            if (!skillCard.querySelector('.skill-equipped-badge')) {
                const badge = document.createElement('div');
                badge.className = 'skill-equipped-badge';
                badge.textContent = '已装备';
                skillCard.appendChild(badge);
            }
            
            // 更新概率显示
            updateProbabilityDisplay();
            
            // 更新上阵数量
            updateEquippedCount();
        }

        // 卸载技能
        function unequipSkill(skillId) {
            // 找到对应的技能槽
            const slots = document.querySelectorAll('.skill-slot.equipped');
            for (let slot of slots) {
                const slotSkillName = slot.querySelector('.skill-name').textContent;
                const skillCard = document.querySelector(`[data-skill="${skillId}"]`);
                const cardSkillName = skillCard.querySelector('.skill-name').textContent;
                
                if (slotSkillName === cardSkillName) {
                    // 清空技能槽
                    slot.classList.remove('equipped');
                    slot.classList.add('empty');
                    slot.innerHTML = `
                        <div class="skill-icon">+</div>
                        <div class="skill-name">空槽位</div>
                    `;
                    break;
                }
            }
            
            // 更新技能卡片状态
            const skillCard = document.querySelector(`[data-skill="${skillId}"]`);
            skillCard.classList.remove('equipped');
            const badge = skillCard.querySelector('.skill-equipped-badge');
            if (badge) {
                badge.remove();
            }
            
            // 更新概率显示
            updateProbabilityDisplay();
            
            // 更新上阵数量
            updateEquippedCount();
        }

        // 从槽位卸载技能
        function unequipSkillFromSlot(slotIndex) {
            const slot = document.querySelector(`[data-slot="${slotIndex}"]`);
            if (!slot.classList.contains('equipped')) return;
            
            const skillName = slot.querySelector('.skill-name').textContent;
            
            // 找到对应的技能卡片
            const allCards = document.querySelectorAll('.skill-card');
            for (let card of allCards) {
                const cardName = card.querySelector('.skill-name').textContent;
                if (cardName === skillName) {
                    card.classList.remove('equipped');
                    const badge = card.querySelector('.skill-equipped-badge');
                    if (badge) {
                        badge.remove();
                    }
                    break;
                }
            }
            
            // 清空槽位
            slot.classList.remove('equipped', 'selected');
            slot.classList.add('empty');
            slot.innerHTML = `
                <div class="skill-icon">+</div>
                <div class="skill-name">空槽位</div>
            `;
            
            // 更新概率显示
            updateProbabilityDisplay();
            
            // 更新上阵数量
            updateEquippedCount();
        }

        // 更新上阵技能数量显示
        function updateEquippedCount() {
            const equippedSlots = document.querySelectorAll('.skill-slot.equipped').length;
            const totalSlots = document.querySelectorAll('.skill-slot').length;
            const totalProb = calculateTotalProbability();
            
            // 更新标题，保持总概率显示
            const titleElement = document.querySelector('.equipped-title');
            titleElement.innerHTML = `⚔️ 上阵技能 (${equippedSlots}/${totalSlots}) - 总概率: <span id="total-probability">${totalProb.toFixed(1)}%</span>`;
        }

        // 技能预设功能
        function loadPreset(presetId) {
            // 移除其他预设的active状态
            document.querySelectorAll('.preset-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 设置当前预设为active
            event.target.classList.add('active');
            
            alert(`加载技能预设 ${presetId}`);
        }

        function savePreset() {
            alert('保存当前技能配置为预设');
        }

        function upgradeSkill(skillType) {
            alert("⚡ 技能升级\n\n消耗技能点数升级技能");
        }

        // 自动战斗
        setInterval(() => {
            if (gameState.autoMode && document.getElementById('adventure-content').classList.contains('active')) {
                if (Math.random() < 0.3) { // 30%概率攻击
                    attackEnemy();
                }
            }
        }, 1500);

        // 触摸反馈
        document.addEventListener('touchstart', function(e) {
            if (e.target.classList.contains('nav-item') || e.target.classList.contains('btn') || 
                e.target.classList.contains('city-building') || e.target.classList.contains('storage-item')) {
                e.target.style.transform = 'scale(0.95)';
            }
        });

        document.addEventListener('touchend', function(e) {
            if (e.target.classList.contains('nav-item') || e.target.classList.contains('btn') || 
                e.target.classList.contains('city-building') || e.target.classList.contains('storage-item')) {
                setTimeout(() => {
                    e.target.style.transform = '';
                }, 150);
            }
        });

        // 阻止下拉刷新
        document.addEventListener('touchmove', function(e) {
            e.preventDefault();
        }, { passive: false });

        // 页面加载完成后初始化概率显示
        document.addEventListener('DOMContentLoaded', function() {
            updateProbabilityDisplay();
        });

        // ===== 冒险系统函数 =====
        
        // 显示地图选择界面
        function showMapSelection(mode) {
            const modeSelection = document.getElementById('adventure-mode-selection');
            const mapSelection = document.getElementById('adventure-map-selection');
            
            modeSelection.style.display = 'none';
            mapSelection.style.display = 'block';
            
            // 根据模式更新地图界面
            updateMapContent(mode);
        }
        
        // 返回模式选择
        function backToModeSelection() {
            const modeSelection = document.getElementById('adventure-mode-selection');
            const mapSelection = document.getElementById('adventure-map-selection');
            const battleScreen = document.getElementById('adventure-battle');
            
            modeSelection.style.display = 'block';
            mapSelection.style.display = 'none';
            battleScreen.style.display = 'none';
        }
        
        // 选择关卡
        function selectStage(stageId, stageName) {
            // 更新关卡信息显示
            const infoTitle = document.querySelector('.stage-title');
            const description = document.querySelector('.stage-description');
            
            if (infoTitle) {
                infoTitle.textContent = stageName;
            }
            
            // 高亮选中的关卡
            document.querySelectorAll('.stage-node').forEach(node => {
                node.classList.remove('selected');
            });
            event.target.closest('.stage-node').classList.add('selected');
            
            // 根据关卡ID更新信息
            updateStageInfo(stageId, stageName);
        }
        
        // 更新关卡信息
        function updateStageInfo(stageId, stageName) {
            const stageData = {
                1: {
                    name: '血脉觉醒',
                    difficulty: '🟢 简单',
                    description: '初次觉醒家族血脉之力，学会感知血脉中的力量...',
                    power: '8,000',
                    stamina: '8',
                    goldReward: '1,500-2,200',
                    expReward: '3,000'
                },
                2: {
                    name: '族内纷争',
                    difficulty: '🟡 普通',
                    description: '家族内部出现分歧，年轻一代挑战长老权威...',
                    power: '10,000',
                    stamina: '9',
                    goldReward: '1,800-2,800',
                    expReward: '4,000'
                },
                3: {
                    name: '长老试练',
                    difficulty: '🟠 困难',
                    description: '家族长老设置考验，测试年轻族长的能力...',
                    power: '11,500',
                    stamina: '9',
                    goldReward: '2,200-3,200',
                    expReward: '4,500'
                },
                4: {
                    name: '分支之战',
                    difficulty: '💀 困难',
                    description: '家族血脉分化到了关键时刻，各分支为了传承权展开激烈争夺...',
                    power: '12,000',
                    stamina: '10',
                    goldReward: '2,000-3,500',
                    expReward: '5,000'
                }
            };
            
            const stage = stageData[stageId] || stageData[4];
            
            // 更新界面显示
            document.querySelector('.stage-title').textContent = stage.name;
            document.querySelector('.stage-difficulty').textContent = stage.difficulty;
            document.querySelector('.stage-description').textContent = stage.description;
            
            // 更新需求信息
            const requirements = document.querySelectorAll('.req-item');
            requirements[0].innerHTML = `💪 推荐战力: ${stage.power}`;
            requirements[1].innerHTML = `⚡ 体力消耗: ${stage.stamina}`;
            requirements[2].innerHTML = `🏆 通关奖励: 首次×2`;
            
            // 更新奖励信息
            const rewards = document.querySelectorAll('.reward-item');
            rewards[0].innerHTML = `💰 金币 ${stage.goldReward}`;
            rewards[1].innerHTML = `⚡ 经验 ${stage.expReward}`;
            rewards[2].innerHTML = `🎁 装备掉落几率 15%`;
            rewards[3].innerHTML = `📜 技能书碎片 × 3`;
        }
        
        // 进入战斗
        function enterBattle(stageName, stageId) {
            const mapSelection = document.getElementById('adventure-map-selection');
            const battleScreen = document.getElementById('adventure-battle');
            
            mapSelection.style.display = 'none';
            battleScreen.style.display = 'block';
            
            // 初始化战斗数据
            initBattleScreen(stageName, stageId);
        }
        
        // 初始化战斗界面
        function initBattleScreen(stageName, stageId) {
            const battleTitle = document.querySelector('.battle-title');
            if (battleTitle) {
                battleTitle.textContent = `${stageName} - 第1波`;
            }
            
            // 重置战斗数据
            window.battleState = {
                currentWave: 1,
                totalWaves: 3,
                isAuto: false,
                battleSpeed: 1,
                turnCount: 0,
                totalDamage: 0,
                battleTime: 0
            };
            
            // 初始化战斗界面
            initBattleInterface();
        }
        
        // 退出战斗
        function exitBattle() {
            if (window.battleTimer) {
                clearInterval(window.battleTimer);
            }
            
            const mapSelection = document.getElementById('adventure-map-selection');
            const battleScreen = document.getElementById('adventure-battle');
            
            mapSelection.style.display = 'block';
            battleScreen.style.display = 'none';
        }
        
        // 装备技能数据
        const equippedSkills = [
            { name: '烈阳斩', icon: '🔥', triggerRate: 28.5, damage: [2500, 5500], type: 'fire' },
            { name: '雷霆击', icon: '⚡', triggerRate: 25.2, damage: [2000, 4500], type: 'thunder' },
            { name: '家族秘技', icon: '🌟', triggerRate: 31.6, damage: [4000, 8000], type: 'ultimate' }
        ];
        
        // 获取装备技能列表
        function getEquippedSkills() {
            return equippedSkills;
        }
        
        // 计算总触发概率
        function calculateTotalTriggerRate() {
            return equippedSkills.reduce((total, skill) => total + skill.triggerRate, 0);
        }
        
        // 更新技能显示
        function updateSkillDisplay() {
            const totalRate = calculateTotalTriggerRate();
            const totalRateElement = document.getElementById('total-trigger-rate');
            if (totalRateElement) {
                totalRateElement.textContent = `总触发率: ${totalRate.toFixed(1)}%`;
            }
            
            const skillItems = document.querySelectorAll('.skill-display-item');
            skillItems.forEach((item, index) => {
                if (index < equippedSkills.length) {
                    const skill = equippedSkills[index];
                    const nameElement = item.querySelector('.skill-name');
                    const rateElement = item.querySelector('.skill-rate');
                    const iconElement = item.querySelector('.skill-icon');
                    
                    if (nameElement) nameElement.textContent = skill.name;
                    if (rateElement) rateElement.textContent = `${skill.triggerRate}%`;
                    if (iconElement) iconElement.textContent = skill.icon;
                }
            });
        }
        
        // 计算技能触发
        function calculateSkillTrigger() {
            const totalRate = calculateTotalTriggerRate();
            const random = Math.random() * 100;
            
            // 如果总概率大于100%，必定触发技能
            if (totalRate >= 100 || random < totalRate) {
                // 按权重选择技能
                let currentWeight = 0;
                const skillRandom = Math.random() * totalRate;
                
                for (const skill of equippedSkills) {
                    currentWeight += skill.triggerRate;
                    if (skillRandom <= currentWeight) {
                        return skill;
                    }
                }
            }
            
            return null; // 普攻
        }
        
        // 执行自动攻击
        function executeAutoAttack() {
            if (!window.battleState?.isAuto) return;
            
            const triggeredSkill = calculateSkillTrigger();
            
            if (triggeredSkill) {
                // 触发技能
                const damage = Math.floor(Math.random() * (triggeredSkill.damage[1] - triggeredSkill.damage[0])) + triggeredSkill.damage[0];
                performAttack(triggeredSkill.name, damage, triggeredSkill.icon);
            } else {
                // 普通攻击
                const damage = Math.floor(Math.random() * 2000) + 1500;
                performAttack('普攻', damage, '⚔️');
            }
        }
        
        // 执行攻击
        function performAttack(skillName, damage, icon) {
            // 更新伤害统计
            if (window.battleState) {
                window.battleState.totalDamage += damage;
                window.battleState.turnCount++;
                
                // 更新界面显示
                document.querySelector('.battle-stats .stat-item:nth-child(1) .stat-value').textContent = window.battleState.turnCount;
                document.querySelector('.battle-stats .stat-item:nth-child(2) .stat-value').textContent = window.battleState.totalDamage.toLocaleString();
            }
            
            // 添加战斗日志
            addBattleLog(`${icon} 李炎阳 使用 ${skillName}，对分支族长造成 ${damage.toLocaleString()} 点伤害`);
            
            // 更新敌人血量（模拟）
            updateEnemyHealth(damage);
            
            // 敌人反击
            setTimeout(() => {
                enemyAttack();
            }, 1000);
        }
        
        // 更新敌人血量
        function updateEnemyHealth(damage) {
            const enemyHpBar = document.querySelector('.enemy-slot.active .hp-fill');
            const enemyHpText = document.querySelector('.enemy-slot.active .hp-text');
            
            if (enemyHpBar && enemyHpText) {
                const currentHp = parseInt(enemyHpText.textContent.split('/')[0].replace(',', ''));
                const maxHp = parseInt(enemyHpText.textContent.split('/')[1].replace(',', ''));
                const newHp = Math.max(0, currentHp - damage);
                const hpPercent = (newHp / maxHp) * 100;
                
                enemyHpBar.style.width = hpPercent + '%';
                enemyHpText.textContent = `${newHp.toLocaleString()}/${maxHp.toLocaleString()}`;
                
                // 检查敌人是否死亡
                if (newHp <= 0) {
                    setTimeout(() => {
                        checkBattleProgress();
                    }, 1500);
                }
            }
        }
        
        // 敌人攻击
        function enemyAttack() {
            const damage = Math.floor(Math.random() * 1200) + 800;
            const playerHpBar = document.querySelector('.hero-hp .hp-fill');
            const playerHpText = document.querySelector('.hero-hp .hp-text');
            
            if (playerHpBar && playerHpText) {
                const currentHp = parseInt(playerHpText.textContent.split('/')[0].replace(',', ''));
                const maxHp = parseInt(playerHpText.textContent.split('/')[1].replace(',', ''));
                const newHp = Math.max(0, currentHp - damage);
                const hpPercent = (newHp / maxHp) * 100;
                
                playerHpBar.style.width = hpPercent + '%';
                playerHpText.textContent = `${newHp.toLocaleString()}/${maxHp.toLocaleString()}`;
            }
            
            addBattleLog(`⚔️ 分支族长 普攻，对李炎阳造成 ${damage.toLocaleString()} 点伤害`);
        }
        
        // 检查技能触发
        function checkSkillTrigger() {
            if (Math.random() < 0.15) { // 15%概率触发
                const triggerSkills = ['雷霆印记', '烈阳余威', '血脉共鸣'];
                const randomSkill = triggerSkills[Math.floor(Math.random() * triggerSkills.length)];
                const extraDamage = Math.floor(Math.random() * 1000) + 500;
                
                addBattleLog(`⚡ 李炎阳 技能触发：${randomSkill}，额外伤害 ${extraDamage.toLocaleString()}`);
                
                // 更新伤害统计
                if (window.battleState) {
                    window.battleState.totalDamage += extraDamage;
                    document.querySelector('.battle-stats .stat-item:nth-child(2) .stat-value').textContent = window.battleState.totalDamage.toLocaleString();
                }
            }
        }
        
        // 初始化战斗界面
        function initBattleInterface() {
            // 更新技能显示
            updateSkillDisplay();
            
            // 重置战斗状态
            if (window.battleState) {
                window.battleState.isAuto = false;
                window.battleState.turnCount = 0;
                window.battleState.totalDamage = 0;
                window.battleState.battleTime = 0;
            }
            
            // 重置按钮状态
            const battleBtn = document.getElementById('auto-battle-btn');
            if (battleBtn) {
                battleBtn.className = 'battle-control-btn start-battle';
                battleBtn.innerHTML = '<span class="btn-icon">▶️</span><span class="btn-text">开始战斗</span>';
            }
        }
        
        // 切换自动战斗
        function toggleAutoBattle() {
            if (!window.battleState) {
                window.battleState = {
                    currentWave: 1,
                    totalWaves: 3,
                    isAuto: false,
                    battleSpeed: 1,
                    turnCount: 0,
                    totalDamage: 0,
                    battleTime: 0
                };
            }
            
            window.battleState.isAuto = !window.battleState.isAuto;
            const battleBtn = document.getElementById('auto-battle-btn');
            
            if (window.battleState.isAuto) {
                // 开始自动战斗
                battleBtn.className = 'battle-control-btn stop-battle';
                battleBtn.innerHTML = '<span class="btn-icon">⏸️</span><span class="btn-text">停止战斗</span>';
                startAutoBattle();
            } else {
                // 停止自动战斗
                battleBtn.className = 'battle-control-btn start-battle';
                battleBtn.innerHTML = '<span class="btn-icon">▶️</span><span class="btn-text">开始战斗</span>';
                stopAutoBattle();
            }
        }
        
        // 开始自动战斗
        function startAutoBattle() {
            if (window.autoBattleTimer) {
                clearInterval(window.autoBattleTimer);
            }
            
            startBattleTimer();
            updateSkillDisplay();
            
            const attackInterval = 2000 / (window.battleState?.battleSpeed || 1);
            
            window.autoBattleTimer = setInterval(() => {
                if (window.battleState?.isAuto) {
                    executeAutoAttack();
                } else {
                    clearInterval(window.autoBattleTimer);
                }
            }, attackInterval);
        }
        
        // 停止自动战斗
        function stopAutoBattle() {
            if (window.autoBattleTimer) {
                clearInterval(window.autoBattleTimer);
                window.autoBattleTimer = null;
            }
            
            if (window.battleTimer) {
                clearInterval(window.battleTimer);
                window.battleTimer = null;
            }
        }
        
        // 切换战斗速度
        function toggleBattleSpeed() {
            if (!window.battleState) {
                window.battleState = { battleSpeed: 1 };
            }
            
            window.battleState.battleSpeed = window.battleState.battleSpeed === 1 ? 2 : 
                                             window.battleState.battleSpeed === 2 ? 3 : 1;
            const speedText = document.getElementById('speed-text');
            speedText.textContent = `⚡ ${window.battleState.battleSpeed}x`;
            
            // 应用新的战斗速度
            updateBattleSpeed();
        }
        
        // 更新战斗速度
        function updateBattleSpeed() {
            if (window.autoBattleTimer && window.battleState?.isAuto) {
                // 重新启动自动战斗以应用新速度
                stopAutoBattle();
                startAutoBattle();
            }
        }
        
        // 检查战斗进度
        function checkBattleProgress() {
            addBattleLog('🎉 敌人被击败！准备下一波...');
            
            if (window.battleState) {
                window.battleState.currentWave++;
                
                if (window.battleState.currentWave > window.battleState.totalWaves) {
                    // 战斗胜利
                    setTimeout(() => {
                        showBattleVictory();
                    }, 2000);
                } else {
                    // 下一波敌人
                    setTimeout(() => {
                        startNextWave();
                    }, 2000);
                }
            }
        }
        
        // 开始下一波
        function startNextWave() {
            const battleTitle = document.querySelector('.battle-title');
            if (battleTitle && window.battleState) {
                battleTitle.textContent = battleTitle.textContent.replace(/第\d+波/, `第${window.battleState.currentWave}波`);
            }
            
            // 重置敌人血量
            const enemyHpBar = document.querySelector('.enemy-slot.active .hp-fill');
            const enemyHpText = document.querySelector('.enemy-slot.active .hp-text');
            
            if (enemyHpBar && enemyHpText) {
                enemyHpBar.style.width = '100%';
                enemyHpText.textContent = '50,000/50,000';
            }
            
            addBattleLog(`🌊 第${window.battleState.currentWave}波敌人出现！`);
        }
        
        // 显示战斗胜利
        function showBattleVictory() {
            const victoryHtml = `
                <div style="text-align: center; line-height: 1.6;">
                    <h2 style="color: #d4af37; margin-bottom: 20px;">🎉 战斗胜利！</h2>
                    <div style="background: rgba(78, 205, 196, 0.2); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h3 style="color: #4ecdc4; margin-bottom: 15px;">🏆 战斗统计</h3>
                        <div style="text-align: left; font-size: 0.95em;">
                            <div>⏱️ 战斗时长: ${formatBattleTime()}</div>
                            <div>🗡️ 总回合数: ${window.battleState?.turnCount || 0}</div>
                            <div>💥 总伤害: ${window.battleState?.totalDamage?.toLocaleString() || 0}</div>
                            <div>📊 平均DPS: ${calculateAverageDPS()}</div>
                        </div>
                    </div>
                    <div style="background: rgba(212, 175, 55, 0.2); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                        <h3 style="color: #d4af37; margin-bottom: 15px;">🎁 获得奖励</h3>
                        <div style="text-align: left; font-size: 0.95em;">
                            <div>💰 金币 +3,200 (首次通关×2)</div>
                            <div>⚡ 经验 +5,000</div>
                            <div>📜 技能书碎片 +3</div>
                            <div>🎁 获得装备: 分支族长之戒 (蓝色)</div>
                        </div>
                    </div>
                    <button onclick="returnToMap()" style="background: #d4af37; color: #2c1810; border: none; padding: 12px 30px; border-radius: 8px; font-size: 1.1em;">返回地图</button>
                </div>
            `;
            
            showModal('战斗胜利', victoryHtml);
        }
        
        // 返回地图
        function returnToMap() {
            closeSkillDetails(); // 关闭胜利弹窗
            exitBattle(); // 退出战斗界面
        }
        
        // 格式化战斗时间
        function formatBattleTime() {
            const time = window.battleState?.battleTime || 0;
            const minutes = Math.floor(time / 60);
            const seconds = time % 60;
            return `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // 计算平均DPS
        function calculateAverageDPS() {
            const totalDamage = window.battleState?.totalDamage || 0;
            const battleTime = window.battleState?.battleTime || 1;
            return Math.floor(totalDamage / battleTime).toLocaleString();
        }
        
        // 启动战斗计时器
        function startBattleTimer() {
            if (window.battleTimer) {
                clearInterval(window.battleTimer);
            }
            
            window.battleTimer = setInterval(() => {
                if (window.battleState) {
                    window.battleState.battleTime++;
                    const timeText = document.querySelector('.battle-stats .stat-item:nth-child(3) .stat-value');
                    if (timeText) {
                        timeText.textContent = formatBattleTime();
                    }
                }
            }, 1000);
        }
        
        // 添加战斗日志
        function addBattleLog(message) {
            const battleLog = document.querySelector('.battle-log');
            if (battleLog) {
                const logItem = document.createElement('div');
                logItem.className = 'log-item';
                logItem.textContent = message;
                
                battleLog.appendChild(logItem);
                
                // 保持最多显示3条日志
                const logItems = battleLog.querySelectorAll('.log-item');
                if (logItems.length > 3) {
                    battleLog.removeChild(logItems[0]);
                }
                
                // 自动滚动到最新日志
                battleLog.scrollTop = battleLog.scrollHeight;
            }
        }
        
        // 更新地图内容
        function updateMapContent(mode) {
            // 这里可以根据不同模式(era/challenge/trial)更新地图内容
            // 目前先显示时代征程的内容
        }
    </script>
</body>
</html> 