---
alwaysApply: true
---
---
description: "微信小游戏开发编码规范，包括项目结构、命名规范、性能优化、API使用和安全要求等完整开发标准"
globs:
  - "*.js"
  - "*.json"
  - "*.wxml"
  - "*.wxss"
  - "*.ts"
  - "game.js"
  - "game.json"
  - "project.config.json"
  - "app.js"
  - "app.json"
alwaysApply: true
---

# WeChat Minigame Coding Standard

## 1. Project Structure
- Clear separation of assets, code, config, and subpackages.
```text
ProjectName/
  ├── assets/
  ├── js/
  ├── game.js
  ├── game.json
  ├── project.config.json
  └── README.md
```

## 2. Naming Conventions
- JS: camelCase; images/audio: lowercase + underscore; config: lowercase.
- Variables: camelCase; constants: UPPER_CASE; private: _prefix.
- Functions: verbCamelCase; events: onPrefix; classes: PascalCase.

## 3. Coding Style
- 2-space indent, space after operators/commas.
- Use parentheses for conditions, no space before function parentheses.
- JSDoc for function docs; group related code; split long functions.

## 4. Modularization & Dependency
- Use CommonJS (`module.exports`, `require`).
- Import dependencies at top; avoid circular deps.
- Use singleton for global state.

## 5. WeChat API Usage
- Follow official docs; correct param/return types.
- Use Promise/async-await for async; always handle errors.
- Only request necessary permissions; check before asking; handle denial.

## 6. Performance Optimization
- Use subpackage loading, load assets on demand, compress images.
- Reduce canvas redraws, use object pool for frequent objects.
- Clean up unused objects/listeners, limit cache size.
- Avoid heavy logic in main loop; use Worker for heavy tasks.

## 7. Security
- Sensitive data only on server; HTTPS; encrypt local data.
- Follow privacy policy; only collect necessary info; provide privacy notice.
- Server-side for critical logic; use signature validation; detect anomalies.

## 8. Debug & Testing
- Unified log levels; detailed logs in dev, minimal in prod.
- Use try-catch, global error handler, WeChat error monitor.
- Monitor FPS/memory; use performance tools in dev.

## 9. Release
- Semantic versioning; set version in game.json.
- Test on multiple devices; check resource loading, features, compliance.
- Automated build/test; gray release.

---
Last updated: 2024-06-20
Maintenance: Update if new platform features or best practices arise.

