# 核心机制与时代总览

**文档说明**: 本文档是游戏剧情系统的最高设计纲领。它整合了**并发危机叙事框架**、**核心传承系统**，并提供了九大时代的概览。具体的时代剧情与世界事件已被拆分至独立的时代文件中。

---

## 目录
- [第一部分：核心叙事框架：并发危机 (如何讲故事)](#第一部分核心叙事框架并发危机-如何讲故事)
  - [1.1. 核心思想：中心辐射与连锁反应](#11-核心思想中心辐射与连锁反应)
  - [1.2. 核心数据驱动：全局世界状态 (GlobalState)](#12-核心数据驱动全局世界状态-globalstate)
  - [1.3. 核心反馈机制：《废土快报》](#13-核心反馈机制废土快报)
  - [1.4. 特殊剧情物品 (Key Story Items)](#14-特殊剧情物品-key-story-items)
- [第二部分：核心传承系统 (选择的长线影响)](#第二部分核心传承系统-选择的长线影响)
  - [2.1. 触发规则：道之根基的确立](#21-触发规则道之根基的确立)
  - [2.2. 当世之刻：道之烙印 (临时增益)](#22-当世之刻道之烙印-临时增益)
  - [2.3. 后世之痕 (一)：历史回响地标 (永久副本)](#23-后世之痕-一历史回响地标-永久副本)
  - [2.4. 后世之痕 (二)：天赋树解锁 (永久成长)](#24-后世之痕-二天赋树解锁-永久成长)
- [第三部分：纪元概览](#第三部分纪元概览)
- [并发危机系统与世界地图模式设计文档](#并发危机系统与世界地图模式设计文档)
  - [1. 核心设计理念：《巫师3》式"中心辐射"模型](#1-核心设计理念巫师3式中心辐射模型)
  - [2. 关键系统之一："世界状态"全局数据](#2-关键系统之一世界状态全局数据)
  - [3. 关键系统之二："中心辐射式"关卡流程](#3-关键系统之二中心辐射式关卡流程)
  - [4. 关键系统之三：《废土快报》反馈机制](#4-关键系统之三废土快报反馈机制)
  - [5. 技术实现要点](#5-技术实现要点)
  - [6.5 世界事件系统 (World Event System)](#65-世界事件系统-world-event-system)
- [第四部分：轮回历练与历史回响（可选挑战）](#第四部分轮回历练与历史回响可选挑战)
  - [4.1. 核心概念澄清：双模式下的地标角色](#41-核心概念澄清双模式下的地标角色)
  - [4.2. 轮回历练副本难度等级体系](#42-轮回历练副本难度等级体系)
  - [4.3. 【官方】轮回历练副本设计模板 V2.0](#43-官方轮回历练副本设计模板-v20)

---

## 第一部分：核心叙事框架：并发危机 (如何讲故事)

### 1.1. 核心思想：中心辐射与连锁反应
本游戏放弃传统的线性剧情结构，采用**"中心辐射"**模型。玩家在指挥室作为中心枢纽，通过世界地图自由选择应对多个**同时发生、并行发展**的危机。

玩家在一个危机中的选择，会通过更新一个全局的**"世界状态"**，动态地影响另一个危机的内容或解决方式，形成真正的连锁反应。

### 1.2. 核心数据驱动：全局世界状态 (GlobalState)
所有剧情、选择、乃至世界状态的唯一数据源是一个全局JSON对象`GlobalState`。
```json
{
  "crisis_status": { "traitor": "active", "wolf_king": "active", ... },
  "world_modifiers": { "authority": 0, "diplomacy": 0, ... },
  "story_flags": [ "traitor_executed", ... ]
}
```

### 1.3. 核心反馈机制：《废土快报》
每次玩家完成一个危机节点的任务返回指挥室时，系统会根据`GlobalState`的变动，生成一份名为**《废土快报》**的总结。它会清晰地展示玩家的选择带来了哪些直接后果和间接影响，让玩家直观地感受到世界的动态变化。

### 1.4. 特殊剧情物品 (Key Story Items)
除了数值和状态，玩家在事件中还可能获得**一次性的、具有特殊功能的剧情物品**。这些物品不是常规的装备或材料，而是用于解锁后续隐藏选项或提供关键情报的"钥匙"。
- **设计理念**: 强化玩家选择的连续性，让当前的行为能为未来的决策铺路。
- **物品示例**:
    - **【敌营堪舆图】**: (消耗品) 在第三时代"屈辱的使团"支线中获得。可在后续针对该势力的战斗中使用，为我方全体提供一次性的命中率加成。
    - **【说客的信物】**: (关键物品) 在第三时代"屈辱的使团"支线中获得。可在后续与"战国说客·苏"相关的剧情中出示，解锁隐藏的对话选项，探寻其背后的秘密。
    - **【传道授业碑】**: (家族遗物) 在第四时代"智慧的共享"支线中获得。可放置在家族宗祠中，为所有族人提供永久性的修行速度加成。
    - **【试炼核心的密文】**: (技术图纸) 在第四时代"力量的枷锁"支线中获得。用于解锁并升级家族领地的特殊防御建筑。
    - **【残缺的星图】**: (世界线索) 在第四时代"星空下的低语"支线中获得。是解锁后续某个隐藏时代或特殊世界事件的关键线索。

---

## 第二部分：核心传承系统 (选择的长线影响)

本部分详细描述玩家的选择如何通过**"后世之痕"** (永久遗产) 与**"当世之刻"** (临时影响) 这两大基石，带来超越单次轮回的长线收益和代价。其具体表现形式——如"历史回响地标"副本内的具体事件，其设计范式和模板可参考 **[随机事件系统设计.md](../随机事件系统设计.md)**。

### 2.1. 触发规则：道之根基的确立
- 在一个轮回中，玩家在处理一系列并发危机的过程中，其抉择倾向将共同塑造其**"道之根基"**。
- 当"道"被首次确立时（例如，一系列铁血选择后，系统判定玩家确立了**铁血道**），将立即触发【传承包】效果：
  1.  获得一个强大的临时增益 **【道之烙印】** (当世之刻)。
  2.  为所有后续轮回永久解锁对应的 **【历史回响】** (后世之痕) 与 **【天赋树】** (后世之痕)。
- **"道"是唯一的**：每个轮回中，玩家只能拥有一种"道"。首次确立后，该轮回中的后续抉择将不再触发新的【传承包】。

### 2.2. 当世之刻：道之烙印 (临时增益)
这是一个强大的临时增益，旨在帮助玩家克服眼前的危机。
- **生效范围**：**仅在获得该烙印的当前危机节点内生效**。

##### 🗡️ 铁血道烙印 - "坚韧意志"
- **刻印效果**：对精英/Boss敌人伤害 +8%，普通治疗效果 -15%，死亡时30%概率以1血复活（每场战斗限1次）。**(数值待调整)**
- **视觉表现**：角色周身会环绕着淡淡的赤色气焰，眼中偶尔闪过坚毅的寒光。

##### 🤝 仁德道烙印 - "慈悲护佑"
- **刻印效果**：所有治疗效果 +20%，暴击率 -8%，队友死亡时，自身获得"复仇之怒"：下次攻击必定暴击。
- **视觉表现**：角色脚下会浮现一个柔和的金色光环，行动时有光点随行。

##### 🧠 智谋道烙印 - "运筹帷幄"
- **刻印效果**：暴击率 +10%，技能冷却时间 +10%，战斗开始时，可预见所有敌人首回合的行动顺序。
- **视觉表现**：角色头顶偶尔会浮现一个由符文构成的、若隐若现的智慧法阵。

##### 👁️ 洞察道烙印 - "明察秋毫"
- **刻印效果**：闪避率 +15%，攻击力 -8%，完美闪避（Dodge）后，下一次攻击必定暴击。
- **视觉表现**：角色眼瞳中会闪烁着深邃的星芒，仿佛能看穿一切。

### 2.3. 后世之痕 (一)：历史回响地标 (永久副本)
在对应的抉择发生地，会为所有后续的轮回留下一个永久的、可交互的**【历史回响地标】**。这不仅是一个物质印记，更是一个**固定的副本入口**。先祖的道路将为家族烙下永恒的"宿敌"，这个抉择的代价，将由后代在进入这些地标进行**"轮回历练"**时直面。

**重要：历史回响的时代唯一性**
虽然【道之烙印】和其解锁的【后世之痕】天赋树是贯穿所有时代的通用传承，但【历史回响】本身是**时代限定且唯一**的。玩家在哪一个时代首次确立"道之根基"，就会在对应的时代背景下，创造出独一无二的物质印记，并烙下与那个时代紧密相关的"宿敌"。在**第一时代**，这具体体现在**"内部的背叛"**这一核心危机中。玩家处理此危机的方式，将直接决定在本时代留下的【历史回响地标】。

##### 🗡️ 抉择A - 公开处决：【叛徒之冢】（铁血道）
- **外观变化**：在抉择发生的区域，将永久出现一座由黑色玄武岩构成的石碑，上面用血色文字铭刻着第一个叛徒的姓名。
- **宿敌烙印：永久代价**：先祖的铁血抉择，也为后世种下了宿敌的种子。在所有后续的**"轮回历练"**中，**"叛徒"类型**的敌人将作为此地标副本的核心挑战，其生命值与攻击力提升15% **(数值待调整)**，成为家族必须世代面对的劲敌。
- **先祖庇佑：通关奖励**：作为平衡，当后代家主**首次成功通关【叛徒之冢】的轮回历练**后，将为**当前整个轮回**激活名为 **"先祖威严"** 的强大被动效果：对"叛徒"类型的敌人威胁值大幅提升，并无视其10%的防御 **(数值待调整)**。
- **氛围描述**：石碑周围的空气似乎都比别处更冷冽，它像一个沉默的警告，无声地诉说着背叛的代价，警示着后人铁腕治家的必要性。

##### 🤝 抉择B - 秘密流放：【和谈亭】（仁德道）
- **外观变化**：在抉择发生的区域，将永久出现一座古朴的凉亭，亭中石桌石凳俱全，仿佛依旧等待着谈判的双方。
- **宿敌烙印：永久代价**：先祖的仁德抉择，留下了未来可能被利用的隐患。在所有后续的**"轮回历练"**中，**"流亡者"或"复仇者"类型**的敌人将作为此地标副本的核心挑战，他们会以更强的姿态归来，其闪避率与暴击率提升10% **(数值待调整)**。
- **先祖庇佑：通关奖励**：作为平衡，当后代家主**首次成功通关【和谈亭】的轮回历练**后，将为**当前整个轮回**激活名为 **"仁者之风"** 的强大被动效果：在进行外交、说服相关的选项时，成功率提升15% **(数值待调整)**。
- **氛围描述**：凉亭虽历经风雨却依然洁净，似乎有一股温和的力量守护着它，见证着先祖以沟通化解干戈的智慧与勇气。

##### 🧠 抉择C - 反间悲剧：【密信石】（智谋道）
- **外观变化**：在抉择发生的区域，将永久出现一块刻有复杂暗号与星图的神秘巨石。
- **宿敌烙印：永久代价**：先祖的智谋让敌人刻骨铭心，也让他们变得更加警觉。在所有后续的**"轮回历练"**中，**"密探"或"刺客"类型**的敌人将作为此地标副本的核心挑战，他们会拥有更高的侦测能力，且攻击有20%几率无视部分护甲 **(数值待调整)**。
- **先祖庇佑：通关奖励**：作为平衡，当后代家主**首次成功通关【密信石】的轮回历练**后，将为**当前整个轮回**激活名为 **"智者传承"** 的强大被动效果：初始"智慧"属性+2，并在开局时自动获得一条关于当前时代危机的、未加密的隐藏情报。
- **氛围描述**：巨石上的符文在特定角度下似乎会缓缓流动，它静静地矗立在那里，仿佛是先祖留下的一道谜题，等待着同样聪慧的后人前来解读。

##### 👁️ 抉择D - 最终审问：【观星台】（洞察道）
- **外观变化**：在抉择发生的区域，将永久出现一座面朝天空的圆形石台，地面上刻画着古老的天体运行轨迹。
- **宿敌烙印：永久代价**：先祖的洞察虽然揭示了阴谋，但也让幕后黑手变得更加谨慎和强大。在所有后续的**"轮回历练"**中，**"阴谋家"或拥有"潜行"技能**的敌人将作为此地标副本的核心挑战，他们更难被识破，且技能附带的负面效果持续时间增加25% **(数值待调整)**。
- **先祖庇佑：通关奖励**：作为平衡，当后代家主**首次成功通关【观星台】的轮回历练**后，即可获得一个名为 **"洞察先机"** 的强大消耗性能力（每个轮回限用1次），效果为：可立即预知接下来所面临的任意一个危机节点中的所有选项及其直接后果。
- **氛围描述**：站上石台，仿佛整个夜空都变得触手可及。它象征着先祖超越表象、洞察本质的非凡能力，并为后人提供了一个窥见未来的机会。

### 2.4. 后世之痕 (二)：天赋树解锁 (永久成长)
当一个拥有【道之烙印】的轮回结束时，先祖的"道"会化为永恒的"后世之痕"，为家族的**天赋树系统**解锁一个全新的、永久性的分支。

##### 🗡️ 铁血传承分支（需铁血道烙印解锁）
```
- 铁血威严 [5天赋点]: 开局"威望"属性+2。(数值待调整)
- 不妥协者 [10天赋点]: 面对"叛徒"类敌人时，所有伤害提升15%。(数值待调整)
- 坚韧血脉 [15天赋点]: 当生命值低于30%时，攻击力提升25%。(数值待调整)
- 铁血族魂 [25天赋点]: (终极天赋) 轮回开始时，有50%概率直接获得弱化版的"坚韧意志"烙印，持续整个轮回。(概率和效果待调整)
```

##### 🤝 仁德传承分支（需仁德道烙印解锁）
```
- 仁德威望 [5天赋点]: 开局"外交"属性+2。(数值待调整)
- 感化之心 [10天赋点]: 在战斗中，有5%的概率使非精英敌人主动放弃战斗并逃跑。(概率待调整)
- 慈悲护体 [15天赋点]: 所有治疗技能会额外为目标施加一个吸收少量伤害的护盾。(效果待调整)
- 仁德族魂 [25天赋点]: (终极天赋) 轮回开始时，所有外交、贸易、结盟相关的选项，初始成功率+10%。(数值待调整)
```

##### 🧠 智谋传承分支（需智谋道烙印解锁）
```
- 智者威望 [5天赋点]: 开局"智慧"属性+2。(数值待调整)
- 运筹帷幄 [10天赋点]: 每场战斗开始时，可以免费重置一次所有技能的冷却时间。(机制待调整)
- 智慧血脉 [15天赋点]: 释放技能时，有10%的概率使该技能的暴击率提升50%。(概率待调整)
- 智谋族魂 [25天赋点]: (终极天赋) 轮回开始时，自动获得2条关于当前时代危机的、需要解密的隐藏情报。(数值待调整)
```

##### 👁️ 洞察传承分支（需洞察道烙印解锁）
```
- 洞察威望 [5天赋点]: 开局"洞察"属性+2。(数值待调整)
- 先知之眼 [10天赋点]: 与精英或Boss敌人战斗时，可以永久看到其下一个技能的目标。(机制待调整)
- 敏锐血脉 [15天赋点]: 完美闪避的判定窗口时间增加25%。(数值待调整)
- 洞察族魂 [25天赋点]: (终极天赋) 轮回开始时，在面对所有危机节点的重大抉择时，都会额外出现一个"再思考一下"的选项，可以让你看到每个选项最坏的可能结果。(机制待调整)
```

---

## 第三部分：纪元概览

| 大阶段 | 轮回 | 纪元名称 | 核心主题 | 主要冲突 |
|--------|------|----------|----------|----------|
| **序章：余烬中的刀锋** | 第一轮回 | 生存求存 | 武道求生 | 强邻环伺 + 野兽凶戾 |
| **(衰落时代)** | 第二轮回 | 血脉分歧 | **内部矛盾** | 族内三派争斗 |
| | 第三轮回 | 强邻入侵 | 外敌威胁 | 周边势力侵犯 |
| **承启：砺锋于微光** | 第四轮回 | 重建基业 | 武仙启蒙 | 妖兽横行 + 群雄并起 |
| **(复苏时代)** | 第五轮回 | 异域挑战 | **西方势力** | 圣殿+奥术+机械文明 |
| | 第六轮回 | 存亡危机 | **灭族风险** | 血脉消散 + 清洗者 |
| **鼎革：破茧见仙踪** | 第七轮回 | 巅峰突破 | 仙道崛起 | 妖王争锋 + 宗门竞争 |
| **(鼎盛时代)** | 第八轮回 | 域外入侵 | **域外威胁** | 混沌魔军 + 星空古神 |
| | 第九轮回 | 薪火升华 | 超越轮回 | 道心考验 |

---

# 并发危机系统与世界地图模式设计文档

## 1. 核心设计理念：《巫师3》式"中心辐射"模型

本系统旨在实现一个动态、互联的剧情体验，即使在独立关卡的架构下，也能让玩家感受到各个危机事件的并发发展和相互影响。

- **核心思想**：玩家的选择不改变"物理时间"，而是持续更新一个全局的**"世界状态"**。每个关卡的内容都会根据这个"世界状态"动态生成，从而反映出玩家此前所有决策带来的连锁反应。
- **玩家体验**：玩家作为决策者，在一个中心枢-
纽（如营地）自由选择优先处理哪个危机，并能通过特定反馈机制（如《废土快报》）清晰地看到自己的每个选择如何影响了整个世界的走向。

---

## 2. 关键系统之一："世界状态"全局数据

"世界状态"是一个全局的JSON对象，是整个系统的驱动核心。它记录了所有关键的剧情变量。

**`GlobalState` 对象结构示例：**
```json
{
  "crisis_status": {
    "traitor": "unresolved", // 危机状态: unresolved, resolved
    "wolf": "unresolved",
    "supply": "unresolved",
    "external": "unresolved"
  },
  "story_flags": [], // 故事标记: 记录玩家的关键选择，如 ["traitor_forgiven", "wolf_resolved_by_trap"]
  "world_modifiers": {
    "authority": 0,       // 玩家属性：威望
    "diplomacy": 0,       // 玩家属性：外交
    "intelligence": 0,    // 玩家属性：智慧
    "wolf_threat_level": 1.0,      // 危机参数：狼王威胁等级
    "supply_shortage_rate": 1.0, // 危机参数：补给短缺率
    "external_hostility": 1.0,    // 危机参数：外部敌意等级
    "power_understanding": 0,      // 天地力量理解度，探索进展指标
    "purifier_influence": 5,       // 清洗者影响力，血脉原罪危机核心参数
    "bloodline_purge_threat": 5,   // 血脉清洗威胁度，内部恐怖程度指标
    "clan_paranoia_level": 3,      // 家族猜忌度，内部信任破裂程度
    "integration_progress": 0,      // 仙武融合进度，新力量体系发展水平
    "ascension_progress": 0,       // 飞升进度，超脱探索进展
    "dao_understanding": 0,        // 大道领悟，终极真理认知水平
    "world_truth_discovery": 0,     // 世界真相探索度，本源奥秘解开程度
    "void_corruption": 0,          // 虚空腐蚀度
    "alliance_trust": 10,          // 文明联盟信任度
    "world_defense_integrity": 10,  // 世界防线完整度
    "legacy_purity": 0,            // 传承纯粹度
    "cosmic_understanding": 0,     // 宇宙真理理解度
    "final_choice_impact": 0       // 最终抉择影响
  }
}
```

---

## 3. 关键系统之二："中心辐射式"关卡流程

### 3.1. 核心场景：营地/指挥室

这是玩家的"家"，是所有行动的起点和终点。玩家在此进行休整、准备，并进行最重要的决策。

### 3.2. 交互核心：世界地图界面

在指挥室内，玩家通过与一张"世界地图"或"战略沙盘"交互来选择任务。

**界面元素设计：**
- **背景**：一张描绘周边区域的简陋废土风格地图。
- **中心点**：家族的"营地"图标。
- **任务点**：周围散布着几个闪烁的**"危机事件"图标**。

**危机图标信息规范：**
- **图标样式**：每个危机有独特的、易于识别的视觉符号（如：叛徒-裂开的面具🎭，补给-空粮袋🌾）。
- **标题**：简明扼要的危机名称，如 `危机：内部的背叛`。
- **简介**：一句话描述危机核心。
- **动态状态**：显示与此危机相关的**"世界修正值"**，让玩家能直观评估严重性（如 `威胁度：高（1.3）`）。
- **已解决状态**：完成的危机图标变为灰色，并显示解决方案摘要。

### 3.3. 玩家行动循环

1.  **决策**：在指挥室查看世界地图，评估各个危机的当前状态。
2.  **选择**：点击一个未解决的危机图标，确认后进入该危机的独立关卡。
3.  **体验**：关卡内容根据进入时的"世界状态"动态生成（如敌人强度、可用选项、NPC对话等都可能不同）。
4.  **行动**：在关卡内完成任务并做出关键选择。
5.  **返回**：完成关卡后，自动返回指挥室。
6.  **反馈**：系统立即弹出《废土快报》，总结玩家的选择所带来的直接后果和对其他危机的连锁影响。
7.  **世界更新**：世界地图上的信息被刷新，反映出"世界状态"的最新变化。
8.  **循环**：玩家再次查看地图，开始新一轮的决策。

---

## 4. 关键系统之三：《废土快报》反馈机制

这是向玩家传达世界变化的关键渠道，确保玩家能理解其选择的深远影响。

**快报内容结构：**
- **头条新闻**：聚焦于玩家刚刚完成的事件和做出的核心选择。
- **深度分析/连带影响**：用几句话描述这个选择如何影响了其他未解决的危机。这是体现"并发感"的核心。
- **威胁评估更新**：以简明的列表或图表，直观展示各个危机的威胁度数值变化。

---

## 5. 技术实现要点

- **全局状态管理**：建议使用`App.globalData`结合`wx.setStorageSync`来管理`GlobalState`对象，确保数据在应用生命周期内持续存在且可被所有页面访问。
- **关卡动态生成**：每个关卡的`onLoad`生命周期函数中，必须读取全局的`GlobalState`，并根据其中的值来调整关卡内的变量（敌人数量、属性、剧情文本、特殊选项等）。
- **条件触发系统**：部分危机节点为条件触发，需要检查`story_flags`数组中是否包含特定标记。在世界地图界面生成危机图标时，必须先进行条件判断，只显示符合触发条件的危机节点。

### 5.1 条件触发节点完整列表

```javascript
// 条件触发节点检查示例
function checkConditionalCrisis(era, globalState) {
  const conditionalCrisis = [];
  
  // 第二时代条件触发
  if (era === 2) {
    // "归来的血脉"节点：需要第一时代选择"秘密流放"
    if (globalState.story_flags.includes('traitor_exiled')) {
      conditionalCrisis.push({
        id: 'returnees',
        name: '归来的血脉',
        condition: '第一时代选择了"秘密流放"选项'
      });
    }
  }
  
  // 第三时代条件触发
  if (era === 3) {
    // "信任危机爆发"节点：vigilance过高或选择了"立即逮捕"
    const highVigilance = globalState.world_modifiers.vigilance > 15;
    const hadPublicExecution = globalState.story_flags.includes('public_execution');
    
    if (highVigilance || hadPublicExecution) {
      conditionalCrisis.push({
        id: 'internal_paranoia',
        name: '信任危机爆发',
        condition: 'vigilance值过高(>15)或选择了"立即逮捕"'
      });
    }
  }
  
  return conditionalCrisis;
}
```

### 5.2 条件选项检查系统

某些危机节点内的特定选项也需要满足前置条件才能显示：

```javascript
// 条件选项检查示例
function checkConditionalOptions(era, nodeId, optionId, globalState) {
  // 第一时代条件选项
  if (era === 1) {
    // 叛徒危机的"将计就计"选项
    if (nodeId === 'traitor' && optionId === 'D') {
      return globalState.world_modifiers.intelligence > 3;
    }
    
    // 补给危机的"以人祭祀古树"选项
    if (nodeId === 'supply' && optionId === 'C') {
      return globalState.story_flags.includes('found_ancient_tree');
    }
  }
  
  // 第三时代条件选项
  if (era === 3) {
    // 三方联盟施压的"利用假情报设伏"选项
    if (nodeId === 'alliance_pressure' && optionId === 'D') {
      return globalState.story_flags.includes('false_info_success');
    }
  }
  
  return true; // 默认可用
}
```

### 5.3 隐藏事件触发系统

部分条件选项依赖于隐藏事件的触发，需要在适当时机检查并添加相应的story_flags：

```javascript
// 隐藏事件触发示例
function triggerHiddenEvents(era, globalState, context) {
  // 第一时代：探索时可能发现古树
  if (era === 1 && context === 'exploration') {
    const discoverChance = Math.min(0.3 + globalState.world_modifiers.insight * 0.05, 0.8);
    
    if (Math.random() < discoverChance) {
      globalState.story_flags.push('found_ancient_tree');
      
      // 显示发现提示
      wx.showModal({
        title: '意外发现',
        content: '在探索过程中，族人发现了一颗枯死的古树...',
        showCancel: false
      });
    }
  }
}
```

- **状态更新逻辑**：在每个关卡成功结束时，编写清晰的逻辑来计算并更新`GlobalState`，包括直接影响和连锁影响。
- **组件化**：建议将\"世界地图\"和\"废土快报\"开发为可复用的组件，便于维护和调用。

### 5.4 条件触发错误预防

为避免类似"归来的血脉"节点错误触发的问题，建议实施以下预防措施：

1. **强制条件检查**：在世界地图渲染时强制执行条件检查，未满足条件的节点不得显示
2. **开发阶段测试**：为每个条件触发节点编写专门的测试用例
3. **文档同步**：设计文档与代码实现保持严格一致，任何修改需同步更新
4. **调试模式**：开发模式下显示每个节点的触发条件和当前状态，便于调试

```javascript
// 调试模式示例
if (DEBUG_MODE) {
  console.log('第二时代危机检查:');
  console.log('- 基础危机: 继承权争夺, 发展路线之争');
  console.log('- 条件检查: traitor_exiled =', globalState.story_flags.includes('traitor_exiled'));
  console.log('- 归来的血脉节点:', globalState.story_flags.includes('traitor_exiled') ? '显示' : '隐藏');
}
```

### 6.5 世界事件系统 (World Event System)

#### 6.5.1 系统概述
"世界事件"是独立于主线"并发危机"的支线任务系统。它的核心目标是让`world_modifiers`参数真正驱动故事，使世界对玩家的选择作出动态反馈。

- **与主线危机的区别**：
  - **主线危机**：时代固有的、必须解决的核心矛盾。
  - **世界事件**：由玩家行为（导致参数变化）触发的可选支线，用于丰富世界、提供额外收益或挑战。

#### 6.5.2 触发与实现机制
- **触发条件**：每个世界事件都有一套基于`world_modifiers`参数的触发条件。游戏引擎在每个回合开始时检查，当满足条件时，事件便会出现在世界地图上。
- **表现形式**：在地图上生成独特的事件图标（如一个卷轴、一个感叹号等），与主线危机的图标明显区分。
- **生命周期**：部分事件有存在时限，如果玩家在规定时间内不处理，事件可能会自动消失或演变成更坏的结果。

**技术实现示例：**
```javascript
function checkWorldEvents(globalState) {
  const triggeredEvents = [];
  const modifiers = globalState.world_modifiers;
  const era = globalState.era;

  // 根据不同时代检查对应的世界事件
  switch (era) {
    case 1:
      if (modifiers.clan_population <= 5 && modifiers.economy <= 5) {
        triggeredEvents.push({ id: 'last_ember', name: '最后的火种' });
      }
      break;
    case 2:
      if (modifiers.development_path_tension >= 8) {
        triggeredEvents.push({ id: 'sundered_dojo', name: '分裂的道场' });
      }
      break;
    case 3:
      if (modifiers.neighbor_hostility >= 7 && modifiers.internal_betrayal_risk >= 5) {
        triggeredEvents.push({ id: 'bloody_invitation', name: '血色请柬' });
      }
      break;
    case 4:
      if (modifiers.beast_threat >= 8 && modifiers.mystical_potential >= 5) {
        triggeredEvents.push({ id: 'whispering_woods', name: '低语森林' });
      }
      break;
    case 5:
      if (modifiers.western_threat >= 7 && modifiers.power_understanding <= 3) {
        triggeredEvents.push({ id: 'stranded_marvel', name: '搁浅的奇物' });
      }
      break;
    case 6:
      if (modifiers.integration_progress >= 8 && modifiers.clan_paranoia_level >= 7) {
        triggeredEvents.push({ id: 'genius_nightmare', name: '融合天才的梦魇' });
      }
      break;
    case 7:
      if (modifiers.dao_understanding >= 10 && modifiers.world_truth_discovery >= 5) {
        triggeredEvents.push({ id: 'shattered_legacy', name: '破碎的传承' });
      }
      break;
    case 8:
      if (modifiers.alliance_trust <= 5 && modifiers.world_defense_integrity <= 6) {
        triggeredEvents.push({ id: 'broken_pact', name: '背弃的盟约' });
      }
      break;
    case 9:
      if (modifiers.cosmic_understanding >= 10 && modifiers.legacy_purity >= 8) {
        triggeredEvents.push({ id: 'echoes_of_the_stars', name: '星辰的回响' });
      }
      break;
  }
  
  return triggeredEvents;
}
```

#### 6.5.3 设计原则
- **奖励与风险并存**：成功解决世界事件应提供独特的奖励（如特殊`story_flags`、稀有资源、关键情报），但失败或处理不当也会带来惩罚。
- **参数强关联**：每个世界事件都应紧密围绕一到两个核心参数设计，让玩家清楚地感受到该参数的意义。
- **丰富世界观**：利用世界事件来补完主线剧情未能详述的世界角落和背景故事。 

---

## 第四部分：轮回历练与历史回响（可选挑战）

本部分定义了玩家在轮回过程中遇到的可选挑战，即"历史回响地标"副本，并为所有相关设计提供统一的规范和模板。

### 4.1. 核心概念澄清：双模式下的地标角色

为了统一设计，我们必须明确"历史回响地标"在游戏两种核心模式下的双重角色：

-   **在"轮回试炼模式"（线性推进）下**:
    -   **角色**: **可选的、可交互的副本入口**。
    -   **行为**: 玩家在线性推进关卡的过程中，会在地图上看到这些实体建筑。玩家可以自由选择是否进入，以挑战"轮回历练"副本，获取额外奖励。

-   **在"主线叙事模式"（战略沙盘）下**:
    -   **角色**: **不可交互的、永久性的历史纪念碑**。
    -   **行为**: 当玩家结束试炼、回归主线后，这些地标会作为家族历史的一部分，永久地矗立在战略沙盘上。它们是过往抉择的见证，不再提供副本入口功能，仅作为视觉上的历史标记。

### 4.2. 轮回历练副本难度等级体系

所有"历史回响地标"副本均采用统一的三级难度体系。玩家在进入副本前可以选择本次挑战的难度。更高难度的挑战需要在成功通关前一难度后才能解锁。

| 难度等级 | 中文名称 | 核心挑战 | 推荐进入时期 |
|---|---|---|---|
| **Bronze** | **初心试炼** | 敌人基础属性，机制简单。 | 首次解锁地标的当世或后续轮回初期。 |
| **Silver** | **传承试炼** | 敌人属性提升30%，部分敌人增加新技能，机制更复杂。 | 家族天赋有一定积累，对游戏有较深理解后。 |
| **Gold** | **至尊试炼** | 敌人属性大幅提升80%，所有敌人均可能获得强化，出现独特的Boss战。 | 游戏后期，追求极限挑战和顶级奖励。 |

### 4.3. 【官方】轮回历练副本设计模板 V2.0

> **【设计规范说明】**：自本文档更新之日起，所有时代地图文件中的"历史回响地标"副本设计，均应遵循此模板规范。此举旨在确保设计一致性，并将所有分散的机制统一收拢。

```markdown
---
### **历史回响：[地标名称] ([英文名])**
-   **解锁条件**：在第[N]时代，完成了[XXX]危机并选择了[YYY]选项。

#### **1. 副本背景**
-   **一句话概述**：对此地标背后历史事件的简短总结。
-   **氛围描述**：详细描述该地标的视觉风格、环境氛围以及给玩家的直观感受。

#### **2. 核心机制与宿敌**
-   **宿敌烙印 (永久代价)**：描述此选择为后世带来的永久性挑战（例如：特定类型的敌人获得永久性增强）。
-   **核心战斗机制**：说明此副本在战斗机制上的独特之处（例如：特殊的战场环境、敌人组合、战斗流程等）。

#### **3. 副本结构与三大试炼**

本副本由以下三种核心试炼构成，玩家需要依次通过，才能最终完成挑战。

##### **3.1 传承考验 (Test of Legacy)**
-   **形式**: 非战斗型考验，侧重于对家族历史和玩家过往选择的"回顾"。
    -   *（示例：情景问答"你的先祖在面对武仙遗迹时，选择了何种道路？"；或因果排序，让玩家将打乱的历史事件重新排序。）*
-   **目的**: 强化玩家对剧情的记忆，理解自己选择的长远影响。

##### **3.2 道德抉择 (Test of Morality)**
-   **形式**: 模拟两难情景，考验玩家是否会坚守或背离先祖的"道"。
    -   *（示例：在一个独立的微缩场景中，让玩家再次面对一个"被污染"的族人，并需要在"处决"与"拯救"之间做出选择。选择结果将与先祖对比，并产生即时效果。）*
-   **目的**: 检验玩家的价值观，并为当前轮回提供临时的、与"道"相关的增益或减益效果。

##### **3.3 技能试炼 (Test of Skill)**
-   **形式**: 核心战斗或策略考验，是【宿敌烙印】发挥作用的主要舞台。
    -   *（示例：对抗一个被【宿敌烙印】强化过的精英首领；或在特殊场景下抵御数波被强化的敌人；或进行一场需要优先击破特定目标的解谜式战斗。）*
-   **目的**: 将"宿敌烙印"带来的负面影响转化为富有挑战性的游戏内容。

#### **4. 难度与奖励**
-   **先祖庇佑 (通关奖励)**：描述玩家在**首次**通关此副本后能获得的、**持续整个当前轮回**的强大增益效果。
-   **分难度首次通关奖励**：
    -   **初心试炼 (Bronze)**: [材料、装备、天赋点等]
    -   **传承试炼 (Silver)**: [更高阶的材料、限定装备、更多天赋点等]
    -   **至尊试炼 (Gold)**: [顶级材料、专属遗物、大量天赋点、特殊称号等]
-   **重复挑战奖励**：[可重复获得的基础材料、资源等]
---
```