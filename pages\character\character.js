Page({
  data: {
    characterData: {
      name: '传承者',
      title: '初代家主',
      clanName: '龙族',
      clanEmblem: '🐲',
      clanLevel: 1,
      level: 1,
      experience: 0,
      nextLevelExp: 100,
      attack: 25,
      defense: 15,
      maxHp: 100,
      critRate: 5,
      attackSpeed: 1.2,
      dps: 30,
      gold: 1000,
      energy: 100,
      maxEnergy: 100,
      diamond: 0,
      skillPoints: 0,
      heritagePoints: 0
    },
    
    titles: [
      {
        id: 1,
        name: '新手冒险者',
        effect: '获得经验+10%',
        equipped: true,
        stats: [
          { type: 'exp', name: '经验获得', value: 10 }
        ]
      },
      {
        id: 2,
        name: '怪物杀手',
        effect: '攻击力+5',
        equipped: false,
        stats: [
          { type: 'attack', name: '攻击力', value: 5 }
        ]
      }
    ],
    
    gameStats: {
      playTime: '2小时30分',
      enemiesKilled: 42,
      goldEarned: 1580,
      equipmentFound: 8,
      skillsUsed: 156
    },
    
    expPercent: 0
  },

  onLoad() {
    console.log('角色页面加载')
    this.loadCharacterData()
  },

  onShow() {
    console.log('角色页面显示')
    this.refreshCharacterData()
  },

  // 加载角色数据
  loadCharacterData() {
    try {
      const savedData = wx.getStorageSync('gameData')
      if (savedData && savedData.playerData) {
        // 合并保存的数据
        const characterData = {
          ...this.data.characterData,
          ...savedData.playerData
        }
        
        this.setData({ characterData })
        this.updateCalculatedValues()
      }
    } catch (error) {
      console.error('加载角色数据失败:', error)
    }
  },

  // 刷新角色数据
  refreshCharacterData() {
    this.loadCharacterData()
  },

  // 更新计算属性
  updateCalculatedValues() {
    const { characterData } = this.data
    const expPercent = Math.floor((characterData.experience / characterData.nextLevelExp) * 100)
    
    this.setData({ expPercent })
  },

  // 编辑族群信息
  editClanInfo() {
    wx.showModal({
      title: '编辑族群信息',
      content: '此功能正在开发中，敬请期待！',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 管理称号
  manageTitles() {
    wx.showActionSheet({
      itemList: ['装备称号', '卸下称号', '查看所有称号'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.equipTitle()
            break
          case 1:
            this.unequipTitle()
            break
          case 2:
            this.viewAllTitles()
            break
        }
      }
    })
  },

  // 切换称号装备状态
  toggleTitle(e) {
    const titleId = e.currentTarget.dataset.id
    const titles = this.data.titles.map(title => {
      if (title.id === titleId) {
        title.equipped = !title.equipped
        
        // 显示提示
        wx.showToast({
          title: title.equipped ? '称号已装备' : '称号已卸下',
          icon: 'success'
        })
      }
      return title
    })
    
    this.setData({ titles })
    this.saveTitleData()
  },

  // 装备称号
  equipTitle() {
    const availableTitles = this.data.titles.filter(title => !title.equipped)
    if (availableTitles.length === 0) {
      wx.showToast({
        title: '没有可装备的称号',
        icon: 'none'
      })
      return
    }
    
    const titleNames = availableTitles.map(title => title.name)
    wx.showActionSheet({
      itemList: titleNames,
      success: (res) => {
        const selectedTitle = availableTitles[res.tapIndex]
        this.toggleTitleById(selectedTitle.id, true)
      }
    })
  },

  // 卸下称号
  unequipTitle() {
    const equippedTitles = this.data.titles.filter(title => title.equipped)
    if (equippedTitles.length === 0) {
      wx.showToast({
        title: '没有已装备的称号',
        icon: 'none'
      })
      return
    }
    
    const titleNames = equippedTitles.map(title => title.name)
    wx.showActionSheet({
      itemList: titleNames,
      success: (res) => {
        const selectedTitle = equippedTitles[res.tapIndex]
        this.toggleTitleById(selectedTitle.id, false)
      }
    })
  },

  // 查看所有称号
  viewAllTitles() {
    wx.showModal({
      title: '称号系统',
      content: `拥有称号: ${this.data.titles.length}\n已装备: ${this.data.titles.filter(t => t.equipped).length}`,
      showCancel: false
    })
  },

  // 根据ID切换称号状态
  toggleTitleById(titleId, equipped) {
    const titles = this.data.titles.map(title => {
      if (title.id === titleId) {
        title.equipped = equipped
      }
      return title
    })
    
    this.setData({ titles })
    this.saveTitleData()
    
    wx.showToast({
      title: equipped ? '称号已装备' : '称号已卸下',
      icon: 'success'
    })
  },

  // 保存称号数据
  saveTitleData() {
    try {
      wx.setStorageSync('titleData', {
        titles: this.data.titles,
        lastUpdate: Date.now()
      })
    } catch (error) {
      console.error('保存称号数据失败:', error)
    }
  },

  // 分享角色信息
  onShareAppMessage() {
    const { characterData } = this.data
    return {
      title: `我的${characterData.clanName}族角色等级已达到${characterData.level}级！`,
      path: '/pages/index/index'
    }
  }
}) 