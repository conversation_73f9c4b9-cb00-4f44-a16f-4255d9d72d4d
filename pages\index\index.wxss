/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

/* 顶部角色信息栏 */
.character-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.clan-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.clan-emblem {
  font-size: 60rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.clan-details {
  display: flex;
  flex-direction: column;
}

.clan-name {
  font-size: var(--font-lg);
  font-weight: 600;
}

.character-level {
  font-size: var(--font-sm);
  opacity: 0.9;
}

.resources {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.resource-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.resource-label {
  font-size: var(--font-md);
}

.resource-value {
  font-size: var(--font-sm);
  font-weight: 500;
}

/* 经验进度条 */
.experience-bar {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--card-bg);
  border-bottom: 2rpx solid var(--border-color);
}

.progress-label {
  margin-bottom: var(--spacing-xs);
}

.progress-label text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

/* 战斗区域 */
.battle-area {
  margin: var(--spacing-md);
}

.area-info {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.area-name {
  display: block;
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.area-description {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

/* 敌人信息 */
.enemy-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: #ffe6e6;
  border-radius: var(--border-radius-md);
  border: 2rpx solid var(--danger-color);
}

.enemy-avatar {
  font-size: 80rpx;
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--danger-color);
  border-radius: 50%;
  color: white;
}

.enemy-info {
  flex: 1;
}

.enemy-name {
  display: block;
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--danger-color);
  margin-bottom: var(--spacing-sm);
}

.enemy-health {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.health-text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  text-align: center;
}

/* 战斗控制 */
.battle-controls {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  align-items: center;
}

.battle-btn {
  flex: 1;
  max-width: 200rpx;
  height: 80rpx;
  font-size: var(--font-lg);
}

/* 技能栏 */
.skills-bar {
  margin: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--card-bg);
  border-radius: var(--border-radius-md);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: var(--font-md);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.skills-list {
  display: flex;
  gap: var(--spacing-sm);
  white-space: nowrap;
}

.skill-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  background: var(--light-color);
  border-radius: var(--border-radius-sm);
  min-width: 120rpx;
}

.skill-icon {
  font-size: 40rpx;
  margin-bottom: var(--spacing-xs);
}

.skill-name {
  font-size: var(--font-xs);
  text-align: center;
  margin-bottom: 4rpx;
}

.skill-level {
  font-size: var(--font-xs);
  color: var(--text-secondary);
}

/* 快捷操作区 */
.quick-actions {
  position: fixed;
  bottom: 120rpx;
  right: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* 离线奖励样式 */
.offline-reward {
  margin: var(--spacing-md);
  border: 2rpx solid var(--warning-color);
}

.offline-reward .card-header {
  background: var(--warning-color);
}

.offline-reward .card-content text {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-sm);
}

/* 动画效果 */
.battle-btn:active {
  animation: attackEffect 0.3s ease;
}

@keyframes attackEffect {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); background-color: #c0392b; }
  100% { transform: scale(1); }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .character-header {
    padding: var(--spacing-sm);
  }
  
  .clan-emblem {
    font-size: 48rpx;
    width: 60rpx;
    height: 60rpx;
  }
  
  .enemy-section {
    flex-direction: column;
    text-align: center;
  }
  
  .enemy-avatar {
    font-size: 60rpx;
    width: 80rpx;
    height: 80rpx;
  }
}
