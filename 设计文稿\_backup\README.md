# 月球（Yueqiu）- 游戏设计总览

---

### **1. 游戏定位与核心玩法**

本游戏是一款以**家族万年兴衰史**为背景，以**剧情体验**为核心的**放置类角色扮演游戏（RPG）**。玩家的核心目标是通过一代代人的努力，在波澜壮阔的历史中延续家族血脉，最终揭示世界的真相。

*   **游戏类型**: **剧情驱动的放置RPG (Story-Driven Idle RPG)**。
    *   游戏的重点在于体验宏大的叙事、培养角色、收集和强化装备。
    *   游戏中的"威望"、"士气"等策略数值主要服务于剧情分支，为玩家提供不同的叙事体验，而非核心经营玩法。

*   **战斗模式**: **全自动战斗 (Auto-Battler)**。
    *   战斗一旦开始便全程自动进行，玩家无法操作角色移动或释放技能。
    *   胜负完全取决于玩家在战前的策略配装。

*   **核心玩法循环 (Core Loop)**: 游戏的核心循环由"局内探索"和"局外轮回成长"两部分构成。
    *   **局内探索**: 在每一世中，玩家通过探索地图、经历剧情事件、挑战自动战斗来推进关卡。
    *   **轮回成长 (Reincarnation)**: 当在局内遭遇无法逾越的挑战时，玩家可以选择**轮回**。通过轮回，玩家可以继承部分强大的装备（遗物）和技能，并将本世的功绩转化为永久的天赋点，用于强化家族的整体实力。这个**"变强然后走得更远"**的过程，构成了游戏最主要的成长路径。

---

## 2. 核心玩法详解

# ⚔️ 月球 Yueqiu - 一款基于微信小程序的文字放置类RPG

**项目简介**: 这是一款以家族传承和文明延续为核心的文字放置类RPG。玩家将扮演一个不朽的家族意志，引导无数代的子孙在黑暗的宇宙中求生、发展、战斗，直到文明的尽头。

**当前版本**: v0.3.0
**核心玩法**: **并发危机驱动的策略选择**与**深度角色养成**
**项目状态**: 设计阶段

---

## 1. 更新日志 (Changelog)

- **v0.3.0 (2024-12-21):**
  - **新增功能：世界事件系统**。为所有九个时代设计并实装了由核心参数驱动的动态支线任务（世界事件），极大地增强了世界的互动性和重玩价值。
  - **文档重构**：清理并重构了混乱的 `README.md` 文件，统一了更新日志格式，并重新组织了核心玩法说明，使其更具逻辑性和可读性。

- **v0.2.1 (2024-12-20):**
  - **关键优化：参数效果与条件触发**。深度分析并改进了参数在游戏中的实际作用，实施了条件选项系统，让参数状态能动态影响玩家的可用选项。

- **v0.2.0 (2024-12-19):**
  - **核心设计：世界观与危机框架**。建立了完整的九个时代世界观和主线危机框架，并定义了核心游戏数据结构。

- **v0.1.0 (2024-12-18):**
  - **项目初始化**：创建了项目的基础目录结构和核心设计文档。

---

## 2. 核心玩法 (Core Gameplay)

本游戏的核心玩法由三个层次构成：**战斗循环**、**时代进程** 和 **轮回成长**，共同打造了一个集深度策略配装与宏大叙事于一体的独特体验。

### 2.1. 战斗核心：放置战斗与战前配装

玩家无法直接操控战斗，游戏的核心策略在于 **战前配装**。

-   **自动战斗**：战斗一旦开始，角色将根据固定的攻击间隔（基础为1.5秒）自动进行一次攻击或技能判定。
-   **策略目标：100%触发率**：游戏最独特的机制在于【装备词条系统】。玩家需要通过精心搭配不同装备的词条，构建出一套能在特定条件下 **100%触发** 各种强大技能的战斗循环（Build）。胜负的关键不在于操作，而在于玩家的配装智慧。

### 📖标准轮回流程

#### 1. 轮回触发
- **触发地点**: 玩家只能在当前所处的关卡进度位置，选择开始轮回。
- **确认仪式**: 确认后将从此地开始新的传承。

#### 2. 族长传承仪式
- **过场动画**: 在当前关卡位置播放传承仪式动画，象征老族长在此地移交族长之位。

#### 3. 角色数据重置
- **等级**: 重置为1级。
- **装备**: 所有装备消失。
- **背包**: 背包内道具消失（遗物除外）。
- **技能**: 轮回后，不仅技能等级重置为1，所有已学会的技能本身也需要被移除。

#### 4. 传承装备入祠
- **选择数量**: 可从当前装备中选择2件传承到"祖祠堂"。
- **使用规则**: 存入祖祠的装备**不消耗使用次数**，在新轮回中可随时从祖祠取用。

#### 5. 获得天赋点奖励
- **奖励范围**: 总计获得5-20点天赋点。
- **显示明细**: 界面会清晰展示本次轮回各项表现所对应的天赋点奖励明细。

#### 6. 副职业重置
- **重新选择**: 副职业等级归零，玩家可重新选择副职业类型。
- **获取加成**: 根据前任家主的副职业等级，获得相应的传承加成。

#### 7. 保留称号和遗物
- **称号**: 称号系统获得的所有称号将**完全保留**。
- **遗物**: 已装备或背包内的遗物将继续保留。

#### 8. 开始回忆试炼：动态生成的宿命剧本
- **核心机制**: 此阶段并非简单的线性闯关，而是一个由玩家过往所有【主线抉择】共同谱写的动态"宿命剧本"。系统会根据家族的千年历史，动态构筑一个独特的"事件池"，所有的遭遇都将源于此。
- **"事件池"构成**:
  - **来源一：历史回响地标 (玩家创造的副本)**: 玩家在各时代【主线抉择】中留下的【历史回响地标】（如"和谈亭"），将作为永久的副本入口出现在世界地图上。在轮回试炼中，玩家可以**主动选择**前往这些地标，挑战由【宿敌烙印】强化的专属敌人，直面自己过去的选择。
  - **来源二：通用随机事件 (世界的变数)**: 事件池中包含了大量通用机遇与挑战（如偶遇商人、遭遇劫匪），其触发受当前家族属性和所处区域影响，为每次轮回增加不可预知的变数。
  - **来源三：宿敌烙印的污染 (历史的复仇)**: 玩家在主线中积累的所有【宿敌烙印】都会"污染"通用事件池，提升特定负面事件的出现概率。例如，若积累了与"强邻入侵"相关的烙印，则在野外遭遇敌方斥候的概率会显著增加。这使得每一次轮回的"随机"体验，都是对家族历史的精准反馈。

#### 9. 回忆结束，回归主线
- **终点**: 到达上任家主选择轮回的地点（例如区域45）。
- **剧情触发**: 触发"接受家族地位"剧情，象征试炼结束。
- **继续旅程**: 玩家正式成为现任家主，从该进度点继续前进，回归**主线世界观**的叙事轨道。

#### 2.1.1. 战斗机制详解：从概率到必然

每一次攻击或技能判定都严格遵循以下流程，这是实现"100%触发率"玩法的技术核心：

1.  **判定开始**：
    -   角色的每一次行动（基础间隔1.5秒，可被"攻击速度"词条缩短）都会触发一次技能释放判定。

2.  **计算总触发概率**：
    -   系统会瞬间计算所有已装备技能的**最终触发概率**之和。
    -   `单个技能最终触发概率 = 技能基础概率 + 技能等级加成 + 装备词条加成 + 其他Buff加成`
    -   `总触发概率 = Σ (所有可用技能的最终触发概率)`

3.  **判定触发方式（核心阈值）**：
    -   **如果 `总触发概率` < 100%**：本次行动将进行一次随机判定。有可能释放一个技能，也有可能只是一次普通的攻击。
    -   **如果 `总触发概率` ≥ 100%**：**本次行动必定会释放一个技能，而不会进行普通攻击。** 这是游戏策略构建的核心目标。

4.  **选择释放的技能**：
    -   在必定触发技能的情况下，系统会根据一套权重体系来决定具体释放哪个技能。
    -   **具体规则**：按技能栏的装配顺序，从左到右依次判定，每个技能的触发概率为其最终概率值。例如，技能A有70%概率，技能B有50%概率，则优先判定技能A，若未触发，再判定技能B。玩家可以通过装备【权重调节类词条】来调整各技能的最终概率，从而实现更精准的技能控制。

5.  **进入技能间隔**：
    -   一个技能被触发后，该技能会立刻进入**"技能间隔期"**（也称"同技能冷却"），在此期间它的基础触发概率降为0%，以防止单一技能被连续滥用。此间隔不影响其他不同技能的触发。
    -   玩家可以通过装备【间隔管理类词条】（如"快速循环"、"破限武道"）来缩短、甚至直接清除这个间隔，这是实现高频、高效输出的关键。

6.  **超越100%的追求**：
    -   当`总触发概率`显著超过100%后，其溢出部分并不会浪费。
    -   通过装备【超越100%类词条】，溢出的概率可以被转化为 **"多重触发"**（一次行动释放多个技能）、**"伤害提升"** 或其他强大效果，这是后期玩家追求的终极目标。

这个清晰、可预测的判定流程，确保了玩家的所有战前配装策略都能在自动战斗中得到精确的执行。

### 2.2. 游戏进程：线性时代与分支任务链

游戏的世界进程是线性的，而时代内部的玩法则充满策略选择。

-   **线性时代演进**：玩家必须完成当前时代所有的 **主线节点**，才能解锁并进入下一个时代。整个万年历史（九大时代）有清晰的先后顺序，构成游戏的主干。
-   **时代内非线性任务**：在每个时代内部，玩家会面临一系列由主线催生的支线任务（并发危机）。玩家可以 **自由选择处理这些任务的顺序**。这个选择将产生连锁反应，直接影响时代内其他任务的难度和结果。
    -   **示例**：在"强邻入侵"时代，若玩家选择先完成"瓦解敌方间谍网"的支线，那么后续"正面战场"主线中的敌人强度就会因此降低。反之，若无视支线，则将面对最高难度的挑战。
-   **参数驱动的动态世界**: 游戏的核心驱动力是**参数系统**。玩家的每一个选择都会影响家族的各项核心参数（如威望、士气、智谋等），而参数的变化又会动态触发不同的"世界事件"，或改变现有危机的难度。这使得世界不再是静态的背景，而是对玩家行为作出实时反馈的有机体。
    -   **设计防火墙**: 为保证核心战斗玩法的纯粹性，参数系统的影响范围被严格限定。它主要通过影响**"战前状态"**（如敌人强度、数量、可用策略选项）和**"宏观战略"**（如资源、人口、剧情走向）来发挥作用，**绝不干涉"战中"的核心战斗机制**（如技能触发率、攻击间隔等）。

### 2.3. 轮回与传承：螺旋式成长

当玩家在某个时代的任务中卡关时，可以选择在当前进度点进行 **轮回**，以更强的姿态重新开始。

-   **数据重置**：
    -   角色等级重置为1级。
    -   背包内道具消失（遗物除外）。
    -   副职业等级归零，可重新选择。
-   **关键传承**：
    -   **祖祠堂**：可从当前装备中选择 **2件** 放入"祖祠堂"，在新轮回中可随时取用，且不消耗使用次数。
    -   **技能**：已学会的技能 **会保留**，但技能等级全部重置为1级。
    -   **称号与遗物**：已获得的所有称号和遗物将 **完全保留**。
    -   **天赋点**：根据上一轮的表现获得5-20点天赋点，用于在【家族天赋树】中解锁永久加成。**评判标准包括角色最终等级、技能总等级、装备图鉴解锁率、通关主线节点数等所有局内核心行为。**
    -   **副职业加成**：根据前任家主的副职业等级，获得相应的传承加成。**具体体现为新任家主对应副职业的初始熟练度提升，公式为：上任家主等级 * 0.5% + 上上任家主等级 * 0.1%。**

通过这种"重置-传承-变强"的循环，玩家将实现螺旋式的成长，最终克服所有挑战。

### 2.4. 深度定制系统：家族的印记
- **族名与族徽**: 玩家可以自定义家族的名称，并通过编辑器组合不同的图腾、纹样和配色，设计出独一无二的家族徽章。
- **属性映射**: 族名和族徽的组合不仅是装饰，更会映射为具体的游戏内加成（如特定技能伤害提升、资源获取效率增加等），让家族的文化符号成为力量的一部分。

---

## 3. 关联文档总览
本项目所有详细设计均沉淀在独立的`markdown`文件中，本文档作为总览和索引。

### 🌍 世界观与剧情系统 (Worldview & Story)
- **[世界观.md](./剧情系统/世界观.md)** - 定义了游戏的完整世界观、九大时代的核心背景和叙事基调。
- **[核心机制与时代总览.md](./剧情系统/核心机制与时代总览.md)** - **【核心必读】** 整合了叙事框架、危机机制、传承系统等游戏最核心的系统规则，提供了官方V2.0轮回历练副本设计模板，并引用了 **[随机事件系统设计.md](./随机事件系统设计.md)** 作为官方事件设计的范式。
- **[参数系统设计.md](./剧情系统/参数系统设计.md)** - 定义了驱动世界演变的各项核心参数及其作用机制。
- **[轮回历练系统设计.md](./剧情系统/轮回历练系统设计.md)** - 补充定义了在"轮回试炼"阶段中，各类随机事件的触发机制、分类与核心原则。
- **[随机事件系统设计.md](./随机事件系统设计.md)** - 提供了游戏内各类随机挑战与机遇的设计范式和具体示例，作为对轮回历练系统的补充和实例化。

#### **分时代主线剧情**
- **[第一时代：奠基.md](./剧情系统/第一时代：奠基.md)**
- **[第二时代：血脉分歧.md](./剧情系统/第二时代：血脉分歧.md)**
- **[第三时代：强邻入侵.md](./剧情系统/第三时代：强邻入侵.md)** - 包含"联盟的价值"和"背水一战"两个主线故事模块
- **[第四时代：武仙启蒙.md](./剧情系统/第四时代：武仙启蒙.md)** - 包含"古仙传承"和"门派风波"两个主线故事模块
- **[第五时代：初次交锋.md](./剧情系统/第五时代：初次交锋.md)**
- **[第六时代：血脉原罪.md](./剧情系统/第六时代：血脉原罪.md)**
- **[第七时代：霸业崛起.md](./剧情系统/第七时代：霸业崛起.md)**
- **[第八时代：域外入侵.md](./剧情系统/第八时代：域外入侵.md)**
- **[第九时代：薪火升华.md](./剧情系统/第九时代：薪火升华.md)**

### 🗺️ 地图与探索系统 (Map & Exploration)
- **[地图系统总览.md](./地图系统/地图系统总览.md)** - 宏观介绍地图系统的设计理念、90个区域的划分原则和战略移动规则。
- **[地图系统设计.md](./地图系统/地图系统设计.md)** - **【核心必读】** 详细定义了地图探索机制、环境交互、关卡节点类型、资源点刷新等具体规则。

#### **分时代地图设计**
- **[第一时代地图：奠基.md](./地图系统/第一时代地图：奠基.md)**
- **[第二时代地图：血脉分歧.md](./地图系统/第二时代地图：血脉分歧.md)**
- **[第三时代地图：强邻入侵.md](./地图系统/第三时代地图：强邻入侵.md)**
- **[第四时代地图：武仙启蒙.md](./地图系统/第四时代地图：武仙启蒙.md)**
- **[第五时代地图：初次交锋.md](./地图系统/第五时代地图：初次交锋.md)**
- **[第六时代地图：血脉原罪.md](./地图系统/第六时代地图：血脉原罪.md)**
- **[第七时代地图：霸业崛起.md](./地图系统/第七时代地图：霸业崛起.md)**
- **[第八时代地图：域外入侵.md](./地图系统/第八时代地图：域外入侵.md)**
- **第九时代地图 (待设计)**

### ⚔️ 战斗与角色系统 (Combat & Character)
- **[技能系统设计.md](./技能系统设计.md)** - 定义了游戏的核心战斗机制，其设计围绕"概率触发"和"1.5秒攻击间隔"两大基石。技能不仅按照世界观分为"武道"、"引气"、"仙道"三大力量体系，其效果更可从以下四个维度进行深度构筑：
    - **纯数值表现**：直接增减攻击、防御、暴击等基础属性。
    - **增强触发概率**：直接提升特定技能或技能类型的触发几率。
    - **强化技能特性**：改变技能的运作方式，如增加攻击目标、延长状态时间或附加额外效果。
    - **引入全新机制**：增加如"生命值越低伤害越高"、"击杀敌人获得永久增益"等复杂的规则，彻底改变战局。
- **[装备与词条系统](./装备系统设计.md)** - 定义了游戏"战前配装定胜负"的核心玩法。词条系统的设计哲学，就是通过上述四大维度，为技能系统提供全方位的支持与魔改，让玩家通过策略搭配，将充满"随机性"的战斗，逐步转变为一个"必然触发"的可控流程。
    - **[装备系统设计.md](./装备系统设计.md)**: 提供了包含6大部位、6种品质和多样化套装的基础装备框架。**同时记录了30件主线故事专属装备**，包含从第三至第七时代的特殊武器、防具、饰品、技能书、配方、材料等珍贵道具，为装备收集系统提供了丰富的内容支撑。
    - **[装备词条系统设计.md](./装备词条系统设计.md)**: 构成了玩法的核心策略深度。它不仅围绕"100%触发率"阈值设计了从"概率操控"到"超越100%"再到"间隔管理"的进阶路径，更通过以下机制提供了丰富的策略组合：
        - **时代特色词条**: 词条体系与九大时代深度绑定，分为前期的"武道"、中期的"引气"和后期的"仙道"三大阶段，确保配装策略随世界进程演进。
        - **多样化构筑**: 支持玩家围绕特定技能体系（如纯武道流、五行流），构筑出效果截然不同的专属战斗流派（Build）。
- **[材料系统设计 (./材料系统设计.md)](./材料系统设计.md)**
  - **核心设计：三级通用，品质为王，配方传承。** 本系统摒弃了繁杂的材料收集，仅保留**三级通用材料**（金属、纤维、宝石），每一级对应一个大的轮回时代。玩家的核心追求不再是寻找无数种材料，而是通过合成、分解和挑战高难度BOSS来**提升现有材料的品质**，因为材料的品质直接决定了制作的成败与装备的优劣。此外，**配方**被设计为一种可以存入"家族藏经阁"的**永久传承资源**，让家族的工艺知识得以跨代积累，构成了与天赋树并行的重要成长线。
- **[家族天赋树系统设计.md](./家族天赋树系统设计.md)** - 定义了玩家**唯一能够跨轮回永久积累**的局外成长体系。其核心设计在于，玩家通过完成轮回和达成特定成就获取天赋点，并将其投入到五条与**"道之根基"**紧密关联的发展路径中，从而为家族定制长期的发展战略：
    - **三大战斗天赋树**: 分别对应"武道"、"引气"、"仙道"，其解锁与玩家在轮回中抉择的"道"直接挂钩，是对先祖道路的直接继承。
    - **工艺传承天赋树**: 专注于提升各项副职业的能力，解锁更强的制造和资源获取能力。
    - **家主威望天赋树**: 是家族所有历史与荣耀的沉淀，专注于强化由"后世之痕"产生的各项传承（如称号、遗物等）。
- **[家主称号与遗物系统设计.md](./家主称号与遗物系统设计.md)** - 定义了记录家族历史和补充装备传承的两大系统：
    - **称号系统**: 基于玩家的战斗数据、副职业成就以及在各种事件抉择中获得的"行为标签"，为每任家主生成一个永久记录其特色的唯一称号。
    - **遗物系统**: 作为祖祠传承的补充，允许将任意装备转化为可跨轮回继承的**"遗物"**，并承载着上一任家主的"战败故事"。
- **[副职业系统设计.md](./副职业系统设计.md)**: 定义了五大非战斗核心玩法。其设计围绕"单代专精"与"三代传承"的核心理念，鼓励玩家在轮回中体验不同的职业乐趣，并通过 **[工艺传承天赋树](./家族天赋树系统设计.md)** 进行永久性的强化。
    - **单代专精**: 每任家主只能选择一种副职业，专注于一个领域。
    - **三代传承**: 若连续三代选择同一职业，可获得熟练度继承奖励，鼓励短期深度发展。
    - **五大职业**: 铁匠（装备）、医师（续航）、猎人（资源）、商人（财富）、毒师（战术），为游戏提供了丰富的策略维度。
- **[族名族徽系统设计.md](./族名族徽系统设计.md)** - 定义了彰显家族身份与传承的标识性系统。它通过一个贯穿所有轮回的**唯一族名**和一个可自定义的**家族徽记**，将历代家主的成就与荣耀归于同一旗帜之下，极大地强化了玩家的身份认同与情感代入。
- **[搁置任务清单.md](./搁置任务清单.md)** - 记录在当前开发阶段被暂时搁置的设计任务、已知问题和待办事项，以便后续追踪和处理。

---

## 4. 技术架构与项目结构

### 4.1. 技术栈
- **平台**: 微信多端框架 (小程序 + Android/iOS)
- **语言**: JavaScript/TypeScript
- **UI框架**: WXML + WXSS + Skyline渲染引擎
- **网络**: 现阶段为纯单机体验，后续规划联网功能。
- **状态管理**: 页面数据绑定 + 全局状态管理

### 4.2. 项目结构
```
Yueqiu2/
├── pages/                  # 页面目录
├── components/             # 组件目录
├── utils/                  # 工具函数
├── assets/                 # 静态资源 (图标等)
├── miniapp/                # 多端平台特定文件
├── i18n/                   # 国际化文件
├── *.js                    # 小程序入口与页面脚本
├── *.json                  # 小程序与项目配置
├── *.wxml                  # 页面结构
├── *.wxss                  # 全局与页面样式
└── *.md                    # 设计文档
```

---
**开发者**: CHX  
**技术栈**: JavaScript + 微信多端框架 + Skyline渲染引擎  
**目标平台**: 微信小程序 + Android/iOS多端应用

