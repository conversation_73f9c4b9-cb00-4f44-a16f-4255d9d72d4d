# 游戏设计总览 (GDD) - 月球 (Yue<PERSON><PERSON>)

**文档版本**: v1.0
**最后更新**: 2024-07-04
**核心理念**: 本文档是整个游戏设计的最高层级索引，旨在提供一个清晰、宏观的视角，帮助所有团队成员理解游戏的核心循环、系统间关系以及设计文档的整体结构。

---

## 1. 核心游戏循环

本游戏采用"战略沙盘"与"轮回历练"双轨并行的模式，其核心循环可以用下图表示：

```mermaid
graph TD
    A[进入<br>轮回历练] --> B{推进关卡<br>战斗/探索};
    B --> C[遭遇挑战<br>战败/瓶颈];
    C --> D[结束历练<br>结算与传承];
    D -- "遗物/天赋/解锁内容" --> E[家族实力增强];
    E -- "开启新战略选项" --> F[返回<br>战略沙盘];
    F --> G{处理危机<br>推动时代剧情};
    G -- "解锁新历练目标" --> A;

    subgraph 永久成长
        D -- "获得<br>传承之物" --> H((遗物/天赋点));
    end

    subgraph 单次挑战
        B -- "获得<br>临时资源" --> I((经验/金币/装备));
    end

    classDef core loop,fill:#f9f,stroke:#333,stroke-width:2px;
    class A,B,C,D,E,F,G core;
```

**循环解读**:
1.  **轮回历练 (Roguelite-like Loop)**: 玩家作为家族的新一代家主，进入线性关卡进行战斗和探索，获取经验、金币和装备等**临时资源**。
2.  **遭遇瓶颈**: 当玩家在历练中达到当前实力的极限（如战败），本次历练结束。
3.  **结算与传承**: 系统会结算本次历练的成果，将部分关键资源转化为**永久成长**的要素，如遗物、天赋点等。
4.  **家族实力增强**: 永久性的传承会提升整个家族的底蕴，这会在战略层面体现。
5.  **战略沙盘 (Grand Strategy Loop)**: 玩家回到宏观视角，利用增强的实力处理并行的危机事件，推动时代剧情的发展。
6.  **开启新循环**: 在战略沙盘上的决策和进展，会为下一次的"轮回历练"解锁新的目标、内容或挑战。

---

## 2. 系统架构与关系

各大核心系统围绕着核心循环紧密协作，其关系如下：

```mermaid
graph TD
    subgraph 核心驱动
        R[轮回与传承系统]
    end

    subgraph 角色与战斗
        C[角色系统]
        B[战斗系统]
        S[技能系统]
    end

    subgraph 资源与成长
        E[装备与材料系统]
        D[掉落系统]
    end
    
    subgraph 舞台与探索
        M[地图与探索系统]
    end

    R -- "定义角色成长边界" --> C;
    R -- "提供永久加成" --> B;
    C -- "定义战斗单位" --> B;
    S -- "提供战斗方式" --> B;
    E -- "提供战斗属性" --> B;
    
    B -- "在...中发生" --> M;
    M -- "通过...产出" --> D;
    D -- "产出" --> E;
    E -- "强化" --> C;

    linkStyle 0,1,2,3,4,5,6,7,8 stroke-width:1.5px,stroke:#666;
```

-   **轮回与传承系统**: 是一切的基石，定义了游戏的长期成长曲线和重玩价值。
-   **角色/战斗/技能系统**: 构成了"轮回历练"中的核心战斗体验。
-   **装备/材料/掉落系统**: 构成了游戏内的经济循环和短期成长反馈。
-   **地图与探索系统**: 提供了战斗和资源产出的舞台。

---

## 3. 设计文档索引

以下是所有核心设计文档的索引，请始终以这些文档中的定义为准。

-   **[系统设计](./01_系统设计/)**
    -   [角色系统.md](./01_系统设计/角色系统.md)
    -   [战斗系统.md](./01_系统设计/战斗系统.md)
    -   [技能系统.md](./01_系统设计/技能系统.md)
    -   [装备与材料系统.md](./01_系统设计/装备与材料系统.md)
    -   [掉落系统.md](./01_系统设计/掉落系统.md)
    -   [地图与探索系统.md](./01_系统设计/地图与探索系统.md)
    -   [轮回与传承系统.md](./01_系统设计/轮回与传承系统.md)
-   **[世界观与剧情](./02_世界观与剧情/)**
    -   (待填充)
-   **[数据与配置](./03_数据与配置/)**
    -   (待填充)
-   **[历史归档](./历史归档/)**
    -   存放所有已废弃或被整合的旧版文档。


