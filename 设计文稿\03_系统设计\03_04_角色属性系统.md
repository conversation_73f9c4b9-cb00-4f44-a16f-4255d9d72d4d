# 角色属性系统设计 v1.0

## 1. 设计理念

本设计旨在为游戏角色构建一套以"力量"、"敏捷"、"体质"为核心的基础属性系统。该系统力求平衡、直观，并为玩家提供有意义的成长选择。部分高级战斗参数将主要通过装备、技能、天赋等方式获得，以增加玩法的多样性和策略深度。

## 2. 核心属性 (Core Attributes)

角色拥有三个核心基础属性，玩家在升级或通过其他方式获得属性点后，可以自由分配到这些属性上。

-   **力量 (Strength)**: 代表角色的肌肉力量和物理爆发力。主要影响角色的物理攻击能力。
-   **敏捷 (Agility)**: 代表角色的身体协调性、速度和反应能力。主要影响角色的攻击频率、命中、闪躲和暴击能力。
-   **体质 (Constitution)**: 代表角色的生命力和耐力。主要影响角色的生存能力。

## 3. 战斗参数 (Combat Parameters)

战斗参数是角色在战斗中各项能力的直接体现，它们由核心属性、装备、技能等多种因素共同决定。

### 3.1 基础战斗参数 (由核心属性主要决定)

| 参数名称 | 英文 | 说明 |
| :--- | :--- | :--- |
| **攻击 (Attack)** | ATK | 角色的基础物理伤害能力。 |
| **生命 (Health)** | HP | 角色的生命值上限。 |
| **防御 (Defense)** | DEF | 角色抵抗物理伤害的能力。 |
| **命中 (Hit Rate)** | HIT | 攻击命中目标的几率。 |
| **闪躲 (Dodge)** | EVA | 躲避敌人攻击的几率。 |
| **暴击 (Critical Strike)** | CRIT | 攻击时造成额外伤害的几率。 |
| **连击 (Combo Attack)** | COMBO | 一次攻击触发多次攻击的几率。 |

### 3.2 高级战斗参数 (由装备、技能等主要决定)

以下参数不受核心属性直接影响，主要通过装备词条、技能效果、天赋、遗物等方式获得，为玩家提供更丰富的构筑（Build）空间。

| 参数名称 | 英文 | 说明 |
| :--- | :--- | :--- |
| **破甲 (Armor Pen)** | PEN | 攻击时无视敌人部分防御力的效果。 |
| **吸血 (Life Steal)** | LIFESTEAL | 将造成的伤害按一定比例转化为自身生命值。 |
| **反击 (Counter Attack)**| COUNTER | 受到攻击后对攻击者进行一次反击的几率。 |
| **格挡 (Block)** | BLOCK | 受到攻击时格挡部分伤害的几率。 |

## 4. 属性转换公式

这是属性系统的核心，定义了每点核心属性如何换算为具体的战斗参数。

### 4.1 力量 (Strength)

-   `1 点力量` =
    -   `+2` **攻击 (Attack)**
    -   `+0.5` **防御 (Defense)** (强壮的体魄也能提供少量防御)

### 4.2 敏捷 (Agility)

-   `1 点敏捷` =
    -   `+0.1%` **暴击率 (Critical Rate)**
    -   `+0.2%` **连击率 (Combo Rate)**
    -   `+0.2%` **闪躲率 (Dodge Rate)**
    -   `+0.1%` **命中率 (Hit Rate)**

### 4.3 体质 (Constitution)

-   `1 点体质` =
    -   `+10` **生命 (Health)**
    -   `+1.5` **防御 (Defense)**

*注意：以上为基础换算比例，游戏后期可通过天赋、技能等进行修正和加成。*

## 5. 战斗相关计算公式（初版）

-   **命中判定**:
    `实际命中率 = (攻击方命中率 - 防御方闪躲率) * (1 - 格挡率)`
    *注：实际命中率有上限和下限，例如 5% ~ 95%。*

-   **伤害计算**:
    `最终伤害 = (攻击方攻击力 - 防御方防御力 * (1 - 破甲率)) * 暴击修正 * 连击修正`
    *注：需要设置最低伤害，如 1。*

## 6. 总结

这套系统将角色的成长与三个直观的核心属性绑定，同时将"破甲"、"吸血"等特效属性解放出来，交由装备和技能系统，鼓励玩家探索不同的战斗流派。例如：
-   **全力战士**: 主加力量，追求极致的面板攻击力。
-   **灵巧游侠**: 主加敏捷，依靠高频率的连击、暴击和闪躲取胜。
-   **坚毅坦克**: 主加体质，作为队伍中坚不可摧的盾牌。
-   **均衡发展**: 根据需求平衡加点，应对不同挑战。

---
**文档版本**: v1.0
**创建日期**: 2024-07-29
**设计者**: AI

## 7. 角色成长与属性点分配

本章节定义了角色从1级到150级的成长路径，核心是属性点的获取与分配。

### 7.1 初始属性

-   **初始等级**: 1级
-   **初始属性点**: 所有角色在1级时，自动拥有 **5点力量、5点敏捷、5点体质** 作为基础属性。
-   **初始可用点数**: 角色在1级时，额外获得 **10个** 可自由分配的属性点，让玩家从一开始就能进行初步的个性化定制。

### 7.2 等级与属性点获取

角色的满级设定为 **150级**。属性点的获取遵循以下规则：

-   **常规升级 (2-100级)**: 在这个阶段，角色每升一级，获得 **5个** 可自由分配的属性点。
-   **后期升级 (101-150级)**: 在这个阶段，角色每升一级，获得 **3个** 可自由分配的属性点。此时玩家的成长将更多地依赖装备、天赋和游戏策略。
-   **里程碑奖励**: 在等级达到 **10, 20, 30, ... , 150** 的倍数时，作为里程碑奖励，角色将 **额外获得10个** 可自由分配的属性点。

### 7.3 总属性点预览

根据以上规则，我们可以计算出角色在不同等级时，通过升级获得的可自由分配的总属性点（不含初始10点）。

| 等级 | 获得方式 | 累计自由属性点 |
| :--- | :--- | :--- |
| **1** | 初始赠予 | **10** |
| **10** | (9级 * 5) + 10里程碑 | 55 + 10 = **65** |
| **50** | (49级 * 5) + (5次里程碑 * 10) | 245 + 50 + 10 = **305** |
| **100** | (99级 * 5) + (10次里程碑 * 10) | 495 + 100 + 10 = **605** |
| **150** | 100级点数 + (50级 * 3) + (5次里程碑 * 10) | 605 + 150 + 50 = **805** |

一个150级满级角色总共将拥有 `10 (初始) + 805 (升级) = 815` 个可自由分配的属性点。

### 7.4 设计思考

-   **前期快速成长**: 在1-100级，每级5点的设计能让玩家在游戏前期明确感受到每次升级带来的提升，快速形成自己的战斗风格。
-   **后期策略深化**: 在101-150级，属性点获取速度放缓，鼓励玩家将注意力从单纯的"刷级加点"转移到更深度的游戏系统，如装备搭配、技能组合和天赋选择，增加了游戏后期的策略性和可玩性。
-   **正向激励**: 里程碑的额外奖励为玩家设立了清晰的短期目标，提供了持续的、可预期的正向反馈，有效提升了玩家的练级动力。

## 8. 角色经验值与等级曲线

本章节定义了角色升级所需的经验值，旨在构建一条平滑且可持续的成长曲线。

### 8.1 经验值计算公式

角色从 `L` 级升到 `L+1` 级所需的经验值（`EXP_required`）由以下公式计算：

**`EXP_required(L) = (Base_EXP + (L * Level_Multiplier)) * (Growth_Factor ^ L)`**

其中：
-   **`L`**: 角色当前等级。
-   **`Base_EXP`**: 基础经验值，决定了游戏初期的升级速度。
    -   设定值: **100**
-   **`Level_Multiplier`**: 等级乘数，一个线性增长的组件，确保每级所需经验值有一个基础的稳定增长。
    -   设定值: **50**
-   **`Growth_Factor`**: 增长因子，一个指数增长的组件，是决定经验曲线斜率的核心参数。它会让后期升级所需经验值显著增加。
    -   设定值: **1.08**

为了便于理解和实现，我们可以将公式简化并取整：

**`所需经验值 = floor((100 + (当前等级 * 50)) * (1.08 ^ 当前等级))`**

*`floor()` 表示向下取整。*

### 8.2 经验值表示例

根据上述公式，我们可以预览一些关键等级升级所需的经验值：

| 当前等级 (L) | 升到下一级所需经验值 |
| :--- | :--- |
| 1 | `floor((100 + 50) * 1.08^1)` = **162** |
| 10 | `floor((100 + 500) * 1.08^10)` ≈ **1,295** |
| 50 | `floor((100 + 2500) * 1.08^50)` ≈ **121,835** |
| 100 | `floor((100 + 5000) * 1.08^100)` ≈ **11,123,064** |
| 149 | `floor((100 + 7450) * 1.08^149)` ≈ **590,920,417** |

*注：这些数值仅为示例，具体的经验值产出（如怪物经验、任务经验）需要与此表相匹配，以控制玩家的平均升级时长。*

### 8.3 设计思考

-   **初期友好**: 较低的 `Base_EXP` 和 `Level_Multiplier` 保证了玩家在游戏初期可以较快地升级，获得正向反馈。
-   **中期平滑过渡**: `Growth_Factor` 的引入使得经验曲线在中期开始变得陡峭，符合游戏内容逐渐深入、挑战逐渐加大的节奏。
-   **后期控制节奏**: 指数级的增长有效延长了游戏后期的升级时间，此时玩家的成长驱动力应更多地来源于装备、成就、社交等系统，而非单纯的等级提升。
-   **易于调整**: 通过修改三个核心参数，开发团队可以在不重构整个系统的情况下，灵活地调整游戏的整体升级节奏，以适应不同阶段的运营需求。 