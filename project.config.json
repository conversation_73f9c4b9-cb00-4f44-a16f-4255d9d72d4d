{"simulatorType": "wechat", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.6.5-38"}, "projectArchitecture": "multiPlatform", "setting": {"condition": true, "es6": false, "postcss": false, "compileWorklet": false, "minified": false, "uglifyFileName": false, "uploadWithSourceMap": true, "enhance": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "disableUseStrict": false, "useCompilerPlugins": false}, "compileType": "miniprogram", "packOptions": {"ignore": [], "include": []}, "appid": "wx53522546f47e9017", "editorSetting": {}}