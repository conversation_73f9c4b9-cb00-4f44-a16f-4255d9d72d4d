# 月球RPG技能系统设计

> 本文件以README为准，后续内容如有冲突以README为最终解释。
> 
> 【玩法模式】本游戏采用并发危机驱动模式，玩家在指挥室中心自由选择危机应对，所有危机并行发展，选择影响后续轮回。
> 
> 【轮回机制】每次轮回，等级、装备、技能、背包等全部重置，仅家族天赋树成长永久保留。技能系统在轮回时全部重置，需重新获取。
> 
> 【数值待定】【经济模型后补】

---

## 目录
- [关联文档](#关联文档)
- [文档内容说明](#文档内容说明)
- [技能系统总览](#-技能系统总览)
  - [设计理念](#-设计理念)
  - [核心参数](#-核心参数)
- [技能触发机制](#-技能触发机制)
  - [概率累计系统](#-概率累计系统)
  - [概率计算公式](#-概率计算公式)
  - [技能间隔机制](#️-技能间隔机制)
- [技能分类体系](#-技能分类体系)
  - [按力量体系分类](#-按力量体系分类)
  - [按效果类型分类](#-按效果类型分类)
- [技能获取与成长](#-技能获取与成长)
  - [技能获取方式](#-技能获取方式)
  - [技能升级机制](#-技能升级机制)
  - [技能熟练度系统](#-技能熟练度系统)
- [技能效果详解](#-技能效果详解)
  - [直接伤害型技能](#1-直接伤害型)
  - [持续效果型技能](#2-持续效果型)
  - [属性克制型技能](#3-属性克制型)
  - [自身强化型技能](#4-自身强化型)
  - [特殊机制型技能](#5-特殊机制型)
- [平衡性与扩展性](#-平衡性与扩展性)
  - [数值平衡策略](#-数值平衡策略)
  - [未来扩展方向](#-未来扩展方向)
- [技术实现方案](#-技术实现方案)
  - [数据结构设计](#-数据结构设计)
  - [核心算法实现](#-核心算法实现)
  - [UI/UX设计](#-uiux设计)

---

## 📜 目录

## 📁 关联文档
- **[世界观](./剧情系统/世界观.md)** - 武道→引气→仙道三阶段的世界观基础设定
- **[装备系统设计](./装备系统设计.md)** - 装备属性如何转换为技能数值，攻击间隔机制
- **[装备词条系统设计](./装备词条系统设计.md)** - 词条如何影响技能触发率和技能选择权重
- **[家族天赋树系统设计](./家族天赋树系统设计.md)** - 三个技能类型对应的天赋树加成
- **[地图系统设计](./地图系统/地图系统设计.md)** - 轮回机制对技能重置的影响
- **[轮回历练系统设计](./剧情系统/轮回历练系统设计.md)** - 随机事件对技能触发率和效果的临时影响
- **[核心机制与时代总览](./剧情系统/核心机制与时代总览.md)** - 关键抉择对特定技能的解锁或强化

## 📝 文档内容说明
本文档定义了游戏的核心战斗机制，包括1.5秒攻击间隔、概率触发技能系统、技能间隔限制、100%触发率阈值机制等。技能分为武道、引气、仙道三大类型，体现世界观的时代变迁。包含技能获得方式、升级机制、熟练度系统等完整设计。

---

# 月球(Yueqiu) - 技能系统设计文档

## 📍 文档概述
本文档详细描述月球RPG游戏的技能系统设计，包括技能分类、触发机制、升级系统、效果设计、平衡性考虑以及技术实现方案。

---

## ⚔️ 技能系统总览

### 🎯 设计理念
技能系统采用"概率触发 + 防重复限制"的机制，让玩家能够构建多样化的战斗流派。通过技能升级、装备词条和套装效果的组合，玩家可以达成从"概率流"到"100%技能流"的不同构建方向。

### 📊 核心参数
- **技能携带上限**: 20个技能
- **触发机制**: 累计概率触发，防重复触发限制
- **升级上限**: 每个技能最高10级
- **效果持续**: 根据技能类型而定（瞬间/持续/永久）
- **熟练度系统**: 每个技能独立1-10级熟练度，通过使用获得

---

## 🎲 技能触发机制

### 🔮 概率累计系统
```
每次攻击时：
1. 计算所有技能的总触发概率
2. 如果总概率 ≥ 100%，必定触发一个技能（不执行普攻）
3. 如果总概率 < 100%，按概率随机触发技能
4. 触发的技能进入防重复触发状态
5. 同一时刻只能触发一个技能
```

### 📈 概率计算公式
```
单个技能最终触发概率 = 基础概率 + 技能等级加成 + 装备词条加成 + 套装效果加成

总触发概率 = Σ(所有可用技能的最终触发概率)
```

### ⏱️ 技能间隔机制

#### 🎯 机制设计目的
- **避免技能滥用**: 防止同一技能在短时间内重复触发
- **保持攻击节奏**: 确保技能触发有合理的时间间隔
- **维持游戏平衡**: 避免高触发率技能造成的不平衡

#### ⏱️ 技能间隔规则
```
基础重复触发概率 = 0%（需要间隔至少1个攻击间隔）

技能间隔加成后概率 = 基础概率 + 技能间隔加成%

当前攻击间隔 = 1.5秒 / (1 + 攻击速度加成)

设计逻辑：
- 技能触发后，默认需要间隔至少1个攻击间隔才能再次触发
- 技能间隔词条可以增加在限制期内重复触发该技能的概率
- 技能间隔加成最多提升50%的重复触发概率
- 这让玩家可以构建专精某个技能的流派
```

#### 🔄 触发流程
```
1. 技能成功触发执行效果
2. 该技能进入重复触发限制期
3. 限制期内该技能基础触发概率为0%
4. 但技能间隔加成可以让该技能在限制期内重新获得触发机会
5. 限制期结束后，技能恢复正常触发概率
6. 特殊效果可清除限制（如"无限循环"、"天道感应"）
```

#### ⚔️ 技能间隔效果示例
```
基础攻击间隔：1.5秒
实际攻击间隔：1.5秒 / (1 + 攻击速度加成)
技能间隔限制：1个攻击间隔

设计示例（无技能间隔加成）：
- 攻击频率每秒1次（1秒间隔）
- 第1秒：触发技能A（进入间隔期）
- 第2秒：技能A重复触发概率0%，触发其他技能
- 第3秒：技能A重新完全可用

设计示例（50%技能间隔加成）：
- 第1秒：触发技能A（进入间隔期）
- 第2秒：技能A有50%概率重复触发，50%概率触发其他技能
- 第3秒：技能A重新完全可用

设计价值：
- 让玩家可以构建专精特定技能的流派
- 保持技能系统的策略深度
- 简单、直观的概率机制
```

---

## 📚 技能分类体系

### 🌟 按力量体系分类

#### 武道技能 (1-3轮回主要掉落，占技能池70%)
- **触发概率**: 2%-6%，平均4%
- **获得方式**: 怪物掉落、离线收益
- **升级材料**: 相同技能书
- **特点**: 朴实有效的武学技法，构成战斗基础
- **时代背景**: 家族衰落期的传统武道传承

#### 引气技能 (4-6轮回主要掉落，占技能池25%)
- **触发概率**: 1%-4%，平均2.5%
- **获得方式**: 妖兽掉落、离线收益(较低概率)
- **升级材料**: 相同技能书
- **特点**: 融合天地灵气的修炼之法，效果奇特
- **时代背景**: 天地复苏期的新兴修炼体系

#### 仙道技能 (7-9轮回稀有掉落，占技能池5%)
- **触发概率**: 0.5%-2%，平均1.25%
- **获得方式**: 妖王Boss掉落、离线收益(极低概率)
- **升级材料**: 相同技能书
- **特点**: 威力巨大的仙家法门，极其稀有珍贵
- **时代背景**: 修仙鼎盛期的高阶传承

### 🎯 按效果类型分类

#### 💡 借鉴《冒险与挖矿》的创新机制分类
**新增机制类型**:
- **逆境反转型**: 在不利条件下获得更强效果
- **百分比机制型**: 基于百分比计算的固定比例效果
- **纯粹效果型**: 无视任何抵抗的绝对效果
- **循环增强型**: 通过特定行为触发的累积增强
- **因果律级型**: 高风险高回报的平衡机制

#### 1. 直接伤害型
**设计理念**: 瞬间爆发伤害，适配卡片对战机制

**武道技能示例**:
- **破甲刀法** (力量主导型): 
  - 伤害: [基础攻击力 × (1 + 力量/100)] × 150%-300% × 力量权重70%
  - 特效: 无视敌人防御 = 15% + (力量/200)%
- **疾风连斩** (敏捷主导型):
  - 伤害: [基础攻击力 × (1 + 敏捷/150)] × 120%-250% × 敏捷权重60%  
  - 特效: 二次攻击概率 = 基础概率 + (敏捷/300)%
- **重锤破击** (力量+体质型):
  - 伤害: [基础攻击力 × (1 + 力量/100 + 体质/200)] × 180%-350%
  - 特效: 防御降低 = 基础值 + (力量+体质)/400

**引气技能示例**:
- **真气爆发** (体质主导型):
  - 伤害: [基础攻击力 × (1 + 体质/120)] × 250%-500% × 体质权重60%
  - 特效: 震慑时长 = 基础时长 + (体质/200)秒
- **焚心掌** (体质+力量型):
  - 伤害: [基础攻击力 × (1 + 体质/120 + 力量/200)] × 300%-600%
  - 特效: 灼烧伤害/回合 = (体质×0.5)点，持续 = 2+(力量/300)回合
- **寒冰真气** (体质+敏捷型):
  - 伤害: [基础攻击力 × (1 + 体质/120 + 敏捷/200)] × 200%-400%
  - 特效: 行动减缓 = 基础效果 + (体质+敏捷)/400%

**仙道技能示例**:
- **太乙真火** (三属性平衡型):
  - 伤害: [基础攻击力 × (1 + 三属性平均/80)] × 500%-1000%
  - 特效: 无视防御，真实伤害，额外伤害 = (力量+体质+敏捷)/150
- **九天玄雷** (最高属性型):
  - 伤害: [基础攻击力 × (1 + 最高属性/60)] × 400%-800% × 敌人数量
  - 特效: 链式攻击数 = 1 + (最高属性/250)个
- **万剑归宗** (属性总和型):
  - 伤害: [基础攻击力 × (1 + 属性总和/300)] × 600%-1200%
  - 特效: 每剑伤害 = 基础伤害 + (属性总和/100)，剑数 = 10 + (属性总和/500)

#### 2. 持续效果型
**设计理念**: 通过DOT或状态效果提供持续价值

**武道技能示例**:
- **淬毒刀法**: 使敌人中毒，每回合损失10%-20%攻击力的生命值，持续3回合
- **血战之法**: 攻击时恢复造成伤害10%-20%的生命值，持续战斗期间
- **怒意积蓄**: 攻击力每回合提升10%，最高叠加5层

**引气技能示例**:
- **蚀骨寒毒**: 使敌人中寒毒，每回合损失20%-40%攻击力的生命值，持续4回合
- **吸星大法**: 击败敌人时恢复30%-60%最大生命值，并获得临时属性加成
- **狂化血气**: 攻击力提升40%-80%，但每回合损失5%最大生命值，持续战斗

**仙道技能示例**:
- **业火红莲**: 使敌人受到业火诅咒，每回合损失当前生命值的8%-15%，持续5回合
- **不死金身**: 免疫所有伤害并且每回合恢复25%最大生命值，持续2回合
- **血脉觉醒**: 每次击败敌人，攻击力永久提升5%，防御力永久提升3%(当前战斗)

#### 3. 属性克制型
**设计理念**: 利用五行相克和特殊状态提供战术价值

**武道技能** (基础属性):
- **金刚劲**: 提升物理伤害，对轻甲敌人额外伤害+30%
- **柔劲化解**: 降低敌人物理攻击力20%，持续3回合
- **气血充盈**: 提升生命恢复效果50%，持续整场战斗

**引气技能** (五行属性):
- **烈火真气**: 火属性伤害，对木属性敌人额外伤害+50%，附加灼烧
- **寒冰内力**: 水属性伤害，对火属性敌人额外伤害+50%，附加冰冻
- **青木生机**: 木属性治疗，对土属性敌人造成额外毒素伤害

**仙道技能** (天地法则):
- **五雷正法**: 天雷属性，对邪恶单位额外伤害+100%，净化负面状态
- **太阴真水**: 至阴之水，对至阳单位造成真实伤害，无视防御
- **太阳真火**: 至阳之火，对至阴单位造成净化伤害，驱散一切负面效果

#### 4. 自身强化型
**设计理念**: 提升角色能力，强化后续战斗

**武道技能示例**:
- **力拔山兮**: 提升攻击力30%-60%，持续整场战斗
- **身轻如燕**: 提升闪避率25%-50%，持续整场战斗  
- **铁布衫**: 提升防御力40%-80%，减少受到伤害

**引气技能示例**:
- **真气护体**: 提升所有属性20%-40%，并获得伤害吸收护盾
- **内息调和**: 每回合恢复生命值，并净化一个负面状态
- **气血共鸣**: 攻击力和生命值互相影响，生命越高攻击越强

**仙道技能示例**:
- **仙体护持**: 所有属性提升80%，技能触发率提升30%，持续整场战斗
- **涅槃重生**: 死亡时重新复活并恢复50%生命，且获得无敌状态1回合
- **道心通明**: 免疫所有负面状态，技能必定触发，持续3回合

#### 5. 敌方削弱型
**设计理念**: 削弱敌人，创造战斗优势

**武道技能示例**:
- **断筋错骨**: 降低敌人攻击力30%-50%，持续3回合
- **封穴截脉**: 使敌人无法回复生命值，持续4回合
- **破甲手法**: 降低敌人防御力40%-70%，持续整场战斗

**引气技能示例**:
- **摄魂夺魄**: 使敌人有50%概率无法行动，持续2回合
- **吸功大法**: 吸取敌人20%-40%的属性值转化为自身加成，持续整场战斗
- **封印真气**: 禁止敌人使用技能，持续3回合

**仙道技能示例**:
- **定身法**: 使敌人完全无法行动，持续1回合(无法被净化)
- **生机剥夺**: 每回合吸取敌人15%-25%当前生命值，持续3回合  
- **封印仙术**: 使敌人所有属性降低60%，无法使用技能，持续2回合

#### 6. 特殊机制型 (优化版)
**设计理念**: 借鉴《冒险与挖矿》的逆境反转、百分比机制、纯粹效果等创新设计，打造独特的战术机制

**武道技能示例** (增强版):
- **后发制人**: 敌人攻击时，立即进行反击，造成150%-300%伤害
- **以战养战**: 每击败一个敌人，下次攻击伤害+50%，可叠加至500%
- **破釜沉舟** (借鉴"背水"): 生命值越低攻击力越高，≤30%时攻击力翻倍，≤10%时攻击力三倍
- **百分比斩击** (借鉴): 造成敌人当前生命值的15%-25%真实伤害，无视防御
- **纯粹武意** (借鉴"纯粹"): 100%命中，无视敌人所有防御、闪避和伤害减免
- **势如破竹**: 连续击败敌人时，每次击败使下个敌人受到的伤害+100%，最多叠加5层

**引气技能示例** (增强版):
- **因果轮回**: 受到伤害时，将50%-80%伤害返还给攻击者
- **先机洞察**: 每场战斗前3次攻击必定暴击，暴击伤害递增(200%/250%/300%)
- **真气爆种**: 消耗30%当前生命值，下次技能必定触发且效果翻倍
- **逆转乾坤** (借鉴逆境反转): 当前生命值≤50%时，所有内力技能伤害按缺失生命值百分比增加
- **百分比吸取**: 攻击时恢复造成伤害20%-35%的生命值，同时偷取敌人5%-10%最大生命值
- **五行循环**: 每次释放不同五行技能，下个五行技能伤害+50%，形成循环增强

**仙道技能示例** (增强版):
- **天人感应**: 根据敌人数量调整自身能力，每多1个敌人全属性+25%
- **劫数转移**: 将自身承受的负面状态转移给随机敌人，并造成转移效果2倍的伤害
- **道法自然**: 战斗期间，技能触发率随回合数递增(每回合+10%)，最高+100%
- **虚空湮灭** (借鉴"纯粹"概念): 直接移除敌人30%-50%当前生命值，无法被任何方式抵挡
- **无限循环**: 击败敌人时，有30%-50%概率清除所有技能的重复触发限制
- **因果律武器**: 下次攻击的伤害等于目标最大生命值的20%-40%，但自身也受到相同数值的伤害

---

## 📊 技能数值平衡

### 🎯 基础数值设计原则

#### 触发概率平衡
```
技能总价值 = 效果强度 × 触发概率 × 持续时间

基础技能: 中等效果 × 高概率 = 稳定输出
稀有技能: 高效果 × 中概率 = 爆发输出  
传说技能: 超高效果 × 低概率 = 极限输出
```

#### 多元化伤害计算设计
```
技能伤害 = 基础伤害类型 × 技能倍率 × 属性权重加成

武道技能 (100%-350%基础倍率):
- 物理类: 基础攻击力 × (1 + 力量/100) × 技能倍率
- 技巧类: 基础攻击力 × (1 + 敏捷/150) × 技能倍率  
- 防御类: 基础攻击力 × (1 + 体质/120) × 技能倍率

引气技能 (200%-600%基础倍率):
- 内力类: 基础攻击力 × (1 + 体质/120) × 技能倍率
- 五行类: 基础攻击力 × (1 + 主属性/100 + 副属性/200) × 技能倍率
- 复合类: 综合多属性计算，权重依技能特性而定

仙道技能 (400%-1200%基础倍率):
- 仙法类: 基础攻击力 × (1 + 三属性平均/80) × 技能倍率
- 法则类: 基础攻击力 × (1 + 最高属性/60) × 技能倍率
- 超越类: 基础攻击力 × (1 + 属性总和/300) × 技能倍率

属性权重影响:
- 单属性依赖: 该属性每100点提供额外100%伤害
- 双属性组合: 主属性70%权重 + 副属性30%权重
- 三属性平均: 每个属性平均权重，更加稳定
```

### 📈 技能升级系统

#### 升级效果
- **1-5级**: 每级提升0.5%触发概率 + 10%效果强度
- **6-10级**: 每级提升0.5%触发概率 + 15%效果强度
- **满级奖励**: 额外特殊效果或显著数值提升

#### 升级材料需求
```
技能升级所需材料:
1级→2级: 1本相同技能书
2级→3级: 2本相同技能书
3级→4级: 3本相同技能书
...
9级→10级: 9本相同技能书

总计需要: 45本相同技能书达到满级
```

### 🔮 概率提升途径

#### 1. 技能等级提升
- **基础**: 每级+0.5%触发概率
- **满级总计**: +5%触发概率

#### 2. 装备词条
- **技能触发率**: +1%-15%（根据装备品质）
- **特定技能强化**: 某类技能+2%-20%触发率
- **技能伤害**: +10%-50%技能效果

#### 3. 套装效果
- **血战套装**: 2件套+5%，4件套+10%，6件套+15%技能触发率
- **五行套装**: 引气技能触发率+20%，五行技能伤害+30%
- **疾风套装**: 武道技能触发率+25%，连击伤害+40%

#### 4. 轮回传承加成
- **传承点数**: 可用于提升特定技能触发率
- **血脉觉醒**: 解锁技能触发率上限突破

---

## 🎮 技能流派构建

### 💯 100%技能流构建
**目标**: 达到100%+技能触发率，每次攻击必定触发技能

**构建路径**:
```
基础配置 (20个满级基础技能): 20 × (4% + 5%) = 180%基础触发率

但考虑到技能重复触发限制机制，实际需要:
- 10个核心技能达到满级: 10 × 9% = 90%
- 血战套装6件套: +15%
- 高品质装备词条: +20%-30%
- 总计: 125%-135%触发率
```

**优势**:
- 稳定的技能输出
- 不依赖普通攻击
- 技能链条流畅

**劣势**:
- 需要大量资源投入
- 技能书收集困难
- 装备要求极高

### ⚡ 爆发流构建
**目标**: 通过少数高伤害技能实现极限爆发

**构建路径**:
```
核心配置:
- 3-5个传说技能满级: 5 × (1.25% + 5%) = 31.25%
- 专精装备词条: +50%传说技能触发率
- 专精套装: +25%传说技能伤害
- 总计: 约30-40%触发率，但单次伤害极高
```

**优势**:
- 单次伤害极高
- 对装备要求相对较低
- 容易获得成就感

**劣势**:
- 输出不稳定
- 依赖运气
- 传说技能书稀有

### 🌊 持续流构建
**目标**: 通过持续效果技能实现持续输出和控制

**构建路径**:
```
核心配置:
- 15个持续效果技能: DOT、增益、减益组合
- 持续效果延长装备词条: +50%持续时间
- 效果强度提升: +40%持续效果伤害
- 总计: 中等触发率，但效果持续时间长
```

**优势**:
- 输出持续稳定
- 提供战术价值
- 适合长期战斗

**劣势**:
- 瞬间爆发不足
- 对短期战斗不利
- 需要精确计算时机

### 🎯 平衡流构建
**目标**: 各类技能均衡搭配，适应各种战斗情况

**构建路径**:
```
核心配置:
- 8个直接伤害技能 (40%触发率)
- 6个持续效果技能 (25%触发率)
- 4个增益技能 (20%触发率)
- 2个特殊效果技能 (10%触发率)
- 总计: 95%触发率，效果多样化
```

**优势**:
- 适应性强
- 战术灵活
- 容错率高

**劣势**:
- 没有特别突出的优势
- 需要更多的技能书投入

---

## 🔧 技术实现设计

### 📊 数据结构设计

```kotlin
// 技能数据类
data class Skill(
    val id: Int,                    // 技能ID
    val name: String,               // 技能名称
    val description: String,        // 技能描述
    val rarity: SkillRarity,        // 稀有度
    val type: SkillType,            // 技能类型
    val element: ElementType?,      // 元素类型（可为null）
    val baseTriggerRate: Float,     // 基础触发概率
    val baseEffect: SkillEffect,    // 基础效果
    val levelScaling: LevelScaling, // 升级缩放
    val maxLevel: Int = 10          // 最大等级
)

// 技能效果数据类
data class SkillEffect(
    val damageMultiplier: Float,    // 伤害倍率
    val duration: Long,             // 持续时间（毫秒）
    val statusEffects: List<StatusEffect>, // 状态效果
    val specialEffects: List<SpecialEffect> // 特殊效果
)

// 技能实例（玩家拥有的技能）
data class PlayerSkill(
    val skillId: Int,               // 技能ID
    val level: Int,                 // 当前等级
    val experience: Int,            // 升级经验
    val lastTriggerTime: Long,      // 上次触发时间
    val isEquipped: Boolean         // 是否装备
)

// 技能触发结果
data class SkillTriggerResult(
    val skill: Skill,               // 触发的技能
    val actualDamage: Float,        // 实际伤害
    val statusEffects: List<StatusEffect>, // 应用的状态效果
    val triggerTime: Long           // 触发时间
)
```

### 🎲 触发系统实现

```kotlin
class SkillTriggerSystem {
    
    fun calculateTriggerRates(
        equippedSkills: List<PlayerSkill>,
        equipment: List<Equipment>,
        setEffects: List<SetEffect>
    ): Map<Int, Float> {
        val triggerRates = mutableMapOf<Int, Float>()
        
        equippedSkills.forEach { playerSkill ->
            val skill = SkillDatabase.getSkill(playerSkill.skillId)
            
            // 基础概率 + 等级加成
            var rate = skill.baseTriggerRate + (playerSkill.level * 0.5f)
            
            // 装备词条加成
            rate += calculateEquipmentBonus(skill, equipment)
            
            // 套装效果加成
            rate += calculateSetBonus(skill, setEffects)
            
            // 传承加成
            rate += calculateInheritanceBonus(skill)
            
            triggerRates[skill.id] = rate
        }
        
        return triggerRates
    }
    
    // 多元化伤害计算方法
    fun calculateSkillDamage(
        skill: Skill,
        playerStats: PlayerStats,
        baseAttackPower: Int,
        skillLevel: Int
    ): Float {
        // 根据技能类型确定基础伤害类型
        val baseDamage = when (skill.damageType) {
            DamageType.PHYSICAL -> baseAttackPower * (1 + playerStats.strength / 100f)
            DamageType.TECHNIQUE -> baseAttackPower * (1 + playerStats.agility / 150f)
            DamageType.INTERNAL -> baseAttackPower * (1 + playerStats.constitution / 120f)
            DamageType.ELEMENTAL -> calculateElementalDamage(skill, playerStats, baseAttackPower)
            DamageType.IMMORTAL -> calculateImmortalDamage(skill, playerStats, baseAttackPower)
        }
        
        // 技能倍率 (基础倍率 + 等级提升)
        val skillMultiplier = skill.baseDamageMultiplier + (skillLevel - 1) * skill.levelScaling
        
        // 属性权重加成
        val attributeBonus = calculateAttributeBonus(skill, playerStats)
        
        return baseDamage * skillMultiplier * attributeBonus
    }
    
    private fun calculateElementalDamage(
        skill: Skill, 
        playerStats: PlayerStats, 
        baseAttackPower: Int
    ): Float {
        return when (skill.elementType) {
            ElementType.FIRE -> baseAttackPower * (1 + playerStats.constitution/120f + playerStats.strength/200f)
            ElementType.WATER -> baseAttackPower * (1 + playerStats.constitution/120f + playerStats.agility/200f)
            ElementType.EARTH -> baseAttackPower * (1 + playerStats.constitution/120f + playerStats.strength/150f)
            ElementType.METAL -> baseAttackPower * (1 + playerStats.strength/100f + playerStats.constitution/180f)
            ElementType.WOOD -> baseAttackPower * (1 + playerStats.agility/120f + playerStats.constitution/180f)
            else -> baseAttackPower * (1 + playerStats.constitution/120f)
        }
    }
    
    private fun calculateImmortalDamage(
        skill: Skill, 
        playerStats: PlayerStats, 
        baseAttackPower: Int
    ): Float {
        return when (skill.immortalType) {
            ImmortalType.BALANCED -> {
                val avgAttribute = (playerStats.strength + playerStats.agility + playerStats.constitution) / 3f
                baseAttackPower * (1 + avgAttribute / 80f)
            }
            ImmortalType.DOMINANT -> {
                val maxAttribute = maxOf(playerStats.strength, playerStats.agility, playerStats.constitution)
                baseAttackPower * (1 + maxAttribute / 60f)
            }
            ImmortalType.TRANSCENDENT -> {
                val totalAttributes = playerStats.strength + playerStats.agility + playerStats.constitution
                baseAttackPower * (1 + totalAttributes / 300f)
            }
            else -> baseAttackPower * (1 + playerStats.constitution/120f)
        }
    }
    
    private fun calculateAttributeBonus(skill: Skill, playerStats: PlayerStats): Float {
        return when (skill.rarity) {
            SkillRarity.MARTIAL -> {
                when (skill.martialType) {
                    MartialType.HEAVY_STRIKE -> 0.7f * (playerStats.strength/100f) + 0.3f * (playerStats.agility/150f) + 1f
                    MartialType.COMBO -> 0.6f * (playerStats.agility/100f) + 0.4f * (playerStats.strength/150f) + 1f
                    MartialType.DEFENSE -> 0.8f * (playerStats.constitution/100f) + 0.2f * (playerStats.strength/150f) + 1f
                    else -> 1f
                }
            }
            SkillRarity.QI_CULTIVATION -> {
                // 引气技能的属性加成
                1f + (playerStats.constitution / 120f) * 0.6f + (playerStats.strength / 180f) * 0.4f
            }
            SkillRarity.IMMORTAL -> {
                // 仙道技能的属性加成
                val avgAttribute = (playerStats.strength + playerStats.agility + playerStats.constitution) / 3f
                1f + (avgAttribute / 80f)
            }
        }
    }
    
    // 新增：特殊机制计算方法
    fun calculateSpecialMechanicDamage(
        skill: Skill,
        playerStats: PlayerStats,
        enemyStats: EnemyStats,
        currentPlayerHP: Int,
        maxPlayerHP: Int,
        baseDamage: Float
    ): Float {
        return when (skill.specialMechanic) {
            SpecialMechanic.ADVERSITY_REVERSAL -> {
                // 逆境反转：生命值越低伤害越高
                val hpRatio = currentPlayerHP.toFloat() / maxPlayerHP
                when {
                    hpRatio <= 0.1f -> baseDamage * 3f  // ≤10%时三倍伤害
                    hpRatio <= 0.3f -> baseDamage * 2f  // ≤30%时双倍伤害
                    hpRatio <= 0.5f -> baseDamage * (2f - hpRatio) // 线性增长
                    else -> baseDamage
                }
            }
            SpecialMechanic.PERCENTAGE_BASED -> {
                // 百分比机制：基于敌人当前生命值计算
                val percentageDamage = enemyStats.currentHP * (skill.percentageRate / 100f)
                maxOf(baseDamage, percentageDamage) // 取较大值
            }
            SpecialMechanic.PURE_EFFECT -> {
                // 纯粹效果：无视所有防御
                val pureDamage = baseDamage * 1.5f // 基础伤害提升50%
                pureDamage // 返回时标记为真实伤害，无视防御
            }
            SpecialMechanic.CYCLE_ENHANCEMENT -> {
                // 循环增强：根据叠加层数增强
                val stackCount = getSkillStackCount(skill.id)
                baseDamage * (1f + stackCount * 0.5f) // 每层+50%伤害
            }
            SpecialMechanic.CAUSALITY_LAW -> {
                // 因果律：高伤害但有反噬
                val causalityDamage = enemyStats.maxHP * (skill.causalityRate / 100f)
                // 同时对自己造成相同伤害（需要在调用处处理）
                causalityDamage
            }
            else -> baseDamage
        }
    }
    
    // 获取技能叠加层数
    private fun getSkillStackCount(skillId: Int): Int {
        return skillStackManager.getStackCount(skillId)
    }
    
    fun triggerSkill(
        triggerRates: Map<Int, Float>,
        currentTime: Long
    ): SkillTriggerResult? {
        // 过滤掉受重复触发限制的技能
        val availableSkills = triggerRates.filter { (skillId, _) ->
            !isInRepeatPreventionPeriod(skillId, currentTime)
        }
        
        val totalRate = availableSkills.values.sum()
        
        // 100%概率必定触发技能
        if (totalRate >= 100f) {
            val selectedSkill = selectSkillByWeight(availableSkills)
            return executeSkill(selectedSkill, currentTime)
        }
        
        // 按概率触发技能
        val randomValue = Random.nextFloat() * 100f
        if (randomValue < totalRate) {
            val selectedSkill = selectSkillByWeight(availableSkills)
            return executeSkill(selectedSkill, currentTime)
        }
        
        // 没有技能触发，执行普通攻击
        return null
    }
    
    private fun executeSkill(skill: Skill, triggerTime: Long): SkillTriggerResult {
        // 设置重复触发限制
        val preventionTime = calculatePreventionTime(skill)
        setRepeatPreventionPeriod(skill.id, triggerTime + preventionTime)
        
        // 计算实际效果
        val actualEffect = calculateActualEffect(skill)
        
        return SkillTriggerResult(
            skill = skill,
            actualDamage = actualEffect.damage,
            statusEffects = actualEffect.statusEffects,
            triggerTime = triggerTime
        )
    }
    
    // 计算防重复触发限制时间
    calculatePreventionTime(skill) {
        // 计算当前攻击间隔
        const baseAttackInterval = 1500; // 1.5秒基础间隔
        const attackSpeedBonus = PlayerStats.getAttackSpeed();
        const currentAttackInterval = Math.floor(baseAttackInterval / (1 + attackSpeedBonus / 100));
        
        // 基础限制时间 = 2倍攻击间隔
        const basePreventionTime = currentAttackInterval * 2;
        
        // 技能间隔可增加限制期内重复触发概率（最多提升50%）
        const stabilityBonus = PlayerEquipment.getTotalSkillStability();
        const reduction = Math.min(stabilityBonus / 100, 0.5); // 最多减少50%
        const finalPreventionTime = Math.floor(basePreventionTime * (1 - reduction));
        
        // 最小限制时间不低于1个攻击间隔
        return Math.max(finalPreventionTime, currentAttackInterval);
    }

}
```

---

## ⚡ 技能熟练度系统

### 🎯 熟练度概述
技能熟练度系统是对技能升级的重要补充，基于"熟能生巧"的理念。每个技能都有独立的熟练度等级（1-10级），通过实际使用技能来提升熟练度，获得额外的技能强化效果。

### 📈 熟练度获取机制

#### 🎯 简化获取规则
```
武道技能：击中敌人 +1熟练度
引气技能：技能触发 +1熟练度
仙道技能：技能释放 +1熟练度

统一规则：
- 所有技能成功使用都获得1点熟练度
- 机制简单明了，易于理解和实现
```

### 📊 熟练度等级体系

#### 🎖️ 熟练度等级表
```
1级 → 2级：50熟练度（新手上路）
2级 → 3级：100熟练度（初有小成）
3级 → 4级：200熟练度（渐入佳境）
4级 → 5级：400熟练度（小有成就）
5级 → 6级：700熟练度（经验丰富）
6级 → 7级：1,200熟练度（炉火纯青）
7级 → 8级：2,000熟练度（登峰造极）
8级 → 9级：3,000熟练度（出神入化）
9级 → 10级：5,000熟练度（返璞归真）

总计：12,650熟练度达到满级
```

#### 🏆 技能称谓
```
1-2级：学徒
3-4级：熟手
5-6级：专家
7-8级：大师
9-10级：宗师

特殊称谓：
- 同时拥有3个大师级技能：技艺精湛
- 同时拥有5个专家级技能：博学多才
- 任一技能达到宗师级：技能宗师
- 所有技能达到专家级：全能专家
```

### ⚔️ 熟练度效果体系

#### 🗡️ 武道技能熟练度效果
```
每级基础效果：
- 伤害提升：+6%（10级时总计+60%）
- 暴击率：+2%（10级时总计+20%）
- 技能间隔：+3%（10级时增加30%重复触发概率）

里程碑效果：
5级解锁：
- 破甲效果：忽视敌人15%防御
- 武器精通：技能消耗-20%

10级解锁：
- 武道真意：5%概率产生"连击"效果，立即再次释放
- 剑心通明：技能必定暴击，暴击伤害+50%
```

#### 🌊 引气技能熟练度效果
```
每级基础效果：
- 触发概率：+3%（10级时总计+30%）
- 技能效果：+5%每级
- 技能范围：+3%每级

里程碑效果：
5级解锁：
- 元素掌控：技能附带对应元素特效
- 内力深厚：技能消耗-25%

10级解锁：
- 引气化神：15%概率同时触发其他引气技能
- 天人合一：技能效果影响范围翻倍
```

#### ⭐ 仙道技能熟练度效果
```
每级基础效果：
- 法术威力：+8%每级
- 法术穿透：+5%每级（无视抗性）
- 施法速度：+4%每级

里程碑效果：
5级解锁：
- 仙法精通：技能无视敌人50%抗性
- 法则理解：技能附带特殊法则效果

10级解锁：
- 超凡入圣：技能威力额外+100%
- 天道感应：5%概率清除所有技能的重复触发限制
```

### 🔄 熟练度与技能等级协同

#### 📈 双重成长机制
```
技能等级系统：
- 提供技能基础威力和解锁条件
- 通过相同技能书升级
- 决定技能的基本数值和效果

熟练度系统：
- 提供技能的精细化提升
- 通过实际使用获得
- 解锁特殊效果和里程碑奖励

协同效果：
- 技能等级×熟练度 = 技能真实威力
- 高等级+高熟练度 = 最强技能形态
- 常用技能自然变得更强
```

### 🎮 熟练度界面设计

#### 📊 显示系统
```
技能图标显示：
- 熟练度等级：技能图标右下角显示1-10数字
- 进度条：技能图标下方显示当前等级进度
- 称谓标识：专家级以上显示特殊光效

详细信息面板：
- 当前熟练度值/下级所需熟练度
- 熟练度加成效果预览
- 下一级解锁的特殊效果
- 历史使用统计（总使用次数、总伤害等）
```

---

### 🏗️ 技能管理系统

```javascript
// 技能管理类
class SkillManager {
    constructor() {
        this.equippedSkills = [];
        this.ownedSkills = new Map();
        this.skillCooldowns = new Map();
        this.MAX_EQUIPPED_SKILLS = 8;
    }
    
    // 装备技能
    equipSkill(skillId) {
        if (this.equippedSkills.length >= this.MAX_EQUIPPED_SKILLS) {
            return false;
        }
        
        const playerSkill = this.ownedSkills.get(skillId);
        if (!playerSkill) {
            return false;
        }
        
        this.equippedSkills.push({ ...playerSkill, isEquipped: true });
        return true;
    }
    
    // 卸下技能
    unequipSkill(skillId) {
        const index = this.equippedSkills.findIndex(skill => skill.skillId === skillId);
        if (index >= 0) {
            this.equippedSkills.splice(index, 1);
            return true;
        }
        return false;
    }
    
    // 升级技能
    upgradeSkill(skillId, skillBooks) {
        const playerSkill = this.ownedSkills.get(skillId);
        if (!playerSkill) {
            return false;
        }
        
        const requiredBooks = this.getRequiredBooksForUpgrade(playerSkill.level);
        
        if (skillBooks.length >= requiredBooks && 
            skillBooks.every(book => book.skillId === skillId)) {
            
            val newLevel = minOf(playerSkill.level + 1, 10)
            ownedSkills[skillId] = playerSkill.copy(level = newLevel)
            
            // 消耗技能书
            consumeSkillBooks(skillBooks.take(requiredBooks))
            
            return true
        }
        
        return false
    }
    
    fun getSkillTriggerRates(): Map<Int, Float> {
        return SkillTriggerSystem().calculateTriggerRates(
            equippedSkills,
            PlayerEquipment.getAllEquipped(),
            PlayerEquipment.getActiveSetEffects()
        )
    }
    
    companion object {
        const val MAX_EQUIPPED_SKILLS = 20
    }
}
```

---

## 📈 平衡性考虑

### ⚖️ 数值平衡原则

#### 1. 投入产出比平衡
```
技能价值评估公式:
DPS贡献 = (基础伤害 × 触发概率 × 频率) + 附加价值

投入成本 = 获得难度 + 升级成本 + 装备需求

平衡目标: 高价值技能 = 高投入成本
```

#### 2. 流派平衡
- **100%技能流**: 高投入，稳定输出，适合长期游戏
- **爆发流**: 中等投入，高风险高回报，适合技术玩家
- **持续流**: 低投入门槛，稳定增长，适合休闲玩家
- **平衡流**: 灵活投入，全面发展，适合探索玩家

#### 3. 进阶曲线设计
```
新手阶段 (1-20级):
- 主要依靠基础技能
- 总触发率 20-40%
- 重点学习技能机制

成长阶段 (21-60级):
- 获得稀有技能
- 总触发率 40-70%
- 开始构建流派

成熟阶段 (61-100级):
- 获得传说技能
- 总触发率 70-100%+
- 完善流派构建

极限阶段 (轮回后):
- 技能流派多样化
- 追求完美构建
- 挑战极限内容
```

---

## 🚀 扩展功能设计

### 🔮 技能进阶系统（后期扩展）

#### 1. 技能觉醒
- **觉醒条件**: 满级技能 + 特殊材料 + 特定成就
- **觉醒效果**: 技能获得额外特殊效果或大幅数值提升
- **觉醒等级**: 3个觉醒等级，每级解锁新效果

#### 2. 技能融合
- **融合机制**: 两个相关技能可以融合成新技能
- **融合条件**: 两个技能都达到满级 + 特殊融合石
- **融合结果**: 获得结合两个技能特点的全新技能

#### 3. 技能符文
- **符文系统**: 可以为技能镶嵌符文提供额外效果
- **符文类型**: 触发率符文、伤害符文、特效符文等
- **符文等级**: 符文也有等级和品质系统

### 🎯 竞技功能（PVP扩展）

#### 1. 技能禁用机制
- **禁用列表**: PVP模式下部分过强技能被禁用
- **平衡调整**: PVP专用的技能数值
- **策略深度**: 玩家需要为PVP准备专门的技能配置

#### 2. 技能预设
- **配置保存**: 玩家可以保存多套技能配置
- **快速切换**: 根据不同场景快速切换技能配置
- **分享功能**: 玩家可以分享自己的技能配置

---

**文档版本**: v1.0  
**创建日期**: 2024年12月19日  
**最后更新**: 2024年12月19日  
**相关文档**: README.md, 地图系统设计.md, 世界观.md  
**维护者**: CHX 

## 7. 轮回与技能

根据游戏核心轮回机制，技能系统在轮回时的处理规则如下：

- **完全重置**: 每次轮回结束时，玩家角色当前已学习的所有技能、技能等级以及技能槽位，都将被完全重置。
- **重新获取**: 在新的轮回开始后，玩家需要通过游戏内的各种方式（如初始携带、战斗掉落、事件奖励等）重新获取技能。
- **无直接继承**: 技能本身不通过任何机制直接继承到下一次轮回，家族的成长主要体现在【家族天赋树】的永久强化上。

### 6.6 技能命名规则
- **格式**: [前缀] + [核心词] + [后缀]
- **示例**: "不屈的**斩击**" "毁灭之**风暴**" "远古的**庇护**"

### 6.7 战斗属性增强技能

作为现有技能体系的补充，这些技能专门强化角色的基础战斗能力。

#### 🎯 战斗精准技能
- **鹰眼术** (武道): 提升命中率+15%，下次攻击必定命中，持续3回合
- **破甲手法** (武道): 本次攻击破甲率+50%，并降低敌人防御力20%
- **致命瞄准** (武道): 暴击率+25%，暴击伤害+50%，持续5回合

#### 🛡️ 战斗反应技能  
- **身法如风** (武道): 闪躲率+30%，成功闪躲后攻击速度+50%，持续3回合
- **格挡反击** (武道): 格挡率+40%，格挡成功时必定触发反击
- **战斗本能** (引气): 连击率+20%，反击率+15%，持续整场战斗

#### ⚡ 生命掌控技能
- **嗜血狂潮** (引气): 吸血率+10%，击杀敌人时额外恢复20%生命值
- **生命共鸣** (仙道): 生命值越高，所有战斗属性越强（最高+50%）

---

## 附录：具体技能列表

本附录提供了游戏中具体可用的技能，按三大力量体系划分，供开发时直接参考。

---
### **🗡️ 武道技能 (Martial Arts Skills)**

#### **主动技能**
1.  **重击 (Heavy Strike)**
    *   **体系**: 武道
    *   **类型**: 主动
    *   **效果**: 对单个目标造成160%力量的物理伤害。
    *   **基础触发率**: 5.0%
    *   **技能间隔**: 2.5秒
    *   **标签**: `单体伤害`
2.  **横扫 (Sweep)**
    *   **体系**: 武道
    *   **类型**: 主动
    *   **效果**: 对前方最多3个目标造成110%力量的物理伤害。
    *   **基础触发率**: 4.5%
    *   **技能间隔**: 3.0秒
    *   **标签**: `范围伤害`
3.  **破甲刺 (Armor Pierce)**
    *   **体系**: 武道
    *   **类型**: 主动
    *   **效果**: 对单个目标造成130%敏捷的物理伤害，并使其物理抗性降低15%，持续5秒。
    *   **基础触发率**: 4.0%
    *   **技能间隔**: 4.0秒
    *   **标签**: `单体伤害`, `减益`
4.  **流血斩 (Bleeding Cut)**
    *   **体系**: 武道
    *   **类型**: 主动
    *   **效果**: 对单个目标造成100%力量的物理伤害，并使其"流血"，在6秒内每秒额外受到20%力量的伤害。
    *   **基础触发率**: 3.5%
    *   **技能间隔**: 5.0秒
    *   **标签**: `单体伤害`, `持续伤害`
5.  **震荡波 (Shockwave)**
    *   **体系**: 武道
    *   **类型**: 主动
    *   **效果**: 对所有敌人造成80%体质的物理伤害，并有20%概率使其"眩晕"1.5秒。
    *   **基础触发率**: 3.0%
    *   **技能间隔**: 6.0秒
    *   **标签**: `范围伤害`, `控制`

#### **被动技能**
6.  **钢铁之躯 (Iron Body)**
    *   **体系**: 武道
    *   **类型**: 被动
    *   **效果**: 永久提升10%的最大生命值和5%的物理抗性。
    *   **基础触发率**: N/A (常驻)
    *   **技能间隔**: N/A
    *   **标签**: `被动`, `属性增益`
7.  **战斗狂热 (Battle Frenzy)**
    *   **体系**: 武道
    *   **类型**: 被动
    *   **效果**: 当你的生命值低于50%时，你的攻击速度提升20%。
    *   **基础触发率**: N/A (条件触发)
    *   **技能间隔**: N/A
    *   **标签**: `被动`, `增益`
8.  **反击姿态 (Counter Stance)**
    *   **体系**: 武道
    *   **类型**: 被动
    *   **效果**: 当你成功格挡一次攻击时，立即对攻击者进行一次反击，造成80%力量的伤害。此效果每2秒最多触发一次。
    *   **基础触发率**: N/A (条件触发)
    *   **技能间隔**: 2.0秒
    *   **标签**: `被动`, `反击`

---
### **🌊 引气技能 (Qi Skills)**

#### **主动技能**
1.  **炎爆术 (Flame Burst)**
    *   **体系**: 引气
    *   **类型**: 主动
    *   **效果**: 对目标及其小范围内的敌人造成200%体质的火焰伤害，并附加"灼烧"效果，持续4秒。
    *   **基础触发率**: 3.0%
    *   **技能间隔**: 4.0秒
    *   **标签**: `范围伤害`, `持续伤害`, `火系`
2.  **寒冰箭 (Frostbolt)**
    *   **体系**: 引气
    *   **类型**: 主动
    *   **效果**: 对单个目标造成180%体质的冰霜伤害，并使其移动和攻击速度降低30%，持续3秒。
    *   **基础触发率**: 3.5%
    *   **技能间隔**: 3.5秒
    *   **标签**: `单体伤害`, `减益`, `控制`, `水系`
3.  **真气护盾 (Qi Shield)**
    *   **体系**: 引气
    *   **类型**: 主动
    *   **效果**: 为自己施加一个可以吸收相当于250%体质伤害的护盾，持续8秒。
    *   **基础触发率**: 2.5%
    *   **技能间隔**: 7.0秒
    *   **标签**: `护盾`, `增益`
4.  **五行乱舞 (Five Elements Dance)**
    *   **体系**: 引气
    *   **类型**: 主动
    *   **效果**: 随机发射5枚法球，每枚对随机目标造成80%体质的伤害，伤害属性随机（金木水火土）。
    *   **基础触发率**: 2.0%
    *   **技能间隔**: 5.0秒
    *   **标签**: `范围伤害`, `多段伤害`
5.  **生命汲取 (Life Siphon)**
    *   **体系**: 引气
    *   **类型**: 主动
    *   **效果**: 吸收单个目标相当于150%体质的生命值，治疗自身。对亡灵单位效果翻倍。
    *   **基础触发率**: 2.8%
    *   **技能间隔**: 6.0秒
    *   **标签**: `单体伤害`, `治疗`

#### **被动技能**
6.  **内力循环 (Internal Cycle)**
    *   **体系**: 引气
    *   **类型**: 被动
    *   **效果**: 每当成功触发一次引气系技能时，恢复最大法力值（若有）的5%，并使所有技能的剩余间隔缩短0.5秒。
    *   **基础触发率**: N/A (条件触发)
    *   **技能间隔**: N/A
    *   **标签**: `被动`, `资源恢复`
7.  **元素亲和 (Elemental Affinity)**
    *   **体系**: 引气
    *   **类型**: 被动
    *   **效果**: 你的所有元素（火、水、土、金、木）伤害提升15%，元素抗性提升10%。
    *   **基础触发率**: N/A (常驻)
    *   **技能间隔**: N/A
    *   **标签**: `被动`, `属性增益`

---
### **⭐ 仙道技能 (Immortal Skills)**

#### **主动技能**
1.  **天罚雷光 (Heaven's Wrath Lightning)**
    *   **体系**: 仙道
    *   **类型**: 主动
    *   **效果**: 召唤一道闪电打击目标，造成400%最高属性的闪电伤害，此伤害无视部分防御和抗性。
    *   **基础触发率**: 1.5%
    *   **技能间隔**: 8.0秒
    *   **标签**: `单体伤害`, `纯粹伤害`
2.  **万剑诀 (Myriad Swords)**
    *   **体系**: 仙道
    *   **类型**: 主动
    *   **效果**: 召唤飞剑攻击场上所有敌人，造成250%属性总和的范围伤害。
    *   **基础触发率**: 1.2%
    *   **技能间隔**: 10.0秒
    *   **标签**: `范围伤害`
3.  **因果逆转 (Karma Reversal)**
    *   **体系**: 仙道
    *   **类型**: 主动
    *   **效果**: 在接下来的5秒内，将受到的所有伤害的60%储存起来，然后在结束时返还给随机一名敌人。
    *   **基础触发率**: 1.0%
    *   **技能间隔**: 15.0秒
    *   **标签**: `反击`, `特殊机制`
4.  **轮回之触 (Touch of Reincarnation)**
    *   **体系**: 仙道
    *   **类型**: 主动
    *   **效果**: 尝试斩杀一个敌人，若其当前生命值低于15%，则立即死亡。若斩杀失败，则对其造成200%最高属性的伤害。对首领敌人斩杀阈值降低至3%。
    *   **基础触发率**: 0.8%
    *   **技能间隔**: 20.0秒
    *   **标签**: `斩杀`, `特殊机制`

#### **被动技能**
5.  **道法自然 (Flow with the Dao)**
    *   **体系**: 仙道
    *   **类型**: 被动
    *   **效果**: 战斗开始后，你所有技能的触发率每10秒提升1%。
    *   **基础触发率**: N/A (常驻)
    *   **技能间隔**: N/A
    *   **标签**: `被动`, `成长`
6.  **涅槃 (Nirvana)**
    *   **体系**: 仙道
    *   **类型**: 被动
    *   **效果**: 当你受到致命伤害时，免疫此次伤害，并恢复50%的最大生命值。此效果每场战斗只能触发一次。
    *   **基础触发率**: N/A (条件触发)
    *   **技能间隔**: N/A
    *   **标签**: `被动`, `复活`, `生存`