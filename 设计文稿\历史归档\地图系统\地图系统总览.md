# 🗺️ 地图系统总览 v2.0 (双模式版)

## 📁 关联文档
- **[世界观.md](../剧情系统/世界观.md)** - 九大时代的世界观背景和历史设定
- **[核心机制与时代总览.md](../剧情系统/核心机制与时代总览.md)** - 定义并发危机系统、核心传承系统与全局状态
- **[装备系统设计.md](../装备系统设计.md)** - 装备掉落机制和传承装备系统
- **[材料系统设计.md](../材料系统设计.md)** - 地图中的材料掉落机制和递减规律
- **[家族天赋树系统设计.md](../家族天赋树系统设计.md)** - 轮回时的天赋点获取机制
- **[家主称号与遗物系统设计.md](../家主称号与遗物系统设计.md)** - 轮回时的称号评定和遗物机制

## 📝 文档内容说明
本文档定义了游戏的**双模式地图系统**总览框架。它整合了两种核心玩法：
1.  **主线叙事模式**: 采用"并发危机"驱动的**战略沙盘**，玩家作为决策者，在非线性的世界地图上应对并行的危机与事件，推动时代发展。
2.  **轮回试炼模式**: 采用**线性关卡推进**结构，玩家作为新任家主，通过重走先祖之路的战斗试炼，以证明自身资格并快速成长。

本文档取代了旧的单一线性地图设计，将原有"区域结构设计"重新定位为"轮回试炼"的核心机制，实现了两种游戏体验的有机结合。

## 📋 时代地图文件列表
- **[第一时代地图：奠基](./第一时代地图：奠基.md)** - 生存求存的基础关卡设计
- **[第二时代地图：血脉分歧](./第二时代地图：血脉分歧.md)** - 内部矛盾与道路选择
- **[第三时代地图：强邻入侵](./第三时代地图：强邻入侵.md)** - 外部战争与防御战略
- **[第四时代地图：武仙启蒙](./第四时代地图：武仙启蒙.md)** - 武学探索与古迹秘密
- **[第五时代地图：初次交锋](./第五时代地图：初次交锋.md)** - 异域文明的首次碰撞
- **[第六时代地图：血脉原罪](./第六时代地图：血脉原罪.md)** - 血脉诅咒的黑暗考验
- **[第七时代地图：霸业崛起](./第七时代地图：霸业崛起.md)** - 帝国征服的宏大战争
- **[第八时代地图：域外入侵](./第八时代地图：域外入侵.md)** - 世界级灾难的绝望抗争
- **[第九时代地图：薪火升华](./第九时代地图：薪火升华.md)** - 超越轮回的终极试炼

---

# 第一部分：主线叙事模式 - 战略沙盘

此模式是游戏剧情推进的核心，适用于玩家在完成"轮回试炼"后，作为现任家主推动历史进程的阶段。

### 🎯 设计理念：从"路径"到"沙盘"
地图的核心功能不再是展示一个线性的、从 A 到 B 的路径，而是作为一个动态的、信息化的 **"世界沙盘"** 或 **"战略地图"**。玩家将以一个固定的"指挥室"或"营地"为中心，俯瞰整个领地，并对同时发生的多个危机事件进行战略决策。

### 🖥️ 核心界面：指挥室与世界地图
游戏的主要场景应该分为两部分：
-   **指挥室/营地**：这是玩家的大本营，是所有行动的起点和终点。玩家在这里进行休整、养成、查看《废土快报》等。
-   **世界地图**：在指挥室内，通过与一张沙盘或墙上的地图交互，进入战略决策界面。这是地图系统的核心。

### 🌍 世界地图元素
世界地图上布满了以下动态的、可交互的图标：

*   **📍 中心营地 (Hub)**
    *   地图的绝对中心，代表玩家的家族。是所有行动的出发点。

*   **🎭 危机事件 (Crisis Event)**
    *   **来源**: 每个时代的主线任务，直接取自时代剧情文档。例如，在第三时代，地图上会同时出现"强邻入侵"、"内部的投降派"、"贸易封锁"等多个危机图标。
    *   **表现形式**:
        *   **独特图标**: 每个危机都应有独特的视觉符号（如"叛徒"用裂开的面具🎭，"入侵"用交叉的战斧⚔️）。
        *   **动态信息**: 悬停或点击时，显示简报，包括：危机名称、一句话描述、以及关联的世界修正值（如"狼王威胁等级：高"）。
        *   **状态变化**: 一个危机被解决后，其图标变为灰色，并显示解决方案摘要（如"已解决：通过火攻重创敌军"）。

*   **📜 世界事件 (World Event)**
    *   **来源**: 由 `world_modifiers` 参数动态触发的支线任务。
    *   **表现形式**: 使用与主线危机不同的图标（如卷轴📜或感叹号❗️），以作区分。部分事件可能带有倒计时。

*   **🏛️ 历史回响 (Historical Echo)**
    *   **来源**: 当玩家在某个时代首次确立"道之根基"（如选择了铁血道），会在地图的特定位置永久生成一个地标。
    *   **表现形式**: 这些是永久性的、不可交互的**地标建筑**。它们是家族历史的见证，新轮回开始时依然存在，并对其绑定的**宿敌**类型在"轮回试炼"中提供永久强度加成。

### 🔄 玩家交互流程
1.  **决策**: 在指挥室查看世界地图，评估各个"危机事件"和"世界事件"的严重性与潜在收益。
2.  **出征**: 选择一个危机图标，确认后进入该危机的专属"关卡"。
3.  **解决**: 在关卡内通过战斗、对话、选择等方式解决危机。
4.  **回归**: 完成任务后，自动返回指挥室。
5.  **反馈**: 系统弹出《废土快报》，总结玩家的决策如何改变了`GlobalState`，以及对其他危机产生的连锁影响。
6.  **演变**: 世界地图上的图标状态、信息、甚至地貌都随之更新，进入下一轮决策。

---

# 第二部分：轮回试炼模式 - 线性试炼

此模式是玩家成长与挑战的核心，适用于玩家选择"轮回"后，作为新任家主重走先祖之路的阶段。

### 🏛️ 试炼核心理念
每次轮回都是"新老族长交替"的神圣仪式。新族长必须通过重走先祖之路的试炼，以证明自己有能力承担家族重任。这不是简单的重复，而是在更高层次上对家族历史的重新体验。

### 📖 试炼触发：修正后的标准轮回流程

#### 1. 轮回触发
- **触发地点**: 玩家只能在当前所处的关卡进度位置，选择开始轮回。
- **确认仪式**: 确认后将从此地开始新的传承。

#### 2. 族长传承仪式
- **过场动画**: 在当前关卡位置播放传承仪式动画，象征老族长在此地移交族长之位。

#### 3. 角色数据重置
- **等级**: 重置为1级。
- **装备**: 所有装备消失。
- **背包**: 背包内道具消失（遗物除外）。
- **技能**: **技能保留但等级全部重置为1级。**

#### 4. 传承装备入祠
- **选择数量**: 可从当前装备中选择2件传承到"祖祠堂"。
- **使用规则**: 存入祖祠的装备**不消耗使用次数**，在新轮回中可随时从祖祠取用。

#### 5. 获得天赋点奖励
- **奖励范围**: 总计获得5-20点天赋点。
- **显示明细**: 界面会清晰展示本次轮回各项表现所对应的天赋点奖励明细。

#### 6. 副职业重置
- **重新选择**: 副职业等级归零，玩家可重新选择副职业类型。
- **获取加成**: 根据前任家主的副职业等级，获得相应的传承加成。

#### 7. 保留称号和遗物
- **称号**: 称号系统获得的所有称号将**完全保留**。
- **遗物**: 已装备或背包内的遗物将继续保留。

#### 8. 开始回忆试炼
- **起点**: 从区域1-1重新开始。
- **目的**: 体验新任家主的成长历程，此阶段为"轮回试炼"阶段，适用高随机性的事件系统。

#### 9. 回忆结束，回归主线
- **终点**: 到达上任家主选择轮回的地点（例如区域45）。
- **剧情触发**: 触发"接受家族地位"剧情，象征试炼结束。
- **继续旅程**: 玩家正式成为现任家主，从该进度点继续前进，回归**主线世界观**的叙事轨道，地图切换为**战略沙盘模式**。

### 🗺️ 试炼结构：线性关卡设计

本模式的核心是线性的关卡推进，由**主线区域**和可选的**支线挑战**构成。

#### 一、 主线区域结构 (Mainline Area Structure)

主线是玩家必须通关才能完成轮回试炼的核心路径。我们采纳了**"渐进式扩展"**的设计理念，主线关卡的体量将随着时代发展而增加，以匹配剧情的史诗感。

*   **主线区域总量**: 90个区域，均匀分布在9个历史时代中，**每个时代包含10个主线区域**。
*   **区域节点定义**: 每个主线区域都是一个独立的"节点"，包含若干"小关卡"和一个最终的"Boss关卡"。
*   **结构详情**:

| 时代 | 区域范围 | 每区域小关卡数量 | 对应剧情阶段与设计思路 |
| :--- | :--- | :--- | :--- |
| 第一时代 | 区域 1-10 | **3-4** | **奠基求存**: 家族初创，挣扎求生。关卡精简，聚焦核心生存挑战。 |
| 第二时代 | 区域 11-20 | **4** | **血脉分歧**: 内部矛盾激化。关卡数略增，体现内部斗争的复杂性。 |
| 第三时代 | 区域 21-30 | **5** | **强邻入侵**: 外部战争爆发。标准化战斗规模，体现正规战争形态。 |
| 第四时代 | 区域 31-40 | **5-6** | **武仙启蒙**: 探索新力量体系。关卡数增加，反映探索与扩张的并进。 |
| 第五时代 | 区域 41-50 | **6** | **初次交锋**: 异文明挑战。引入新敌人、新机制，关卡更长，体现文化碰撞。 |
| 第六时代 | 区域 51-60 | **7** | **血脉原罪**: 内忧外患，生死存亡。关卡数量显著增加，渲染存亡危机的紧迫感。 |
| 第七时代 | 区域 61-70 | **8** | **霸业崛起**: 全面战争，争夺霸权。宏大的战争场面，需要更长的关卡链来表现。 |
| 第八时代 | 区域 71-80 | **9** | **域外入侵**: 世界级灾难。前所未有的危机，关卡规模达到顶峰，体现末日之战的史诗感。|
| 第九时代 | 区域 81-90 | **10**| **薪火升华**: 超越轮回，终极考验。抽象的道心考验，每个关卡都是对意志的磨砺。 |

*   **特殊Boss节点**:
    *   **前代族长分身**: 每10个区域的第5个区域（即区域5, 15, 25...）的Boss会被替换为更强大的**前代族长分身**。
    *   **时代最终Boss**: 每个时代的最后一个区域（即区域10, 20, 30...）的Boss是该时代的**最终守关者**，拥有独特的机制和更高的挑战性。

#### 二、 支线挑战关卡 (Side Quest Stages)

为了丰富游戏世界、提供额外挑战和奖励，我们在主线之外，设计了随时代发展的可选支线关卡。

*   **设计理念**: 支线关卡是**非强制**的，玩家可以自由选择是否挑战。它们提供独特的敌人、环境和更高的奖励，并补充主线未能详述的时代背景故事。
*   **数量递增规则**: 随着时代发展，世界的广度和深度增加，支线挑战数量也随之扩大，体现历史的厚重感。

| 时代 | 支线关卡数量 | 总计 | 设计思路 |
| :--- | :--- | :--- | :--- |
| 第一时代 | **5** 个 | 5 | 基础探索，补充世界观。 |
| 第二时代 | **8** 个 | 13 | 围绕"血脉分歧"主题，提供不同选择的后果。 |
| 第三时代 | **12** 个 | 25 | 体现战争的残酷，如"失落的哨站"、"难民的请求"。 |
| 第四时代 | **15** 个 | 40 | 各大武学门派的额外试炼，或失传秘籍的线索。 |
| 第五时代 | **20** 个 | 60 | 探索西域文明的边角，发现他们的秘密。 |
| 第六时代 | **25** 个 | 85 | 深入调查血脉诅咒的来源，寻找被遗忘的净化之地。 |
| 第七时代 | **30** 个 | 115 | 帝国疆域内的各种逸闻、叛乱和寻宝传说。 |
| 第八时代 | **35** 个 | 150 | 在末日背景下，寻找幸存者、守护避难所的绝望任务。 |
| 第九时代 | **40** 个 | 190 | 探索破碎的法则位面，收集残存的希望火种。 |

*   **总计**: 轮回试炼模式中，除了数百个主线关卡外，还提供了 **190个** 独特的支线挑战关卡。

### 🧮 动态难度："传承之重"系统

#### 核心设计哲学
"传承之重"系统的核心理念与"新任家主的考验"世界观深度融合。对新家主的考验，并非固定的难度，而是与其驾驭家族力量（天赋树）的能力相匹配的**动态资格认证**。家主从天赋树中汲取的力量越多，所背负的"传承"就越重，引来的挑战也越严峻。

#### 难度计算公式
废除旧的固定加成模式，采用新的动态难度公式：
```
试炼敌人强度 = 基础强度 × (1 + 基础试炼强度 + 天赋强度加成)
```
- **基础试炼强度**: 固定值 **+5%**。确保每次轮回都略有挑战，符合"试炼"的基本要求。
- **天赋强度加成**: 动态值，与玩家已花费的总天赋点数挂钩。
  - **计算公式**: `天赋强度加成 = 已花费的总天赋点数 × 0.6%`
  - *（平衡系数0.6%可根据后续测试在0.5%-0.7%间微调）*

#### "历史回响"宿敌加成
除了上述的通用强度加成外，特定的"历史回响"还会对其绑定的"宿敌"类型施加一个**独立、永久、固定**的额外强度乘数。

- **触发条件**：当关卡中出现与已解锁的"历史回响"相关联的"宿敌"类型时触发。
- **计算方式**：此加成为独立乘区，在"传承之重"系统计算完毕后生效。
- **示例公式**：
  ```
  // 仅对"宿敌"生效
  宿敌最终强度 = (试炼敌人强度) × (1 + 宿敌加成)
  
  // 示例：若已解锁【叛徒之冢】，宿敌加成为15%
  叛徒最终强度 = (试炼敌人强度) × (1 + 0.15)
  ```

#### 成长加速机制
此部分作为对玩家接受更高挑战的奖励。
```
经验倍率 = 1 + (轮回次数 × 0.5)
金币倍率 = 1 + (轮回次数 × 0.3) 
装备掉落率 = 基础掉落率 × (1 + 轮回次数 × 0.2)
```

### 🥋 特殊挑战：前代族长分身试炼
每5个区域（区域5、15、25...85）出现的特殊关卡，代表前代族长留下的智慧传承。

#### 分身试炼特点
- **数据来源**: 基于随机玩家的角色数据生成
- **智慧传承**: 每个分身代表特定时代的族长智慧
- **适应性强度**: 分身实力会根据当前试炼时代调整
- **传承奖励**: 胜利后获得前代族长的智慧传承（特殊奖励）

#### 分身试炼奖励机制
- **胜利奖励（基于试炼倍率）**: 
- **金币奖励**: 当前金币总额的50%-100%
- **经验奖励**: 大量经验值（适应试炼倍率）
- **特殊物品**: 前代族长专属装备/技能
- **传承点数**: 用于解锁家族传承技能
- **失败奖励（鼓励挑战）**: 
- **金币补偿**: 当前金币总额的10%-30%
- **经验奖励**: 部分经验值
- **再次挑战**: 每日3次免费，额外挑战消耗积分

---

# 第三部分：技术实现与数据结构

### 📊 数据结构设计
```javascript
// 游戏区域数据结构 (适用于两种模式)
// 在[轮回试炼]模式中，它代表一个线性关卡。
// 在[主线叙事]模式中，它代表一个危机事件发生的地点。
const gameArea = {
    id: 1,                         // 区域/事件ID
    name: "祖地废墟",               // 区域/事件名称
    era: 1,                        // 所属时代 (1-9)
    requiredLevel: 1,              // 等级要求
    enemies: [],                   // 敌人列表
    boss: {},                      // Boss信息
    environment: {},               // 环境描述
    specialMechanic: null          // 特殊机制
}

// 试炼修正值数据结构 (仅用于[轮回试炼]模式)
const trialModifiers = {
    reincarnationCount: 0,         // 轮回次数
    enemyStrengthMultiplier: 1.0,  // 敌人强度倍率 (由"传承之重"系统计算)
    expMultiplier: 1.0,            // 经验倍率
    goldMultiplier: 1.0,           // 金币倍率
    dropRateBonus: 0.0             // 掉落率加成
}
```

### 🎮 试炼系统流程
```javascript
// 试炼系统管理类
class TrialSystem {
    // 开始轮回传承
    startReincarnation(playerData, reincarnationCount) {
        // 1. 触发传承仪式剧情
        this.triggerSuccessionCeremony();
        
        // 2. 重置玩家数据
        this.resetPlayerData(playerData);
        
        // 3. 选择传承装备
        this.selectInheritanceEquipment(2);
        
        // 4. 计算试炼修正值
        const modifiers = this.calculateTrialModifiers(reincarnationCount, playerData.totalTalentPoints);
        
        // 5. 开始线性试炼挑战
        this.startLinearTrialChallenge(modifiers);
    }
    
    // 计算试炼修正值
    calculateTrialModifiers(reincarnationCount, totalTalentPoints) {
        const baseTrialStrength = 0.05; // 基础试炼强度 +5%
        const talentStrength = totalTalentPoints * 0.006; // 天赋强度加成

        return {
            reincarnationCount: reincarnationCount,
            enemyStrengthMultiplier: 1.0 + baseTrialStrength + talentStrength,
            expMultiplier: 1.0 + (reincarnationCount * 0.5),
            goldMultiplier: 1.0 + (reincarnationCount * 0.3),
            dropRateBonus: reincarnationCount * 0.2
        };
    }
}
```

### 📈 平衡性考虑
- **轮回试炼**: 
  - **前期**: 难度提升温和，玩家能通过成长加速机制感受到明显的成长感和爽快感。
  - **中期**: 玩家进入优势期，轮回红利体验最大化，可以爽快地推进流程。
  - **后期**: 敌人强度随着玩家天赋的增强而显著提升，游戏回归策略性，确保了长期的挑战价值。
- **主线叙事**: 难度由当前时代的危机和`GlobalState`决定，更侧重于战略选择而非单纯的数值碾压。
- **奖励平衡**: 试炼期间装备品质和数量提升，补偿重置损失；高倍率金币和经验确保快速恢复实力。

---

**文档版本**: v2.0
**最后更新**: 2024年12月20日
**维护者**: CHX 