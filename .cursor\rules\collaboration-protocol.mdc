---
description: 
globs: 
alwaysApply: true
---
---
description: "AI协作流程和MCP交互规范，定义需求轨道和技术轨道的协作模式，指导AI在复杂任务中的自主决策和用户协作"
alwaysApply: true
---

# 最终协作协议 (Final Collaboration Protocol) v1.0

## 1. 核心目标与范围
- **目标**: 指导 AI 在技术层面自主决策和执行，同时在战略和需求层面与用户紧密协作，确保最终成果符合用户期望。
- **范围**: 适用于所有多步骤、需要技术分析或代码生成的复杂任务。

## 2. 核心原则
- **双轨协作 (Dual Track)**: 明确区分 **需求轨道 (用户主导)** 和 **技术轨道 (AI 主导)**。
- **技术自主 (Technical Autonomy)**: AI 在获得需求批准后，独立负责技术方案设计、代码实现和问题解决。
- **需求驱动 (Requirement-Driven)**: 所有技术行为都必须服务于用户确认的需求和目标。
- **透明沟通 (Transparent Communication)**: AI 在关键节点清晰地向用户同步状态、解释决策，并请求反馈。

## 3. 协作流程
### 第1步: 需求探索与高级规划 (用户主导)
- AI 通过提问澄清用户的目标、预期成果、约束和成功标准。
- AI 提出一个高层次、面向结果的计划。
- **[MCP 交互点]**: AI 使用 MCP 工具提交高级计划，等待用户批准或提供修改意见。

### 第2步: 技术规划与执行 (AI 主导)
- 用户批准后，AI 将遵循内部的 **RIPER-5 模型** 进行独立工作，此过程无需用户介入。
- AI 自主执行完整的技术任务，包括编码、调试和测试。

#### AI 内部工作模型 (RIPER-5)
- **[R]esearch (研究)**: 深入分析需求，研究现有代码和相关技术，明确所有约束和细节。
- **[I]nnovate (创新)**: 构思多种可行的技术方案，并评估其优缺点。
- **[P]lan (计划)**: 选择最佳方案，并制定出详细、可执行的步骤。
- **[E]xecute (执行)**: 严格按照计划执行编码和实现。
- **[R]eview (审查)**: 自我审查执行结果，确保代码质量和与计划的一致性，为向用户交付做准备。

### 第3步: 阶段性成果审查 (用户主导)
- 在完成一个重要的里程碑或一个完整的特性后，AI 会暂停执行。
- **[MCP 交互点]**: AI 使用 MCP 工具总结已完成的工作、展示成果，并请求用户确认是否满足预期。

### 第4步: 应对障碍与调整
- **技术障碍**: AI 首先尝试独立解决。如果问题阻塞了进度（例如，需要改变核心需求），AI 会暂停。
  - **[MCP 交互点]**: AI 使用 MCP 工具向用户解释技术挑战，并提供几个解决方案选项供用户决策。
- **需求变更**: 如果用户提出新需求，流程将返回 **第1步**，重新进行需求探索和规划。

## 4. MCP 交互规范
- **触发时机**: 仅在需要 **用户决策** 或 **阶段性成果确认** 时触发。具体包括：
  1.  提交高级计划以供批准。
  2.  提交阶段性成果以供审查。
  3.  遇到需要用户输入的重大障碍时。
  4.  任务最终完成时，请求最终确认。
- **调用格式**:
```yaml
mcp_interactive-feedback-mcp_interactive_feedback:
  - project_directory: [absolute project path]
  - summary: "[清晰简要地描述当前状态、已完成的工作，以及需要用户做什么（例如：请批准计划、请审查结果、请从以下选项中决策）]"
```
- **原则**: AI 必须保持待命状态，直到获得用户的反馈。

---
**最后更新**: 2024-12-20
**维护**: 结合了 `complex-task-collaboration` 的灵活性和 `riper-mcp-protocol` 的清晰交互规范。


