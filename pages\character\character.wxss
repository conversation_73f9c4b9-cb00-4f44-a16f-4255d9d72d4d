/* 角色资料卡片 */
.character-profile .card-content {
  padding: var(--spacing-lg);
}

.profile-main {
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-start;
}

.character-avatar {
  flex-shrink: 0;
}

.clan-emblem-large {
  font-size: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.character-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.character-name {
  font-size: var(--font-xxl);
  font-weight: 700;
  color: var(--primary-color);
}

.character-title {
  font-size: var(--font-md);
  color: var(--warning-color);
  font-weight: 500;
}

.clan-name {
  font-size: var(--font-lg);
  color: var(--text-primary);
  font-weight: 600;
}

.level-info {
  margin-top: var(--spacing-sm);
}

.level-text {
  display: block;
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--success-color);
  margin-bottom: var(--spacing-xs);
}

.exp-bar {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.exp-text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  text-align: center;
}

/* 角色属性网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--light-color);
  border-radius: var(--border-radius-md);
  text-align: center;
}

.stat-label {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-size: var(--font-xl);
  font-weight: 700;
}

/* 家族传承 */
.clan-heritage .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.heritage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.heritage-item:last-child {
  border-bottom: none;
}

.heritage-label {
  font-size: var(--font-md);
  color: var(--text-secondary);
}

.heritage-value {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
}

.heritage-emblem {
  font-size: var(--font-lg);
}

/* 称号系统 */
.equipped-titles .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.title-item {
  padding: var(--spacing-md);
  background: var(--light-color);
  border-radius: var(--border-radius-md);
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.title-item.equipped {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(52, 152, 219, 0.1));
  border-color: var(--primary-color);
}

.title-name {
  display: block;
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.title-effect {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.title-stats {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.title-stat {
  font-size: var(--font-xs);
  color: var(--success-color);
  background: rgba(39, 174, 96, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
}

/* 资源网格 */
.resource-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.resource-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--light-color);
  border-radius: var(--border-radius-md);
}

.resource-icon {
  font-size: 48rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}

.resource-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.resource-name {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin-bottom: 4rpx;
}

.resource-amount {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
}

/* 游戏统计 */
.stats-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1rpx solid var(--border-color);
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-row .stat-label {
  font-size: var(--font-md);
  color: var(--text-secondary);
}

.stat-row .stat-value {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--primary-color);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .profile-main {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .resource-grid {
    grid-template-columns: 1fr;
  }
  
  .clan-emblem-large {
    font-size: 100rpx;
    width: 140rpx;
    height: 140rpx;
  }
} 