# 设计文稿目录与格式优化详细建议

## 1. 目录结构优化
- **分层分主题**：
  - `00_总览与规范/`：项目总览、设计规范、GDD、团队协作说明等。
  - `01_世界观与剧情/`：世界观总览、家族个性化、各时代剧情、主线与支线剧情。
  - `02_核心机制/`：核心循环、随机事件、轮回历练等全局机制。
  - `03_系统设计/`：战斗、装备、技能、掉落、家族天赋树等具体系统，旧设计归入`旧_系统设计/`。
  - `04_地图与探索/`：地图系统、各时代地图、探索机制、区域设定。
  - `05_数据与配置/`：参数系统、核心数据结构、配置表说明等。
  - `历史归档/`：已废弃、历史版本、重大变更前的文档。
  - `_backup/`：临时备份、未整理文档，定期清理。
- **建议每个大类下有README.md或目录.md**，简要说明本目录内容、结构和维护人。
- **示例结构**：
  ```
  设计文稿/
  ├── 00_总览与规范/
  │   ├── 设计总览.md
  │   ├── 游戏核心设计_GDD.md
  │   └── 设计规范.md
  ├── 01_世界观与剧情/
  │   ├── 世界观总览.md
  │   ├── 家族个性化系统.md
  │   ├── 第一时代_剧情.md
  │   └── ...
  ├── 02_核心机制/
  │   ├── 核心循环与时代总览.md
  │   ├── 随机事件系统.md
  │   └── ...
  ├── 03_系统设计/
  │   ├── 战斗系统设计.md
  │   ├── 掉落系统设计.md
  │   ├── 旧_系统设计/
  │   │   └── 副职业系统.md
  │   └── ...
  ├── 04_地图与探索/
  │   ├── 地图系统总览.md
  │   └── ...
  ├── 05_数据与配置/
  │   ├── 参数系统.md
  │   └── ...
  ├── 历史归档/
  └── _backup/
  ```

## 2. 文件命名规范
- **统一用半角英文、下划线或中划线**，避免中文符号、空格和特殊字符。
- **时代剧情**：`第一时代_剧情.md`、`第二时代_剧情.md`，便于排序和检索。
- **系统设计**：`战斗系统设计.md`、`装备系统设计.md`，突出功能。
- **机制/配置**：`轮回历练系统.md`、`参数系统.md`。
- **所有文件用.md结尾**，便于批量处理。
- **注意事项**：
  - 文件重命名后需同步修正文档内引用路径。
  - 目录下如有多个相关文档，建议编号排序，如`01_世界观总览.md`、`02_家族个性化系统.md`。

## 3. Markdown格式规范
- **标题层级统一**：一级`#`，二级`##`，三级`###`，避免跳级。
- **目录/索引**：建议每个大类目录下有`README.md`或`目录.md`，用无序列表或表格列出子文件及简要说明。
- **代码/数据结构**：统一用代码块（```），注明语言类型（如js、json、sql等），并加注释说明字段含义。
- **表格**：用标准Markdown表格格式，避免混用空格或制表符。
- **文档头部元信息**：建议每个文档开头加如下信息，便于管理和追溯：
  ```markdown
  ---
  文档类型: 系统设计
  版本: 1.0
  最后更新: 2024-06-20
  作者: AI
  ---
  ```
- **关联文档引用**：统一用标准Markdown链接，相对路径，示例：`[世界观总览](../01_世界观与剧情/世界观总览.md)`。
- **图片、引用、链接**：统一格式，便于后续批量处理。

## 4. 内容同步与维护建议
- **文件重命名/移动后，需同步修正文档内所有引用路径**，可用IDE批量替换或脚本辅助。
- **内容重复/交叉引用**：建议主文档集中描述，其他文档引用，避免多头维护。
- **历史文档管理**：
  - 旧文档、废弃内容统一归入`历史归档/`或`旧_系统设计/`，并在新文档中注明替代关系。
  - 重大变更前建议备份原文档，便于追溯。
- **定期清理_backup/**：避免冗余和混乱。

## 5. 其他建议
- **团队协作**：建议每个目录指定维护人，定期review和合并文档。
- **自动化工具**：可用脚本自动校验引用、生成目录索引、批量重命名。
- **文档变更记录**：建议在每次大改动后，在目录下维护`CHANGELOG.md`，记录变更历史。
- **内容合理性优化**：结构优化后，建议逐步梳理内容一致性、去重、补充缺失说明。

---
本建议详细覆盖了目录结构、命名、格式、引用、维护等方面，便于团队理解和落地执行。如需自动化脚本或具体操作指引，可进一步补充。 