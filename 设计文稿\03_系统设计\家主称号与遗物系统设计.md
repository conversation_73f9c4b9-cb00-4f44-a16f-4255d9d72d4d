# 月球RPG家主称号与遗物系统设计

> 本文件以README为准，后续内容如有冲突以README为最终解释。
> 
> 【玩法模式】本游戏采用并发危机驱动模式，玩家在指挥室中心自由选择危机应对，所有危机并行发展，选择影响后续轮回。
> 
> 【轮回机制】每次轮回，等级、装备、技能、背包等全部重置，仅家族天赋树成长永久保留。
> 
> 【数值待定】【经济模型后补】

## 📁 关联文档
- **[地图系统设计](./地图系统/地图系统设计.md)** - 轮回时的称号评定流程和遗物产生机制
- **[轮回历练系统设计](./剧情系统/轮回历练系统设计.md)** - 随机事件选择标签对称号生成的影响
- **[世界观](./剧情系统/世界观.md)** - 称号命名的世界观基础和历史背景
- **[材料系统设计](./材料系统设计.md)** - 传承星火材料转换和遗物使用机制
- **[家族天赋树系统设计](./家族天赋树系统设计.md)** - 家主威望天赋树对称号系统的影响
- **[技能系统设计](./技能系统设计.md)** - 战斗数据对称号评定的影响

## 📝 文档内容说明
本文档定义了轮回系统的历史记录功能，通过称号系统记录每任家主的特色和成就，通过遗物系统提供装备传承能力的扩展。设计了称号的唯一性机制、祖祠展示系统、遗物的获取和使用机制等。

## 系统概述

家主称号与遗物系统是游戏的核心传承机制之一，记录每任家主的辉煌事迹和最终结局。通过数据统计和关键抉择，为每任家主生成独特的称号，并在战败时留下承载故事的遗物。

## 家主称号系统

### 称号生成机制

称号基于家主在位期间的**战斗数据**和**关键抉择**动态生成，体现每任家主的独特经历和性格特征。

#### 基础数据统计
系统持续记录以下数据：
- **装备制造次数**：记录制作装备的总数量
- **使用物品次数**：记录消耗道具的总数量  
- **击杀普通敌人数量**：记录击败普通怪物总数
- **击杀BOSS数量**：记录击败Boss怪物总数
- **技能触发次数**：记录各类技能的使用频率
- **获得材料数量**：记录收集材料的总数量
- **战败次数**：记录家主失败的次数
- **治疗量统计**：记录总治疗量和占比
- **副职业活动**：记录副职业相关的制作和活动

#### 称号判定条件

##### 单一数据触发类
- **装备制造次数 > 100** → "巧手工匠"
- **使用物品次数 > 500** → "道具大师"
- **击杀普通敌人 > 10000** → "杀戮之王"
- **击杀BOSS数量 > 50** → "屠龙英雄"
- **技能触发次数 > 1000** → "战技宗师"
- **获得材料数量 > 5000** → "收集大家"
- **战败次数 > 20** → "不屈战士"
- **零战败记录** → "无敌传说"

##### 复合条件触发类
- **治疗量占比>50% + 医师职业** → "仁心圣手"
- **制造装备>50件 + 铁匠职业** → "神工鬼斧"
- **材料获取>3000 + 猎人职业** → "狩猎宗师"
- **金币收入极高 + 商人职业** → "富甲一方"
- **毒杀敌人>500 + 毒师职业** → "毒王"
- **零战败 + 击杀BOSS>10** → "无敌战神"
- **拯救NPC>20次** → "救世英雄"
- **击杀稀有怪物>30** → "传奇猎手"

##### 宗师技艺触发类 (新增)
- **铁匠职业达到100级 + 制作传说套装** → "铁匠宗师"
- **医师职业达到100级 + 制作仙丹妙药** → "医师宗师"
- **猎人职业达到100级 + 制作神兽陷阱** → "猎人宗师"
- **商人职业达到100级 + 制作财富符文** → "商人宗师"
- **毒师职业达到100级 + 制作九幽毒经** → "毒师宗师"

##### 关键抉择触发类
- **牺牲NPC获得优势** → "冷血统治者"
- **多次拯救濒危NPC** → "守护天使"
- **偏好使用火焰技能** → "烈焰之主"
- **偏好使用冰霜技能** → "寒冰君主"
- **频繁使用毒系技能** → "暗影毒师"
- **长期单挑强敌** → "独行侠客"
- **经常团队配合** → "协调指挥"

##### 技能熟练度触发类 (新增)
- **同时拥有3个大师级技能** → "技艺精湛"
- **同时拥有5个专家级技能** → "博学多才"
- **任一技能达到宗师级** → "技能宗师"
- **所有技能达到专家级** → "全能专家"

##### 随机事件触发类 (40%的称号)
基于家主在随机事件中的选择累积标签，当特定标签达到阈值时触发对应称号：

**数据标签类** (需要积累5个以上相同标签)：
- **"勇猛者"标签 x5** → "勇武之主"
- **"谨慎者"标签 x5** → "深谋长者"
- **"血战者"标签 x5** → "血煞武君"
- **"观察者"标签 x5** → "明察真人"
- **"稳健者"标签 x5** → "稳山居士"
- **"挑战者"标签 x5** → "逆天武尊"
- **"攻击大师"标签 x5** → "攻击大师"
- **"防御大师"标签 x5** → "防御大师"
- **"技能大师"标签 x5** → "技能大师"
- **"破甲专家"标签 x5** → "破甲专家"
- **"捕捉专家"标签 x5** → "捕捉专家"
- **"借力大师"标签 x5** → "借力大师"

**选择偏向类** (需要积累7个以上相同方向标签)：
- **攻击系标签总数>7** → "破军星君"
- **防御系标签总数>7** → "镇山真人"
- **平衡系标签总数>7** → "调和大师"
- **风险系标签总数>7** → "剑胆琴心"
- **稳重系标签总数>7** → "厚德长者"

**特殊组合类** (需要特定标签组合)：
- **"古神传人" + "挑战者" + "智者"** → "古道承天"
- **"血脉融合者" + "狂暴血脉" + "混乱适应者"** → "血魔化身"
- **"净化者" + "试炼者" + "传统主义者"** → "正道明君"
- **"无招大师" + "天人合一者" + "谦逊者"** → "返真武圣"
- **"挑战者" + 击败超强Boss** → "宇宙征服者"

**领导风格类** (需要积累相应标签)：
- **"仁慈统治者"标签 x7** → "仁慈统治者"
- **"铁血统帅"标签 x7** → "铁血统帅"
- **"隐秘领袖"标签 x7** → "隐秘领袖"

**性格特质类** (需要积累相应标签)：
- **"冒险家"标签 x5** → "冒险家"
- **"稳健发展者"标签 x5** → "稳健发展者"
- **"技术革新者"标签 x5** → "技术革新者"

**价值观类** (需要积累相应标签)：
- **"孤狼"标签 x5** → "孤狼"
- **"团队领袖"标签 x5** → "团队领袖"

**时代特征类** (需要在特定时代获得相应标签)：
- **第一时代："自立者"+"英灵加护拒绝"** → "自立族主"  
- **第二时代："平衡武者"+"学习者"** → "血脉调和"
- **第三时代："机会主义者"+"精准打击者"** → "乱世枭雄"
- **第四时代："引气者"+"护体者"** → "引气真人"
- **第五时代："净化者"+"传统主义者"** → "守道宗师"
- **第六时代："重生者"+"顽抗者"** → "血脉重塑"
- **第七时代："极致攻击者"+"屠妖者"** → "斩妖真君"
- **第八时代："古神传人"+"现实坚持者"** → "护界尊者"
- **第九时代："无招大师"+"谦逊者"** → "飞升真仙"

#### 称号获得与唯一性机制
```
【称号获得流程】
1. 家主轮回时触发称号评定
2. 系统检查满足条件的所有称号
3. 过滤掉已被历任家主获得的称号（唯一性限制）
4. 从剩余可用称号中按优先级选择
5. 如果无可用称号，则该家主无称号
6. 获得称号后，该称号永久"封印"，不可重复获得

【称号选择优先级】
1. 传奇级称号（复合条件+高难度成就）
2. 专精级称号（单一数据极值）
3. 特色级称号（关键抉择特征）
4. 基础级称号（常规数据达成）

【唯一性价值】
- 增强称号珍贵性：后期获得称号难度递增
- 鼓励多样化策略：避免重复相同玩法
- 历史纪念意义：每个称号代表家族独特历史
```

### 祖祠陈列系统

#### 简化展示设计
- **祖祠家主列表**：按轮回顺序列出历任家主
- **名称展示格式**：[家主姓名] - [称号]（如有）
- **无称号显示**：[家主姓名] - 无称号
- **称号唯一性**：每个称号在家族历史中只能被一任家主获得

#### 生涯回顾功能
点击任意家主称号，播放该家主的**文字版生涯高光时刻**：

##### 回顾内容结构
```
【第X任家主：XXX】
称号：[家主称号]
在位期间：X年X月至X年X月

=== 巅峰时刻 ===
• 最辉煌战绩：[如击败特定Boss、达成无伤记录等]
• 关键抉择：[如救助重要NPC、做出重大牺牲等]  
• 专精领域：[如装备制造、战斗技巧等]

=== 数据统计 ===
• 击杀敌人：X只普通怪物，X只Boss
• 制作装备：X件装备，成功率X%
• 材料收集：X种材料，总量X个
• 副职业：X级[职业名]

=== 最终结局 ===
[战败情况描述和遗物生成故事]
```

## 遗物系统

### 遗物生成机制

遗物是承载家主故事的特殊掉落物品，通过以下方式生成：

#### 生成条件
1. **战败记录生成**：前任家主战败时，系统在当前关卡生成遗物掉落记录
2. **跨代传承掉落**：后续家主到达同一位置击杀怪物时，有概率获得前任家主的遗物
3. **活动产出**：在某个地图频繁活动时，低概率产生遗物（隐藏掉落，不提示玩家）
4. **数量管理**：系统维护遗物列表，每次获得后对应遗物数量-1

#### 遗物掉落机制详解

**战败记录系统**：
- **记录生成**：家主战败时，在该关卡创建遗物掉落记录
- **遗物池**：每个关卡维护一个遗物列表，包含前任家主的遗物
- **跨代掉落**：新家主在该关卡击杀怪物时，有0.1%概率从遗物池中获得遗物
- **数量递减**：获得遗物后，对应记录数量-1，为0时从池中移除

#### 实现示例
```
关卡数据结构示例：
区域5-3遗物池：
- "破损的族长印章" x1 (第3任家主战败遗物)
- "沾血的药瓶" x2 (第5任家主战败遗物)  
- "锈迹斑斑的铁锤" x1 (第7任家主战败遗物)

第8任家主在区域5-3击杀怪物：
- 0.1%概率从遗物池随机获得1个遗物
- 获得"沾血的药瓶"后，其数量变为1
- 当数量为0时，该遗物从池中移除
```

**活动频繁度判定**：
- **活动统计**：记录玩家在每个关卡的活动时间和击杀数量
- **频繁阈值**：在单个关卡累计活动时间>30分钟，或击杀怪物>200只
- **掉落概率**：达到阈值后，每次击杀怪物有0.1%概率掉落遗物
- **冷却机制**：单个关卡生成遗物后，24小时内不再计算频繁度
- **隐藏设计**：不向玩家显示遗物掉落提示，增加发现的惊喜感

#### 遗物类型分类
1. **身体部件**：战甲碎片、武器残骸、护身符等
2. **随身物品**：家族印章、笔记本、药瓶等
3. **装备碎片**：破损装备、附魔石、符文等

#### 战败故事生成
每个遗物附带**50字内的战败故事**，根据家主的称号、副职业和战败情况动态生成：

##### 故事模板示例
- **巧手工匠** + **战败情况**：
  "破损的锻造锤：他在最后时刻仍在修复战友的铠甲，直到怪物冲破工坊大门。"

- **仁心圣手** + **战败情况**：  
  "沾血的医书：她耗尽最后一滴法力救治重伤战士，终因体力不支倒下。"

- **不屈战士** + **战败情况**：
  "裂纹累累的盾牌：纵然伤痕满身，他依然挡在村民面前，直到生命的最后一刻。"

- **冷血统治者** + **战败情况**：
  "焦黑的指令书：他死守烽火台指引难民撤退，却被自己抛弃的部下背叛。"

### 传承星火系统

#### 星火提取机制
- **使用方式**：遗物作为一次性道具，使用后立即销毁
- **提取结果**：100%成功获得1个"传承星火"
- **简化设计**：遗物掉落已经极其稀有，无需额外概率限制

#### 星火使用效果
- **主要功能**：为祖祠传承装备增加使用次数上限
- **使用方式**：在祖祠中对传承装备使用星火
- **效果**：
  - 使用次数上限<10：每个星火+1次上限
  - 使用次数上限≥10：每个星火减少10分钟冷却时间

#### 星火限制
- **单装备上限**：每件传承装备最多接受20个星火强化
- **稀有性保证**：极低的提取概率确保星火的珍贵性
- **策略选择**：玩家需要在保留遗物（情感价值）和提取星火（实用价值）间抉择

### 遗物存储与管理

#### 存储机制
- **背包占用**：遗物占用普通背包空间
- **祖祠存放**：可存放在祖祠中，但不单独设置展示区域
- **数量叠加**：相同类型遗物可叠加存储，节省背包空间

#### 管理功能
- **遗物列表**：按生成时间排序显示所有遗物
- **故事查看**：点击遗物可阅读附带的故事描述
- **直接使用**：点击使用按钮立即提取1个传承星火
- **确认提示**：使用前提醒玩家遗物将会消失，确认后获得星火

## 系统整合与平衡

### 情感价值设计
- **故事连贯性**：称号、遗物、战败故事形成完整的家主生涯叙述
- **情感共鸣**：通过具体的故事细节增强玩家对家主的情感投入
- **跨代传承**：在前任家主战败的地方获得遗物，增强家族历史感
- **传承意义**：每件遗物都承载着前任家主的精神遗产和战败记忆

### 游戏性平衡
- **稀有性控制**：极低的遗物掉落概率确保传承星火稀有性
- **收集决策**：使用遗物获得实用星火 vs 保留纪念价值的选择
- **收集价值**：遗物承载故事，既有实用价值又有收藏价值
- **长期目标**：为长期游玩提供收集和传承的动力
- **隐藏惊喜**：活动频繁度掉落不提示，增加发现乐趣

### 扩展性预留
- **社交分享**：为后期分享家主故事功能预留接口
- **成就系统**：可与成就系统联动，解锁特殊称号
- **剧情整合**：为主线剧情中的重要抉择预留称号生成点 