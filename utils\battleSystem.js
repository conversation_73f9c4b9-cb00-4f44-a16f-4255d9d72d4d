/**
 * 战斗系统工具
 * 负责战斗逻辑计算、伤害计算、敌人生成等
 */

// 敌人数据模板
const ENEMY_TEMPLATES = {
  slime: {
    name: '史莱姆',
    maxHp: 50,
    attack: 10,
    defense: 5,
    gold: 10,
    exp: 15,
    icon: '🟢'
  },
  goblin: {
    name: '哥布林',
    maxHp: 80,
    attack: 15,
    defense: 8,
    gold: 15,
    exp: 25,
    icon: '👺'
  },
  skeleton: {
    name: '骷髅兵',
    maxHp: 120,
    attack: 20,
    defense: 12,
    gold: 25,
    exp: 35,
    icon: '💀'
  },
  orc: {
    name: '兽人战士',
    maxHp: 200,
    attack: 30,
    defense: 18,
    gold: 40,
    exp: 60,
    icon: '👹'
  },
  dragon: {
    name: '幼龙',
    maxHp: 500,
    attack: 60,
    defense: 35,
    gold: 150,
    exp: 200,
    icon: '🐉'
  }
}

// 区域配置
const AREA_CONFIG = {
  1: {
    name: '新手村',
    description: '适合初学者的安全区域',
    enemies: ['slime', 'goblin'],
    minLevel: 1,
    maxLevel: 5
  },
  2: {
    name: '森林边缘',
    description: '茂密森林的外围区域',
    enemies: ['goblin', 'skeleton'],
    minLevel: 5,
    maxLevel: 15
  },
  3: {
    name: '暗影森林',
    description: '危险的森林深处',
    enemies: ['skeleton', 'orc'],
    minLevel: 15,
    maxLevel: 30
  },
  4: {
    name: '龙之峡谷',
    description: '传说中龙族栖息的峡谷',
    enemies: ['orc', 'dragon'],
    minLevel: 30,
    maxLevel: 50
  }
}

/**
 * 根据玩家等级生成敌人
 * @param {number} playerLevel - 玩家等级
 * @param {number} areaId - 区域ID，默认根据等级计算
 * @returns {Object} 敌人对象
 */
function generateEnemy(playerLevel, areaId = null) {
  try {
    // 根据等级确定区域
    if (!areaId) {
      areaId = getAreaByLevel(playerLevel)
    }
    
    const area = AREA_CONFIG[areaId]
    if (!area) {
      console.error('无效的区域ID:', areaId)
      areaId = 1
    }
    
    // 随机选择敌人类型
    const enemyTypes = AREA_CONFIG[areaId].enemies
    const randomType = enemyTypes[Math.floor(Math.random() * enemyTypes.length)]
    const template = ENEMY_TEMPLATES[randomType]
    
    // 根据玩家等级调整敌人属性
    const levelMultiplier = 1 + (playerLevel - 1) * 0.1
    
    const enemy = {
      ...template,
      currentHp: Math.floor(template.maxHp * levelMultiplier),
      maxHp: Math.floor(template.maxHp * levelMultiplier),
      attack: Math.floor(template.attack * levelMultiplier),
      defense: Math.floor(template.defense * levelMultiplier),
      gold: Math.floor(template.gold * levelMultiplier),
      exp: Math.floor(template.exp * levelMultiplier),
      level: playerLevel + Math.floor(Math.random() * 3) - 1 // ±1级别浮动
    }
    
    return enemy
  } catch (error) {
    console.error('生成敌人失败:', error)
    // 返回默认史莱姆
    return {
      ...ENEMY_TEMPLATES.slime,
      currentHp: ENEMY_TEMPLATES.slime.maxHp,
      level: playerLevel
    }
  }
}

/**
 * 根据等级获取对应区域
 * @param {number} level - 玩家等级
 * @returns {number} 区域ID
 */
function getAreaByLevel(level) {
  if (level < 5) return 1
  if (level < 15) return 2
  if (level < 30) return 3
  return 4
}

/**
 * 计算战斗伤害
 * @param {Object} attacker - 攻击者属性
 * @param {Object} defender - 防御者属性
 * @returns {Object} 战斗结果
 */
function calculateDamage(attacker, defender) {
  try {
    const baseAttack = attacker.attack || 10
    const defense = defender.defense || 0
    const critRate = attacker.critRate || 5
    
    // 基础伤害计算
    let damage = Math.max(1, baseAttack - defense)
    
    // 随机浮动 ±20%
    const randomFactor = 0.8 + Math.random() * 0.4
    damage = Math.floor(damage * randomFactor)
    
    // 暴击判定
    const isCritical = Math.random() * 100 < critRate
    if (isCritical) {
      damage = Math.floor(damage * 2)
    }
    
    return {
      damage: damage,
      isCritical: isCritical,
      actualDamage: Math.min(damage, defender.currentHp || defender.maxHp)
    }
  } catch (error) {
    console.error('计算伤害失败:', error)
    return {
      damage: 1,
      isCritical: false,
      actualDamage: 1
    }
  }
}

/**
 * 执行一轮战斗
 * @param {Object} player - 玩家数据
 * @param {Object} enemy - 敌人数据
 * @returns {Object} 战斗结果
 */
function executeBattle(player, enemy) {
  try {
    // 玩家攻击敌人
    const playerAttackResult = calculateDamage(player, enemy)
    const newEnemyHp = Math.max(0, enemy.currentHp - playerAttackResult.actualDamage)
    
    const updatedEnemy = {
      ...enemy,
      currentHp: newEnemyHp
    }
    
    const enemyDefeated = newEnemyHp <= 0
    let updatedPlayer = { ...player }
    let enemyAttackResult = null
    
    // 如果敌人存活，敌人反击
    if (!enemyDefeated) {
      enemyAttackResult = calculateDamage(enemy, player)
      const newPlayerHp = Math.max(0, player.currentHp - enemyAttackResult.actualDamage)
      
      updatedPlayer = {
        ...player,
        currentHp: newPlayerHp
      }
    } else {
      // 敌人被击败，玩家获得奖励
      updatedPlayer = {
        ...player,
        gold: player.gold + enemy.gold,
        experience: player.experience + enemy.exp
      }
      
      // 检查升级
      const levelUpResult = checkLevelUp(updatedPlayer)
      if (levelUpResult.leveledUp) {
        updatedPlayer = levelUpResult.playerData
      }
    }
    
    return {
      player: updatedPlayer,
      enemy: updatedEnemy,
      playerAttack: playerAttackResult,
      enemyAttack: enemyAttackResult,
      enemyDefeated: enemyDefeated,
      playerDefeated: updatedPlayer.currentHp <= 0,
      leveledUp: false // 将在checkLevelUp中设置
    }
  } catch (error) {
    console.error('执行战斗失败:', error)
    return {
      player: player,
      enemy: enemy,
      playerAttack: { damage: 0, isCritical: false, actualDamage: 0 },
      enemyAttack: null,
      enemyDefeated: false,
      playerDefeated: false,
      leveledUp: false
    }
  }
}

/**
 * 检查玩家是否升级
 * @param {Object} playerData - 玩家数据
 * @returns {Object} 升级结果
 */
function checkLevelUp(playerData) {
  try {
    let updatedPlayer = { ...playerData }
    let leveledUp = false
    
    while (updatedPlayer.experience >= updatedPlayer.nextLevelExp) {
      // 升级
      updatedPlayer.level += 1
      updatedPlayer.experience -= updatedPlayer.nextLevelExp
      updatedPlayer.nextLevelExp = Math.floor(updatedPlayer.nextLevelExp * 1.15)
      
      // 属性提升
      updatedPlayer.maxHp += 10
      updatedPlayer.attack += 2
      updatedPlayer.defense += 1
      updatedPlayer.currentHp = updatedPlayer.maxHp // 升级回满血
      
      leveledUp = true
    }
    
    return {
      playerData: updatedPlayer,
      leveledUp: leveledUp
    }
  } catch (error) {
    console.error('检查升级失败:', error)
    return {
      playerData: playerData,
      leveledUp: false
    }
  }
}

/**
 * 获取区域信息
 * @param {number} areaId - 区域ID
 * @returns {Object} 区域信息
 */
function getAreaInfo(areaId) {
  return AREA_CONFIG[areaId] || AREA_CONFIG[1]
}

/**
 * 计算每秒伤害(DPS)
 * @param {Object} playerData - 玩家数据
 * @returns {number} DPS值
 */
function calculateDPS(playerData) {
  try {
    const attack = playerData.attack || 10
    const attackSpeed = playerData.attackSpeed || 1.0
    const critRate = (playerData.critRate || 5) / 100
    
    // 基础DPS = 攻击力 × 攻击速度 × (1 + 暴击率)
    const baseDPS = attack * attackSpeed * (1 + critRate)
    
    return Math.floor(baseDPS)
  } catch (error) {
    console.error('计算DPS失败:', error)
    return 10
  }
}

module.exports = {
  generateEnemy,
  calculateDamage,
  executeBattle,
  checkLevelUp,
  getAreaInfo,
  getAreaByLevel,
  calculateDPS,
  ENEMY_TEMPLATES,
  AREA_CONFIG
} 